import { createContext, useContext, useState, useCallback } from "react";
import { useApolloClient } from "@apollo/client";

const FavoritesContext = createContext({
  favoriteIds: new Set(),
  toggleFavorite: () => {},
  setFavoriteIds: () => {},
  isFavorited: () => false,
  refreshFavorites: () => {},
});

export const useFavorites = () => useContext(FavoritesContext);

export const FavoritesProvider = ({ children }) => {
  const [favoriteIds, setFavoriteIds] = useState(new Set());
  const apolloClient = useApolloClient();

  const isFavorited = useCallback(
    (itemId) => {
      return favoriteIds.has(parseInt(itemId));
    },
    [favoriteIds]
  );

  const toggleFavorite = useCallback(
    (itemId, newStatus) => {
      const id = parseInt(itemId);
      setFavoriteIds((prev) => {
        const newSet = new Set(prev);
        if (newStatus) {
          newSet.add(id);
        } else {
          newSet.delete(id);
        }
        return newSet;
      });

      apolloClient.refetchQueries({
        include: ["HomeScreen", "GetSections"],
      });
    },
    [apolloClient]
  );

  const setAllFavoriteIds = useCallback((ids) => {
    setFavoriteIds(new Set(ids.map((id) => parseInt(id))));
  }, []);

  const refreshFavorites = useCallback(async () => {
    await apolloClient.refetchQueries({
      include: ["HomeScreen", "GetSections", "GetFavorites"],
    });
  }, [apolloClient]);

  const value = {
    favoriteIds,
    toggleFavorite,
    setFavoriteIds: setAllFavoriteIds,
    isFavorited,
    refreshFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
