import { useContext } from "react";
import { StyleSheet, ScrollView, View } from "react-native";
import { useNavigation } from "@react-navigation/native";

import IconButton from "../../components/IconButton";
import { UserContext } from "../../context/userContext";
const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 10,
    paddingVertical: 20,
    width: "100%",
  },
  iconWrapper: {
    width: "25%", // Assuming 4 icons per row, and 4% total space between icons
    alignItems: "center",
    marginBottom: 20,
  },
});

const AllVendorsScreen = () => {
  const navigation = useNavigation();
  const { user } = useContext(UserContext);

  const { suppliers } = user;
  const orderedSuppliers = [...suppliers].sort((a, b) => {
    if (a.logo && !b.logo) {
      return -1;
    }
    if (b.logo && !a.logo) {
      return 1;
    }

    return 0;
  });
  return (
    <ScrollView contentContainerStyle={styles.container} horizontal={false}>
      {orderedSuppliers.map((supplier) => (
        <View key={supplier.id} style={styles.iconWrapper}>
          <IconButton
            key={supplier.id}
            label={supplier.name}
            onPress={() => navigation.navigate("BrowseSupplier", { supplier })}
            uri={supplier.logo || "https://via.placeholder.com/100"}
            minimum={supplier.minimum}
          />
        </View>
      ))}
    </ScrollView>
  );
};

export default AllVendorsScreen;
