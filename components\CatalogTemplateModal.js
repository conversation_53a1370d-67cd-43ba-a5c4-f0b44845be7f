import { useTheme } from "@react-navigation/native";
import { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import CheckBox from "expo-checkbox";
import PropTypes from "prop-types";
import Button from "./Button";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTranslation } from "react-i18next";

const createStyles = (theme) => {
  const screenHeight = Dimensions.get("window").height;
  const modalHeight = Math.min(screenHeight * 0.6, screenHeight - 400); // 80% of screen or screen minus 100px, whichever is smaller

  const styles = StyleSheet.create({
    checkboxContainer: {
      alignItems: "center",
      flexDirection: "row",
      flex: 1,
    },
    checkboxLabel: {
      color: theme.appColors.text,
      fontSize: 16,
      marginLeft: 8,
    },
    container: {
      backgroundColor: theme.appColors.surface,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: modalHeight,
      maxHeight: modalHeight,
      minHeight: modalHeight,
    },
    content: {
      flex: 1,
      overflow: "hidden",
      paddingHorizontal: 20,
    },
    emptyState: {
      alignItems: "center",
      justifyContent: "center",
      maxHeight: 300,
      minHeight: 300,
      paddingVertical: 40,
    },
    emptyStateText: {
      color: theme.colors.text,
      fontSize: 16,
      opacity: 0.7,
      textAlign: "center",
    },
    footer: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderTopColor: theme.appColors.disabled,
      borderTopWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingVertical: 15,
    },
    header: {
      alignItems: "center",
      borderBottomColor: theme.colors.border,
      borderBottomWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingVertical: 15,
    },
    selectedTemplate: {
      backgroundColor: theme.appColors.backdropNeutral,
      borderColor: theme.appColors.primary,
      borderWidth: 2,
    },
    shareButton: {
      backgroundColor: theme.appColors.primary,
      maxWidth: 200,
      width: "100%",
    },
    templateCheckmark: {
      position: "absolute",
      right: 15,
      top: 15,
    },
    templateDescription: {
      color: theme.colors.text,
      fontSize: 14,
      opacity: 0.7,
    },
    templateItem: {
      backgroundColor: theme.appColors.backdropNeutral,
      borderColor: theme.colors.border,
      borderRadius: 8,
      borderWidth: 1,
      marginVertical: 5,
      paddingHorizontal: 15,
      paddingVertical: 15,
      position: "relative",
    },
    templateName: {
      color: theme.appColors.text,
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 5,
      paddingRight: 30,
    },
  });

  return styles;
};
const CatalogTemplateModal = ({
  visible,
  onClose,
  templates,
  loading,
  onShareCatalog,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();
  const { t } = useTranslation();

  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showPrice, setShowPrice] = useState(true);
  const [sharing, setSharing] = useState(false);

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    // Update show price based on template config
    if (template?.config?.pricing_enabled !== undefined) {
      setShowPrice(template.config.pricing_enabled);
    }
  };

  const handleShare = async () => {
    if (!selectedTemplate) {
      Alert.alert("Error", t("catalog.select_template_first"));
      return;
    }

    setSharing(true);
    try {
      await onShareCatalog(selectedTemplate, showPrice);
    } catch (error) {
      Alert.alert("Error", t("catalog.failed_to_generate"));
      console.error("Error sharing catalog:", error);
    } finally {
      setSharing(false);
    }
  };

  const renderTemplateItem = ({ item }) => {
    const isSelected = selectedTemplate?.id === item.id;
    const config = item.config || {};

    return (
      <TouchableOpacity
        style={[styles.templateItem, isSelected && styles.selectedTemplate]}
        onPress={() => handleTemplateSelect(item)}
      >
        <Text style={[styles.templateName, globalStyles.text]}>
          Title - {config.title}
        </Text>
        <Text style={[styles.templateDescription, globalStyles.text]}>
          {config.subtitle && `Subtitle - ${config.subtitle}`}
        </Text>
        <Text style={[styles.templateDescription, globalStyles.text]}>
          Created: {new Date(item.created_at).toLocaleDateString()}
        </Text>
        {isSelected && (
          <Ionicons
            name="checkmark-outline"
            size={24}
            color={theme.appColors.primary}
            style={styles.templateCheckmark}
          />
        )}
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ScrollView
          contentContainerStyle={styles.emptyState}
          showsVerticalScrollIndicator={false}
        >
          <ActivityIndicator size="large" color={theme.appColors.primary} />
          <Text style={[styles.emptyStateText, globalStyles.text]}>
            {t("catalog.loading_templates")}
          </Text>
        </ScrollView>
      );
    }

    if (!templates || templates?.length === 0) {
      return (
        <ScrollView
          contentContainerStyle={styles.emptyState}
          showsVerticalScrollIndicator={false}
        >
          <Ionicons
            name="document-outline"
            size={60}
            color={theme.colors.text}
            style={{ opacity: 0.3, marginBottom: 15 }}
          />
          <Text style={[styles.emptyStateText, globalStyles.text]}>
            No catalog templates found.{"\n"}
            Create templates from the dashboard to share catalogs.
          </Text>
        </ScrollView>
      );
    }

    return (
      <FlatList
        data={templates}
        renderItem={renderTemplateItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[globalStyles.headlineLarge, globalStyles.text]}>
          {t("catalog.select_template")}
        </Text>
        <TouchableOpacity onPress={onClose}>
          <Ionicons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>{renderContent()}</View>

      <View style={styles.footer}>
        <View style={styles.checkboxContainer}>
          <CheckBox
            value={showPrice}
            onValueChange={setShowPrice}
            color={showPrice ? theme.appColors.primary : undefined}
          />
          <Text style={[styles.checkboxLabel, globalStyles.text]}>
            Show Prices
          </Text>
        </View>

        <Button
          variant="primary"
          size="md"
          onPress={handleShare}
          disabled={!selectedTemplate || sharing}
          loading={sharing}
          style={styles.shareButton}
        >
          <Text style={{ color: theme.appColors.textNeutral }}>
            {sharing ? "Generating..." : "Share"}
          </Text>
        </Button>
      </View>
    </View>
  );
};

CatalogTemplateModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  templates: PropTypes.array,
  loading: PropTypes.bool,
  onShareCatalog: PropTypes.func.isRequired,
};

export default CatalogTemplateModal;
