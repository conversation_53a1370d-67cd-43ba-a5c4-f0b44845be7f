import { TouchableOpacity, StyleSheet, Text, View } from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import React, { useMemo } from "react";
import { useTheme } from "@react-navigation/native";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      borderRadius: 7,
      justifyContent: "center",
      marginHorizontal: 5,
      paddingHorizontal: 15,
      paddingVertical: 5,
    },
    container: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
    },
    disabled: {
      backgroundColor: theme.appColors.disabled,
    },
    selected: {
      backgroundColor: theme.appColors.backdropDark,
    },
    unselected: {
      backgroundColor: "transparent",
    },
  });
  return styles;
};

const ButtonToggle = ({ options, selectedOption, onSelect, disabled }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const styles = useMemo(() => createStyles(theme), [theme]);

  return (
    <View style={styles.container}>
      {options.map((option, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.button,
            selectedOption === option ? styles.selected : styles.unselected,
            disabled ? styles.disabled : null,
          ]}
          onPress={() => onSelect(option)}
          disabled={disabled}
        >
          <Text
            style={[
              globalStyles.headlineMedium,
              selectedOption === option
                ? globalStyles.textNeutral
                : globalStyles.text,
            ]}
          >
            {option}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

ButtonToggle.propTypes = {
  options: PropTypes.arrayOf(PropTypes.string).isRequired,
  selectedOption: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default ButtonToggle;
