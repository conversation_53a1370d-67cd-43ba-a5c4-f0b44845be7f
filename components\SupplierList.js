import { useContext } from "react";
import { StyleSheet, View } from "react-native";
import { useNavigation } from "@react-navigation/native";

import { UserContext } from "../context/userContext";
import Text from "./Text";
import IconButton, { AllVendorsButton } from "./IconButton";
import useGlobalStyles from "../lib/useGlobalStyles";

const styles = StyleSheet.create({
  listContainer: {
    flexDirection: "row",
  },
  outerContainer: {
    backgroundColor: "white",
    paddingBottom: 6,
    paddingHorizontal: 30,
  },
});

const SupplierList = () => {
  const { user } = useContext(UserContext);

  const navigation = useNavigation();
  const globalStyles = useGlobalStyles();

  return (
    <View style={styles.outerContainer}>
      <Text
        style={[globalStyles.headlineMedium, globalStyles.text, styles.title]}
      >
        Your Vendors
      </Text>
      <View style={styles.listContainer}>
        {user.suppliers.slice(0, 3).map((supplier) => (
          <IconButton
            key={supplier.id}
            label={supplier.name}
            onPress={() => navigation.navigate("BrowseSupplier", { supplier })}
            uri={supplier.logo}
            minimum={supplier.minimum}
          />
        ))}
        <AllVendorsButton />
      </View>
    </View>
  );
};

export default SupplierList;
