query GetUserGoals($goalsInput: GoalsInput!) {
  goals(goalsInput: $goalsInput) {
    goals {
      id
      supplier_id
      name
      type
      period
      target_amount
      start_date
      end_date
      is_active
      status
      available_periods {
        label
        value
      }
      created_at
      updated_at
      assignments {
        id
        goal_id
        employee_id
        employee {
          id
          name
          email
        }
        target_amount
        current_progress
        percentage_complete
        created_at
        updated_at
      }
    }
    totalCount
  }
}