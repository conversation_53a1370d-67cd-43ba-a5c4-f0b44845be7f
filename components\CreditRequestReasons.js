import React from "react";
import { View, Image, StyleSheet, Text } from "react-native";
import useGlobalStyles from "../lib/useGlobalStyles";
import CreditRequestTag from "./CreditRequestTag";
const CreditRequestReasons = ({ reasons }) => {
  const globalStyles = useGlobalStyles();
  return (
    <View
      style={{
        marginBottom: 5,
        flexDirection: "row",
      }}
    >
      {reasons.map((reason) => (
        <CreditRequestTag key={reason} reason={reason} />
      ))}
    </View>
  );
};

export default CreditRequestReasons;
