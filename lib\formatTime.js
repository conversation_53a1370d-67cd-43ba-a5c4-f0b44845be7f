const formatTime = (time) => {
  if (!time) return "";
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? "PM" : "AM";
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return `${displayHour}:${minutes} ${ampm}`;
};

const formatDaysOfWeek = (daysOfWeek) => {
  if (!daysOfWeek || daysOfWeek.length === 0) return "";

  // Check if it contains all 7 days of the week
  if (daysOfWeek.length === 7) {
    return "Every Day";
  }

  // Check if it contains "everyday" (case insensitive)
  const hasEveryday = daysOfWeek.some(
    (day) =>
      day.toLowerCase() === "everyday" || day.toLowerCase() === "every day"
  );

  if (hasEveryday) {
    return "Every Day";
  }

  // Mapping of full day names to abbreviated versions
  const dayAbbreviations = {
    Monday: "M",
    Tuesday: "T",
    Wednesday: "W",
    Thursday: "Th",
    Friday: "F",
    Saturday: "Sa",
    Sunday: "Su",
  };

  // Convert days to abbreviated format
  const abbreviatedDays = daysOfWeek.map((day) => dayAbbreviations[day] || day);

  // If only one day, add "only" to the end
  if (abbreviatedDays.length === 1) {
    return `${abbreviatedDays[0]} only`;
  }

  // Otherwise, join with spaces
  return abbreviatedDays.join(" ");
};

export { formatTime, formatDaysOfWeek };
