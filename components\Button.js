import {
  TouchableOpacity,
  StyleSheet,
  Text,
  ActivityIndicator,
} from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import React, { useMemo } from "react";
import { useTheme } from "@react-navigation/native";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      borderRadius: 7,
      justifyContent: "center",
      paddingVertical: 5,
      width: "100%",
    },
    dark: {
      backgroundColor: theme.appColors.backdropDark,
    },
    disabled: {
      backgroundColor: theme.appColors.disabled,
    },
    light: {
      backgroundColor: theme.appColors.backdropLight,
    },
  });
  return styles;
};

const Button = (props) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const styleVariant = props.variant === "light" ? styles.light : styles.dark;

  const textVariant =
    props.variant === "light" ? globalStyles.text : globalStyles.textNeutral;
  const sizes = {
    sm: { height: 30, width: 80 },
    md: { height: 40, width: 130 },
    lg: { height: 40, width: 310 },
  };
  const sizeVariant = sizes[props.size];
  return (
    <TouchableOpacity
      style={[
        styles.button,
        styleVariant,
        sizeVariant,
        props.style,
        props.disabled ? styles.disabled : null,
        props.loading ? { flexDirection: "row" } : null,
      ]}
      onPress={props.onPress}
      disabled={props.disabled || props.loading}
    >
      <Text style={[globalStyles.headlineMedium, textVariant]}>
        {props.children}
      </Text>
      {props.loading ? (
        <ActivityIndicator
          color={textVariant.color}
          style={{ marginLeft: 5 }}
        />
      ) : null}
    </TouchableOpacity>
  );
};

Button.propTypes = {
  children: PropTypes.node,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  onPress: PropTypes.func,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
};

export default Button;
