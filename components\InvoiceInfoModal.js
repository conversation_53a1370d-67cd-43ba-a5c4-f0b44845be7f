import { Ionicons } from "@expo/vector-icons";
import { useHeaderHeight } from "@react-navigation/elements";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import DropDownPicker from "react-native-dropdown-picker";
import useGlobalStyles from "../lib/useGlobalStyles";
import Button from "./Button";
import Text from "./Text";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    infoContainer: {
      alignItems: "center",
      flexDirection: "column",
      marginBottom: 10,
    },
    infoContainerRow: {
      flexDirection: "row",
      gap: 20,
      marginBottom: 10,
    },
    safeContainer: {
      backgroundColor: "white",
      borderRadius: 20,
      flexDirection: "column",
      paddingBottom: 50,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    textInput: {
      backgroundColor: "white",
      borderColor: theme.appColors.backdropDark,
      borderRadius: 10,
      borderWidth: 1,
      padding: 10,
      width: "100%",
    },
  });
  return styles;
};

const InvoiceInfoModal = ({
  onClose,
  subtotal,
  paid,
  credit,
  paymentMethod,
  notes,
  config,
}) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);
  const headerHeight = useHeaderHeight();
  const [paidInput, setPaidInput] = useState(paid ? paid.toFixed(2) : "");
  const [creditInput, setCreditInput] = useState(
    credit ? credit.toFixed(2) : ""
  );
  const [paymentMethodInput, setPaymentMethodInput] = useState(paymentMethod);
  const [paymentMethodInputOpen, setPaymentMethodInputOpen] = useState(false);
  const [notesInput, setNotesInput] = useState(notes || "");
  const openBalance =
    Math.round(
      (subtotal - (Number(creditInput) || 0) - (Number(paidInput) || 0)) * 100
    ) / 100;
  const totalColor =
    openBalance === 0 ? theme.appColors.greenText : theme.appColors.redText;

  return (
    <KeyboardAvoidingView
      behavior="padding"
      keyboardVerticalOffset={headerHeight}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={styles.safeContainer}>
          <View
            style={{
              width: "100%",
              marginBottom: 10,
              flexDirection: "row",
              justifyContent: "flex-start",
            }}
          >
            <TouchableOpacity onPress={() => onClose(undefined)}>
              <Ionicons
                name="close"
                size={35}
                color={theme.appColors.primary}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.infoContainer}>
            <View
              style={{
                ...styles.infoContainerRow,
                textAlign: "left",
              }}
            >
              <View style={{ flex: 1 }}>
                <Text style={[globalStyles.headlineLarge, globalStyles.text]}>
                  Subtotal:
                </Text>
              </View>
              <View style={{ flex: 1 }}>
                <Text
                  style={[
                    globalStyles.headlineLarge,
                    globalStyles.text,
                    { textAlign: "right" },
                  ]}
                >
                  ${subtotal.toFixed(2)}
                </Text>
              </View>
            </View>
            <View style={{ ...styles.infoContainerRow, zIndex: 1 }}>
              <View style={{ flex: 1 }}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Paid
                </Text>
                <TextInput
                  style={styles.textInput}
                  placeholderTextColor="gray"
                  placeholder="Enter Amount"
                  onChangeText={(text) => {
                    setPaidInput(text);
                  }}
                  value={paidInput}
                  keyboardType="numeric"
                  onEndEditing={() => {
                    setPaidInput(Number(paidInput).toFixed(2));
                  }}
                />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Payment Method
                </Text>
                <View style={{ flex: 1 }}>
                  <DropDownPicker
                    open={paymentMethodInputOpen}
                    value={paymentMethodInput}
                    items={[
                      { label: "Cash", value: "Cash" },
                      { label: "Check", value: "Check" },
                      { label: "Card", value: "Card" },
                      {
                        label: "Money Transfer/Zelle",
                        value: "Money Transfer/Zelle",
                      },
                      { label: "Other", value: "Other" },
                    ]}
                    setOpen={setPaymentMethodInputOpen}
                    setValue={setPaymentMethodInput}
                    dropDownContainerStyle={{
                      borderColor: theme.appColors.backdropDark,
                      borderRadius: 10,
                    }}
                    style={{
                      backgroundColor: "white",
                      borderColor: theme.appColors.backdropDark,
                      borderRadius: 10,
                      borderWidth: 1,
                      minHeight: null,
                      height: "100%",
                      padding: 10,
                    }}
                    placeholderStyle={{
                      color: "gray",
                    }}
                  />
                </View>
              </View>
            </View>

            <View style={styles.infoContainerRow}>
              <View style={{ flex: 1 }}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Notes/Check #
                </Text>
                <TextInput
                  style={styles.textInput}
                  placeholderTextColor="gray"
                  placeholder="Enter Notes/Check #"
                  onChangeText={(text) => {
                    setNotesInput(text);
                  }}
                  value={notesInput}
                  multiline={true}
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
            </View>

            <View style={styles.infoContainerRow}>
              <View style={{ flex: 1 }}>
                <Text
                  style={[
                    globalStyles.headlineMedium,
                    globalStyles.text,
                    styles.signatureTitleText,
                  ]}
                >
                  Credit
                </Text>
                <TextInput
                  style={styles.textInput}
                  placeholderTextColor="gray"
                  placeholder="Enter Amount"
                  onChangeText={(text) => {
                    setCreditInput(text);
                    if (!credit) {
                      setPaidInput((subtotal - Number(text)).toFixed(2));
                    }
                  }}
                  value={creditInput}
                  keyboardType="numeric"
                  onEndEditing={() => {
                    setCreditInput(Number(creditInput).toFixed(2));
                  }}
                />
              </View>
            </View>
            <View
              style={{
                ...styles.infoContainerRow,
                textAlign: "left",
              }}
            >
              <View>
                <Text
                  style={[globalStyles.headlineLarge, { color: totalColor }]}
                >
                  Open Balance:
                </Text>
              </View>
              <View style={{ flex: 1 }}>
                <Text
                  style={[
                    globalStyles.headlineLarge,
                    { textAlign: "right", color: totalColor },
                  ]}
                >
                  ${openBalance.toFixed(2)}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.infoContainerRow}>
            <Button
              variant="light"
              size="md"
              style={{ flex: 1 }}
              onPress={() => {
                const lastPaidDate = new Date().toISOString().slice(0, 10)
                onClose({
                  paid: Number(paidInput),
                  credit: Number(creditInput),
                  total: subtotal - Number(creditInput),
                  payment_method: paymentMethodInput,
                  notes: notesInput,
                  config: {
                    ...config,
                    date_last_paid: paidInput > 0 ? lastPaidDate : null
                  }
                });
              }}
            >
              <Text>Update</Text>
            </Button>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

InvoiceInfoModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  subtotal: PropTypes.number,
  paid: PropTypes.number,
  credit: PropTypes.number,
  paymentMethod: PropTypes.string,
  notes: PropTypes.string,
  config: PropTypes.object,
};

export default InvoiceInfoModal;
