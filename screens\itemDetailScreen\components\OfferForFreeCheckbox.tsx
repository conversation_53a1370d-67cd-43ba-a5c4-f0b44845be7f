import React from "react";
import { TouchableOpacity } from "react-native";
import Checkbox from "expo-checkbox";
import { useTheme } from "@react-navigation/native";
import Animated, { FadeIn } from "react-native-reanimated";
import Text from "../../../components/Text";
import useGlobalStyles from "../../../lib/useGlobalStyles";

interface OfferForFreeCheckboxProps {
  canSellForFree: boolean;
  isEditingPrice: boolean;
  offerForFree: boolean;
  onOfferForFreeChange: (value: boolean) => void;
}

export default function OfferForFreeCheckbox({
  canSellForFree,
  isEditingPrice,
  offerForFree,
  onOfferForFreeChange,
}: OfferForFreeCheckboxProps) {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  if (!canSellForFree || !isEditingPrice) {
    return null;
  }

  const handleCheckboxChange = (newValue: boolean) => {
    onOfferForFreeChange(newValue);
  };

  const handleTouchablePress = () => {
    const updated = !offerForFree;
    onOfferForFreeChange(updated);
  };

  return (
    <Animated.View
      entering={FadeIn.duration(300)}
      style={{
        flexDirection: "row",
        alignItems: "center",
        marginTop: -12,
      }}
    >
      <Checkbox
        value={offerForFree}
        onValueChange={handleCheckboxChange}
        color={offerForFree ? theme.appColors.primary : undefined}
        style={{
          borderRadius: 4,
          borderWidth: 1,
          borderColor: theme.appColors.primary,
        }}
      />
      <TouchableOpacity onPress={handleTouchablePress}>
        <Text
          style={[
            globalStyles.bodyLarge,
            globalStyles.text,
            { marginLeft: 8, paddingTop: 4, paddingBottom: 4 },
          ]}
        >
          Offer for free
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
}
