const { withDangerousMod } = require("@expo/config-plugins");
const path = require("path");
const fs = require("fs");

const withZebraSdk = (config) => {
  return withDangerousMod(config, [
    "ios",
    (config) => {
      const podfilePath = path.join(
        config.modRequest.platformProjectRoot,
        "Podfile"
      );

      // Read the existing Podfile contents
      let podfileContents = fs.readFileSync(podfilePath, "utf-8");

      // Define the post_install script to add ZSDK_API.xcframework to ExpoZebraPrinter
      const postInstallScript = `
        post_install do |installer|
          installer.pods_project.targets.each do |target|
            if target.name == 'ExpoZebraPrinter'
              framework_path = File.join(__dir__, '../modules/expo-zebra-printer/ios/Frameworks/ZSDK_API.xcframework')
              target.add_file_references([framework_path])

              # Add to Embed Frameworks build phase if necessary
              embed_phase = target.build_phases.find { |phase| phase.display_name == 'Embed Frameworks' }
              embed_phase.add_file_reference(framework_path) if embed_phase
            end
          end
        end
      `;

      // Only add the post_install hook if it doesn’t already exist
      if (!podfileContents.includes("post_install do |installer|")) {
        podfileContents += postInstallScript;
        fs.writeFileSync(podfilePath, podfileContents, "utf-8");
      }

      return config;
    },
  ]);
};

module.exports = withZebraSdk;
