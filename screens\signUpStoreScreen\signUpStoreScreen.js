import { useState, useContext } from "react";
import { Controller, useForm } from "react-hook-form";
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  ScrollView,
  ActivityIndicator,
  StatusBar,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import SafeAreaView from "../../components/SafeAreaView";
import Text from "../../components/Text";
import AddressAutocomplete from "../../components/AddressAutocomplete";
import { UserContext } from "../../context/userContext";
import { posthog } from "../../src/posthog";
import {
  useCreateUserMutation,
  useGetRoutesQuery,
} from "../../generated/graphql";
import DropDownPicker from "react-native-dropdown-picker";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldSecondaryText: {
      color: theme.appColors.text,
      fontFamily: theme.fonts.regular.fontFamily,
      fontSize: 16,
    },
    container: {
      backgroundColor: theme.appColors.backdropNeutral,
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    dropdownWrapper: {
      zIndex: 1000,
    },
    keyboardAvoidingContainer: {
      flex: 1,
    },
    labelContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 8,
      marginTop: 20,
    },
    labelIcon: {
      marginRight: 10,
    },
    resultContainer: {
      alignItems: "center",
      backgroundColor: theme.colors.background,
      flex: 1,
      justifyContent: "center",
      padding: 30,
    },
    resultHeaderText: {
      color: theme.appColors.text,
      fontFamily: theme.fonts.regular.fontFamily,
      fontSize: 28,
      textAlign: "center",
    },
    resultIcon: {
      marginBottom: 20,
    },
    resultMessageText: {
      color: theme.appColors.text,
      fontFamily: theme.fonts.light.fontFamily,
      fontSize: 16,
      lineHeight: 24,
      marginVertical: 15,
      textAlign: "center",
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 40,
      paddingHorizontal: 20,
    },
    scrollView: {
      display: "flex",
      flexDirection: "column",
      height: "100%",
      marginHorizontal: 20,
    },
    submitBtn: {
      alignItems: "center",
      backgroundColor: theme.appColors.primary,
      borderRadius: 10,
      height: 50,
      justifyContent: "center",
      marginBottom: 100,
      marginTop: 40,
      width: "100%",
    },
    submitBtnText: {
      color: theme.appColors.textNeutral,
      fontFamily: theme.fonts.regular.fontFamily,
      fontSize: 16,
    },
    textInput: {
      backgroundColor: theme.appColors.surface,
      borderColor: theme.appColors.disabled,
      borderRadius: 10,
      borderWidth: 1,
      color: theme.appColors.text,
      fontFamily: theme.fonts.medium.fontFamily,
      fontSize: 16,
      height: 50,
      paddingHorizontal: 15,
    },
    textInputError: {
      color: theme.appColors.redText,
      fontFamily: theme.fonts.light.fontFamily,
      fontSize: 12,
      marginTop: 6,
    },
  });
  return styles;
};

export default function SignUpStoreScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const { user, backendUrl } = useContext(UserContext);
  const supplierId = user.suppliers[0]?.id;

  const [signupComplete, setSignupComplete] = useState(false);
  const [signupError, setSignupError] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newlyCreatedStore, setNewlyCreatedStore] = useState(null);

  const [open, setOpen] = useState(false);

  const { control, handleSubmit, formState } = useForm({
    defaultValues: {
      storeName: "",
      ownerName: "",
      phoneNumber: "",
      address: "",
      routeId: "",
    },
    mode: "onBlur",
  });
  const { errors } = formState;

  const [createUser] = useCreateUserMutation();

  const { data: routesData } = useGetRoutesQuery({
    variables: { getRoutesBySupplierInput: { supplierId } },
    skip: !supplierId,
  });

  const routeItems =
    routesData?.routesBySupplier?.map((route) => ({
      label: route.name,
      value: route.id,
    })) || [];

  const onSubmit = async (data) => {
    setIsSubmitting(true);

    try {
      posthog.capture("mobile_store_signup_submitted", {
        storeName: data.storeName,
        hasRoute: !!data.routeId,
        createdBy: user.id,
      });

      const result = await createUser({
        variables: {
          createUserInput: {
            name: data.storeName,
            address: data.address,
            phone_number: data.phoneNumber,
            route_id: data.routeId || null,
            suppliers: [supplierId],
            created_by: `mobile_driver_${user.id}`,
            created_at: new Date().toISOString(),
            user_name: data.storeName
              .trim()
              .toLowerCase()
              .replace(/\s+/g, "-")
              .replace(/['"]/g, ""),
            password: `${data.ownerName.split(" ")[0].toLowerCase()}-store`,
            config: { owner_name: data.ownerName },
          },
        },
      });

      if (result.data?.createUser) {
        posthog.capture("mobile_store_signup_success", {
          storeId: result.data.createUser[2],
          storeName: data.storeName,
        });
        setNewlyCreatedStore({
          id: result.data.createUser[2],
          routeId: data.routeId,
        });
        setSignupComplete(true);
      } else {
        throw new Error("Create user mutation did not return expected data.");
      }
    } catch (err) {
      console.error("Store signup error:", err);
      posthog.capture("mobile_store_signup_error", { error: err.message });
      setSignupError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (signupComplete) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.resultContainer}>
          <Ionicons
            name="checkmark-circle-outline"
            size={90}
            color={theme.appColors.accent}
            style={styles.resultIcon}
          />
          <Text style={styles.resultHeaderText}>
            {t("signupStore.success")}
          </Text>
          <Text style={styles.resultMessageText}>
            {t("signupStore.successMessage")}
          </Text>
          <TouchableOpacity
            style={styles.submitBtn}
            onPress={() => {
              if (newlyCreatedStore) {
                navigation.navigate("Users", {
                  autoSelectStoreId: newlyCreatedStore.id,
                  autoSelectRouteId: newlyCreatedStore.routeId,
                });
              }
            }}
          >
            <Text style={styles.submitBtnText}>Done</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (signupError) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.resultContainer}>
          <Ionicons
            name="close-circle-outline"
            size={90}
            color={theme.appColors.redText}
            style={styles.resultIcon}
          />
          <Text style={styles.resultHeaderText}>{t("signupStore.error")}</Text>
          <Text style={styles.resultMessageText}>
            {t("signupStore.errorMessage")}
          </Text>
          <TouchableOpacity
            style={styles.submitBtn}
            onPress={() => setSignupError(false)}
          >
            <Text style={styles.submitBtnText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingContainer}
        keyboardVerticalOffset={Platform.OS === "ios" ? 60 : 0}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <ScrollView style={styles.scrollView}>
            <View style={styles.labelContainer}>
              <Ionicons
                name="storefront-outline"
                size={22}
                color={theme.appColors.text}
                style={styles.labelIcon}
              />
              <Text style={styles.boldSecondaryText}>
                {" "}
                {t("signupStore.storeName")}{" "}
              </Text>
            </View>
            <Controller
              control={control}
              rules={{ required: t("signupStore.storeNameError") }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={styles.textInput}
                  placeholder={t("signupStore.storeNamePlaceholder")}
                  placeholderTextColor={theme.appColors.background}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  value={value}
                  autoCapitalize="words"
                />
              )}
              name="storeName"
            />
            {errors.storeName && (
              <Text style={styles.textInputError}>
                {" "}
                {errors.storeName.message}{" "}
              </Text>
            )}

            <View style={styles.labelContainer}>
              <Ionicons
                name="person-outline"
                size={22}
                color={theme.appColors.text}
                style={styles.labelIcon}
              />
              <Text style={styles.boldSecondaryText}>
                {" "}
                {t("signupStore.ownerName")}{" "}
              </Text>
            </View>
            <Controller
              control={control}
              rules={{ required: t("signupStore.ownerNameError") }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={styles.textInput}
                  placeholder={t("signupStore.ownerNamePlaceholder")}
                  placeholderTextColor={theme.appColors.placeholder.background}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  value={value}
                  autoCapitalize="words"
                />
              )}
              name="ownerName"
            />
            {errors.ownerName && (
              <Text style={styles.textInputError}>
                {" "}
                {errors.ownerName.message}{" "}
              </Text>
            )}

            <View style={styles.labelContainer}>
              <Ionicons
                name="call-outline"
                size={22}
                color={theme.appColors.text}
                style={styles.labelIcon}
              />
              <Text style={styles.boldSecondaryText}>
                {" "}
                {t("signupStore.phoneNumber")}{" "}
              </Text>
            </View>
            <Controller
              control={control}
              rules={{
                required: t("signupStore.phoneNumberError"),
                pattern: {
                  value:
                    /^([0-9]{10})|((\([0-9]{3}\)|[0-9]{3}-?)\s?[0-9]{3}\s?-?\s?[0-9]{4})$/g,
                  message: t("signupStore.phoneNumberError"),
                },
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={styles.textInput}
                  placeholder={t("signupStore.phoneNumberPlaceholder")}
                  placeholderTextColor={theme.appColors.placeholder.background}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  value={value}
                  keyboardType="phone-pad"
                />
              )}
              name="phoneNumber"
            />
            {errors.phoneNumber && (
              <Text style={styles.textInputError}>
                {" "}
                {errors.phoneNumber.message}{" "}
              </Text>
            )}

            <View style={styles.labelContainer}>
              <Ionicons
                name="location-outline"
                size={22}
                color={theme.appColors.text}
                style={styles.labelIcon}
              />
              <Text style={styles.boldSecondaryText}>
                {" "}
                {t("signupStore.address")}{" "}
              </Text>
            </View>
            <Controller
              control={control}
              name="address"
              rules={{ required: t("signupStore.addressError") }}
              render={({ field: { onChange } }) => (
                <AddressAutocomplete
                  style={styles.textInput}
                  onAddressSelected={onChange}
                  backendUrl={backendUrl}
                />
              )}
            />
            {errors.address && (
              <Text style={styles.textInputError}>
                {" "}
                {errors.address.message}{" "}
              </Text>
            )}

            <View style={styles.labelContainer}>
              <Ionicons
                name="map-outline"
                size={22}
                color={theme.appColors.text}
                style={styles.labelIcon}
              />
              <Text style={styles.boldSecondaryText}>
                {" "}
                {t("signupStore.route")}{" "}
              </Text>
            </View>
            <Controller
              control={control}
              name="routeId"
              render={({ field: { onChange, value } }) => (
                <View style={styles.dropdownWrapper}>
                  <DropDownPicker
                    open={open}
                    value={value}
                    items={routeItems}
                    setOpen={setOpen}
                    setValue={(callback) => {
                      const val = callback(value);
                      onChange(val);
                      console.log(val);
                    }}
                    placeholder={t("signupStore.routePlaceholder")}
                    style={styles.textInput}
                    placeholderStyle={{
                      color: theme.appColors.placeholder.background,
                    }}
                    dropDownContainerStyle={{
                      backgroundColor: theme.appColors.surface,
                      borderColor: theme.appColors.disabled,
                    }}
                    textStyle={{
                      color: theme.appColors.text,
                      fontFamily: theme.fonts.medium.fontFamily,
                    }}
                    listMode="SCROLLVIEW"
                  />
                </View>
              )}
            />
            {errors.routeId && (
              <Text style={styles.textInputError}>
                {" "}
                {errors.routeId.message}{" "}
              </Text>
            )}

            <TouchableOpacity
              style={[styles.submitBtn, { opacity: isSubmitting ? 0.7 : 1 }]}
              onPress={handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator color={theme.appColors.textNeutral} />
              ) : (
                <Text style={styles.submitBtnText}>
                  {" "}
                  {t("signupStore.submit")}{" "}
                </Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
