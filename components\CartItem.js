import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import useGlobalStyles from "../lib/useGlobalStyles";
import NumericInput from "./NumericInput";
import Checkbox from "expo-checkbox";
import { useTheme } from "@react-navigation/native";
import React, { useMemo, useContext } from "react";
import { Ionicons } from "@expo/vector-icons";
import { UserContext } from "../context/userContext";
import { getUomQuantity, getUomName } from "../lib/uomHelpers";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    buttonText: {
      color: theme.appColors.textLight,
      // fontSize: 25,
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
    imageContainer: {
      justifyContent: "center",
    },
    itemContainer: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      marginVertical: 10,
    },
    itemCountContainer: {
      alignItems: "center",
      flex: 1,
      flexDirection: "row",
      justifyContent: "center",
      marginRight: 10,
    },

    itemImage: {
      height: 50,
      width: 50,
    },
    itemSubtotalContainer: {
      justifyContent: "center",
    },
    itemSubtotalText: {
      color: theme.appColors.primary,
      textAlign: "right",
      width: 60,
    },
    itemTextContainer: {
      flex: 2,
      marginLeft: 10,
    },

    unitSizeText: {
      fontSize: 12,
      color: theme.appColors.textLight,
      textAlign: "left",
    },
    uomText: {
      fontSize: 11,
      color: theme.appColors.text,
      textAlign: "left",
      fontStyle: "italic",
      marginTop: 2,
    },
    itemCountContainer: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      marginRight: 10,
    },
    minusButton: {
      display: "flex",
      alignItems: "center",
      backgroundColor: "white",
      justifyContent: "center",
      // borderColor: theme.appColors.backdropLight,
      // borderWidth: 1,
      // borderRadius: 5,
      height: 25,
      width: 25,
    },
    plusButton: {
      display: "flex",
      alignItems: "center",
      backgroundColor: "white",
      justifyContent: "center",
      // borderColor: theme.appColors.backdropLight,
      // borderWidth: 1,
      // borderRadius: 5,
      height: 25,
      width: 25,
    },
    quantity: {
      height: 30,
      justifyContent: "center",
      width: 25,
    },
    quantityText: {
      color: theme.appColors.textLight,
      fontSize: 15,
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
    unitSizeText: {
      color: theme.appColors.textLight,
      fontSize: 12,
      textAlign: "left",
    },
  });
  return styles;
};

export default function CartItem({
  imageUri,
  itemName,
  unitSize,
  quantity,
  amount,
  onEditQuantity,
  cartId,
  itemId,
  item, // Add item prop for UOM information
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const { user } = useContext(UserContext);

  // Calculate display quantity (divide by UOM quantity if UOM is selected)
  const uomQuantity = item?.item_uom_id
    ? getUomQuantity(item, item.item_uom_id)
    : 1;
  const displayQuantity = Number((quantity / uomQuantity).toFixed(2));

  // Get UOM name for display
  const selectedUomName = item?.item_uom_id
    ? getUomName(item, item.item_uom_id)
    : "Unit";

  const handleEditQuantity = (newQuantity) => {
    // Convert display quantity back to backend quantity
    const backendQuantity = newQuantity * uomQuantity;
    onEditQuantity(cartId, itemId, backendQuantity);
  };

  return (
    <View style={styles.itemContainer}>
      <View style={styles.imageContainer}>
        <Image
          style={styles.itemImage}
          source={{
            uri: imageUri,
          }}
        />
      </View>
      <View style={styles.itemTextContainer}>
        <Text
          style={[globalStyles.titleLarge, globalStyles.text]}
          numberOfLines={3}
        >
          {itemName}
        </Text>
        {item?.item_uom_id && selectedUomName !== "Unit" && (
          <Text style={styles.uomText}>
            UOM: {selectedUomName} ({getUomQuantity(item, item.item_uom_id)})
          </Text>
        )}
        {/* <Text style={styles.unitSizeText}>{unitSize} ct</Text> */}
      </View>
      <View style={styles.itemCountContainer}>
        <NumericInput
          onEditQuantity={handleEditQuantity}
          quantity={displayQuantity}
          totalHeight={20}
          totalWidth={69}
          borderRadius={5}
        />
      </View>
      {user.show_prices ? (
        <View style={styles.itemSubtotalContainer}>
          <Text style={styles.itemSubtotalText}>${amount}</Text>
        </View>
      ) : null}
    </View>
  );
}
