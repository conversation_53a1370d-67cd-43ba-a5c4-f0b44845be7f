import {
  StyleSheet,
  View,
  TextInput,
  PixelRatio,
  Platform,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { connectSearchBox } from "react-instantsearch-native";

const fontScale = PixelRatio.getFontScale();

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.appColors.primary,
      padding: 16,
    },
    input: {
      backgroundColor: "white",
      borderColor: "#ddd",
      borderRadius: 6,
      borderWidth: 1,
      fontSize: 16 / fontScale,
      height: 48,
      padding: 12,
      shadowColor: "black",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
  });
  return styles;
};

export const SearchBox = ({
  currentRefinement,
  refine,
  noAutoFocus,
  searchKeyboard,
  update,
  placeholder,
  defaultRefinement,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <View style={styles.container}>
      <TextInput
        autoFocus={!noAutoFocus}
        keyboardType={
          searchKeyboard && Platform.OS === "ios" ? "web-search" : "default"
        }
        style={styles.input}
        onChangeText={(value) => refine(value)}
        value={currentRefinement || defaultRefinement}
        onEndEditing={update}
        placeholder={placeholder || "Start typing to search!"}
        placeholderTextColor="#bebebe"
      />
    </View>
  );
};

SearchBox.propTypes = {
  currentRefinement: PropTypes.string.isRequired,
  refine: PropTypes.func.isRequired,
  noAutoFocus: PropTypes.bool,
  searchKeyboard: PropTypes.bool,
  update: PropTypes.func,
  placeholder: PropTypes.string,
};

export default connectSearchBox(SearchBox);
