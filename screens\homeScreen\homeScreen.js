import { useContext, useEffect } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  PixelRatio,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import { useTranslation } from "react-i18next";

import Text from "../../components/Text";
import { createStackNavigator } from "@react-navigation/stack";
import { useHomeScreenQuery } from "../../generated/graphql";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import OrderDetailScreen from "../orderDetailScreen/orderDetailScreen";
import SelectItemsScreen from "../selectItemsScreen/selectItemsScreen";
import SearchResultsScreen from "../searchResultsScreen/searchResultsScreen";
import SettingsScreen from "../settingsScreen/settingsScreen";
import DebugScreen from "../debugScreen/debugScreen";
import PaymentScreen from "../paymentScreen/paymentScreen";
import PaymentScreen2 from "../paymentScreen/paymentScreen2";
import PaymentScreen3 from "../paymentScreen/paymentScreen3";
import CutoffTimesScreen from "../cutoffTimesScreen/cutoffTimesScreen";
import RecommendedItemsModalScreen from "../recommendedItemsModalScreen/recommendedItemsModalScreen";
import { Ionicons } from "@expo/vector-icons";
import { UserContext } from "../../context/userContext";
import { formatTime, formatDaysOfWeek } from "../../lib/formatTime";

import Spotlights from "../../components/Spotlights";
import ItemsSection from "../../components/ItemsSection";
import SupplierList from "../../components/SupplierList";
import Promo from "../../components/Promo";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import OrderStatusScreen from "../orderStatusScreen/orderStatusScreen";
import SupplierItemsScreen from "../supplierItemsScreen/supplierItemsScreen";
import AllVendorsScreen from "../allVendorsScreen/allVendorsScreen";
import BrowseSupplierScreen from "../browseSupplierScreen/browseSupplierScreen";
import UsersScreen from "../usersScreen/usersScreen";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { posthog } from "../../src/posthog";
import { useFavorites } from "../../context/favoritesContext";
const fontScale = PixelRatio.getFontScale();

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.appColors.primary,
      flex: 1,
    },
    homeContainer: {
      flex: 1,
      paddingTop: 0,
    },
    searchBar: {
      backgroundColor: "white",
      borderRadius: 5,
      flex: 1,
      height: 38,
      marginLeft: 5,
      padding: 0,
      shadowOffset: { height: 3 },
      shadowOpacity: 0.1,
    },
    searchBarInsideView: {
      alignItems: "center",
      flexDirection: "row",
      flex: 1,
      justifyContent: "space-between",
      paddingHorizontal: 10,
    },
    sectionsList: {
      // height: "100%",
      // paddingBottom: 300,
      flex: 1,
    },
  });
  return styles;
};

function HomeComponent({ navigation }) {
  const globalStyles = useGlobalStyles();
  const { t } = useTranslation();
  const { user, showRecommendationsModal } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const bgVariant = "dark";
  const firstSupplier = user.suppliers[0];
  const { setFavoriteIds } = useFavorites();

  const { loading, data, error } = useHomeScreenQuery({
    fetchPolicy: "cache-and-network",
    skip: user == null || user.id == null,
    variables: {
      getCategoriesInput: {
        pagination: {
          offset: 0,
          limit: 100,
        },
        userId: user.id,
      },

      getSectionsInput: {
        pagination: {
          offset: 0,
          limit: 10,
        },
        userId: user.id,
      },
      getRecommendationsInput: {
        userId: user.id,
        limit: 6,
      },
      userId: user.id,
    },
  });

  // Populate favorites context when data loads
  useEffect(() => {
    if (data?.sections) {
      const favoritesSection = data.sections.find(
        (section) => section.value === "favorites"
      );
      if (favoritesSection && favoritesSection.items) {
        // Extract all favorite item IDs
        const favoriteIds = favoritesSection.items.map((item) => item.id);
        setFavoriteIds(favoriteIds);
      }
    }
  }, [data?.sections, setFavoriteIds]);

  useEffect(() => {
    if (
      showRecommendationsModal &&
      !loading &&
      data.recommendations &&
      data.recommendations.length >= 4 &&
      !user.supplier_beta
    ) {
      navigation.navigate("RecommendedItemsModalScreen", {
        recommendations: data.recommendations,
      });
    }
  }, [showRecommendationsModal, loading, data]);

  const settingsButtonPressed = async () => {
    posthog.capture("settings_clicked");
    navigation.navigate("Settings");
  };

  const searchBarPressed = async () => {
    navigation.navigate(
      "SearchResults",
      user.supplier_beta ? { supplier: firstSupplier.name } : {}
    );
    posthog.capture("search_clicked");
  };

  const scanButtonPressed = () => {
    navigation.navigate("Scan");
  };

  const allSections = data?.sections?.concat(
    !user.supplier_beta ? data?.brandSections : []
  );

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  const renderItem = ({ item, index }) => (
    <ItemsSection
      variant={bgVariant}
      params={"asdasdasdasd"}
      title={item.name}
      items={item.items || item.itemsPreview}
      index={index}
      showSuppliers
      userApproved={user.approved}
      addBottomPadding={index === allSections.length - 1}
      customPrices={user.custom_prices}
      refreshOnFocus={item.value === "favorites"}
    />
  );

  const spotlights =
    data.brandSpotlights.length > 0 ? data.brandSpotlights : data.spotlights;

  return (
    <View style={styles.homeContainer}>
      <SafeAreaView style={styles.container}>
        <FlatList
          removeClippedSubviews={true}
          style={styles.sectionsList}
          maxToRenderPerBatch={10}
          initialNumToRender={10}
          ListHeaderComponent={
            <>
              <View style={globalStyles.statusBar}></View>
              <View style={globalStyles.navBar}>
                {!user.supplier_beta ? (
                  <Text
                    maxFontSizeMultiplier={1.4}
                    style={[
                      globalStyles.headlineLarge,
                      globalStyles.textNeutral,
                    ]}
                  >
                    {`Hi, ${user.name}`}
                  </Text>
                ) : (
                  <View>
                    <View
                      style={{
                        flexDirection: firstSupplier.logo ? "row" : "column",
                        alignItems: firstSupplier.logo
                          ? "center"
                          : "flex-start",
                        justifyContent: "flex-start",
                      }}
                    >
                      <Text
                        maxFontSizeMultiplier={1.4}
                        style={[
                          globalStyles.headlineSmall,
                          globalStyles.textNeutral,
                        ]}
                      >
                        {t("common.welcome")}
                      </Text>
                      {firstSupplier.logo ? (
                        <View
                          style={{
                            height: 50,
                            width: 100,
                            overflow: "hidden",
                          }}
                        >
                          <Image
                            style={{
                              // These styles work for the CG logo and may need to be adjusted for others
                              height: 80,
                              width: 90,
                              top: -15,
                              left: -20,
                              borderRadius: 12,
                            }}
                            source={{ uri: firstSupplier.logo }}
                          />
                        </View>
                      ) : (
                        <Text
                          maxFontSizeMultiplier={1.4}
                          style={[
                            globalStyles.headlineLarge,
                            globalStyles.textNeutral,
                            { marginTop: 5 },
                          ]}
                        >
                          {`${firstSupplier.name}`}
                        </Text>
                      )}
                    </View>
                    {user.driver ? (
                      <TouchableOpacity
                        style={[
                          {
                            display: "flex",
                            flexDirection: "row",
                            marginTop: 5,
                            paddingVertical: 5,
                            paddingHorizontal: 10,
                            alignItems: "left",
                            borderRadius: 10,
                            overflow: "hidden",
                            maxWidth: "100%",
                          },
                          !user.is_user_account
                            ? { backgroundColor: "red" }
                            : { borderColor: "#FFFFFF44", borderWidth: 1 },
                        ]}
                        onPress={() => {
                          navigation.navigate("Users");
                        }}
                      >
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          {user.is_user_account ? (
                            <>
                              <Text
                                numberOfLines={1}
                                style={[
                                  globalStyles.headlineMedium,
                                  globalStyles.textNeutral,
                                  { marginRight: 2, flexShrink: 0 },
                                ]}
                              >
                                {t("user.user_selected") + ": "}
                              </Text>
                              <Text
                                numberOfLines={1}
                                style={[
                                  globalStyles.headlineMedium,
                                  globalStyles.textNeutral,
                                  { flex: 1 },
                                ]}
                                ellipsizeMode="tail"
                              >
                                {user.name}
                              </Text>
                            </>
                          ) : (
                            <Text
                              numberOfLines={1}
                              style={[
                                globalStyles.headlineMedium,
                                globalStyles.textNeutral,
                                { flex: 1, textAlign: "center" },
                              ]}
                            >
                              {t("user.click_to_select")}
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    ) : null}
                  </View>
                )}

                <View
                  style={{
                    position: "absolute",
                    top: 27,
                    left: 0,
                    right: 0,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    paddingHorizontal: 10,
                  }}
                >
                  <View style={{ flex: 0.9 }} />
                  {user.delivery_window &&
                    user.delivery_window.days_of_week.length > 0 &&
                    user.delivery_window.start_time &&
                    user.delivery_window.end_time && (
                      <View
                        style={{
                          backgroundColor: theme.appColors.surface,
                          paddingHorizontal: 10,
                          paddingVertical: 6,
                          borderRadius: 8,
                          marginRight: 10,
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          flex: 1,
                          maxWidth: 200,
                          borderLeftColor: theme.appColors.backdropDark,
                          borderLeftWidth: 5,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "flex-start",
                          }}
                        >
                          <Ionicons
                            name="time-outline"
                            size={14}
                            color={theme.appColors.text}
                          />
                          <Text
                            style={{
                              color: theme.appColors.text,
                              marginLeft: 4,
                              fontSize: 11,
                              fontWeight: "600",
                            }}
                          >
                            {t("common.delivery_window")}
                          </Text>
                        </View>
                        <Text
                          style={[
                            globalStyles.bodySmall,
                            {
                              color: theme.appColors.text,
                              fontSize: 10,
                            },
                          ]}
                        >
                          {formatDaysOfWeek(user.delivery_window.days_of_week)}{" "}
                          • {formatTime(user.delivery_window.start_time)}-
                          {formatTime(user.delivery_window.end_time)}
                        </Text>
                      </View>
                    )}
                  <TouchableOpacity onPress={settingsButtonPressed}>
                    <Ionicons
                      name="settings-sharp"
                      size={24}
                      color="white"
                      style={{ paddingRight: 10 }}
                    />
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: 20,
                    flexDirection: "column",
                    justifyContent: "space-between",
                    width: "100%",
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      width: "100%",
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        height: 38,
                        width: 38,
                        paddingLeft: 6,
                        paddingRight: 6,
                        borderRadius: 5,
                        shadowOpacity: 0.1,
                        shadowOffset: { height: 3 },
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: theme.appColors.backdropLight,
                      }}
                      onPress={scanButtonPressed}
                    >
                      <View
                        style={{
                          flexDirection: "column",
                          alignItems: "center",
                        }}
                      >
                        <Image
                          style={{
                            height: 26,
                            width: 26,
                          }}
                          source={theme.assets.barcode}
                        />
                        <Text
                          style={{
                            color: "black",
                            fontSize: 10,
                            lineHeight: 10,
                            fontWeight: "600",
                          }}
                        >
                          Scan
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.searchBar}
                      onPress={searchBarPressed}
                    >
                      <View style={styles.searchBarInsideView}>
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            flex: 1,
                            height: "100%",
                          }}
                        >
                          <Ionicons name="search" size={16} color="black" />
                          <Text
                            maxFontSizeMultiplier={1.4}
                            style={[
                              globalStyles.bodyMedium,
                              { marginTop: 1, marginLeft: 5 },
                            ]}
                          >
                            {!user.supplier_beta
                              ? t("search.search_products_and_suppliers")
                              : t("search.search_products")}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {firstSupplier.name === "Empire Snacks" && (
                <View style={{ paddingTop: 7, backgroundColor: "white" }}>
                  {!user.driver ? <Spotlights spotlights={spotlights} /> : null}
                  {!user.supplier_beta ? <SupplierList /> : null}
                </View>
              )}
            </>
          }
          numColumns={1}
          data={allSections}
          renderItem={renderItem}
          keyExtractor={(item) => item.name}
          onEndReachedThreshold={0.3}
        />
        {user.supplier_beta &&
        firstSupplier.name === "Empire Snacks" &&
        firstSupplier.config &&
        firstSupplier.config.promo &&
        firstSupplier.config.promo.numOrders > firstSupplier.orderCount ? (
          <Promo
            message={firstSupplier.config.promo.message}
            subMessage={firstSupplier.config.promo.subMessage}
            supplier={firstSupplier}
          />
        ) : null}
      </SafeAreaView>
    </View>
  );
}

const HomeStack = createStackNavigator();
export default function HomeScreen() {
  return (
    <HomeStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 20 / fontScale,
        },
      })}
    >
      <HomeStack.Screen
        name="Attain"
        component={HomeComponent}
        options={{ headerShown: false }}
      />
      <HomeStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{ title: "Item Details" }}
      />
      <HomeStack.Screen
        name="OrderDetail"
        component={OrderDetailScreen}
        options={{ title: "Order Details" }}
      />
      <HomeStack.Screen
        name="OrderStatus"
        component={OrderStatusScreen}
        options={{ title: "Order Status" }}
      />
      <HomeStack.Screen
        name="SupplierItems"
        component={SupplierItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
      <HomeStack.Screen
        name="SelectItems"
        component={SelectItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
      <HomeStack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{ title: "Search Results" }}
      />
      <HomeStack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ title: "Settings" }}
      />
      <HomeStack.Screen
        name="Debug"
        component={DebugScreen}
        options={{ title: "Debug Info" }}
      />
      <HomeStack.Screen
        name="PaymentMethods"
        component={PaymentScreen}
        options={{ title: "Payment Method" }}
      />
      <HomeStack.Screen
        name="CutoffTimes"
        component={CutoffTimesScreen}
        options={{ title: "Cutoff Times" }}
      />
      <HomeStack.Screen
        name="AddPaymentMethod"
        component={PaymentScreen2}
        options={{ title: "Add Payment Method" }}
      />
      <HomeStack.Screen
        name="AddPaymentMethodRedirect"
        component={PaymentScreen3}
        options={{ title: "Bank Authorization" }}
      />
      <HomeStack.Screen
        name="AllVendors"
        component={AllVendorsScreen}
        options={{ title: "Your Vendors" }}
      />
      <HomeStack.Screen
        name="BrowseSupplier"
        component={BrowseSupplierScreen}
        options={{ headerShown: false }}
      />
      <HomeStack.Screen
        name="Users"
        component={UsersScreen}
        options={{ title: "Users", headerShown: false }}
      />
      <HomeStack.Screen
        name="RecommendedItemsModalScreen"
        component={RecommendedItemsModalScreen}
        options={{
          headerShown: false,
          stackPresentation: "fullScreenModal", // This is essential for displaying the screen in full-screen
          cardStyle: { backgroundColor: "transparent" },
          cardStyleInterpolator: ({ current: { progress } }) => {
            const translateY = progress.interpolate({
              inputRange: [0, 1],
              outputRange: [1000, 0], // Modify this range if needed
            });

            return { cardStyle: { transform: [{ translateY }] } };
          },
        }}
      />
    </HomeStack.Navigator>
  );
}
