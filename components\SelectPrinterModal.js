import { StyleSheet, View, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";
import Text from "./Text";
import { Ionicons } from "@expo/vector-icons";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import { ScrollView } from "react-native-gesture-handler";
import Button from "./Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {},
    devicesContainer: {
      height: 400,
      marginTop: 10,
    },
    safeContainer: {
      backgroundColor: "white",
      borderRadius: 20,
      paddingBottom: 50,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
  });
  return styles;
};

const SelectPrinterModal = ({ devices, onClose }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);

  const devicesConnected = devices.filter((device) => device.connected);
  const devicesWithNames = devices
    .filter((device) => device.name && !device.connected)
    .sort((a, b) => a.name.localeCompare(b.name));
  const devicesWithoutNames = devices
    .filter((device) => !device.name && !device.connected)
    .sort((a, b) => a.address.localeCompare(b.address));

  return (
    <View style={styles.container}>
      <View style={styles.safeContainer}>
        <View
          style={{
            width: "100%",
            marginBottom: 10,
            flexDirection: "row",
            justifyContent: "flex-end",
          }}
        >
          <TouchableOpacity onPress={() => onClose(undefined)}>
            <Ionicons name="close" size={35} color={theme.appColors.primary} />
          </TouchableOpacity>
        </View>
        <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
          Detected Bluetooth Devices
        </Text>
        <ScrollView style={styles.devicesContainer}>
          {devicesConnected
            .concat(devicesWithNames, devicesWithoutNames)
            .map((device, index) => {
              return (
                <View
                  key={index}
                  style={{
                    justifyContent: "center",
                    flexDirection: "row",
                    marginBottom: 10,
                    // width: "100%",
                  }}
                >
                  <Button
                    variant="light"
                    size="lg"
                    onPress={() => onClose(device)}
                    style={{
                      backgroundColor: device.connected
                        ? theme.appColors.greenText
                        : theme.appColors.backdropLight,
                    }}
                  >
                    <Text
                      style={{
                        color: device.connected
                          ? theme.appColors.textNeutral
                          : theme.appColors.text,
                      }}
                    >
                      {(device.name ? device.name : device.address).replaceAll(
                        ":",
                        ":\u{200B}"
                      )}
                    </Text>
                  </Button>
                </View>
              );
            })}
        </ScrollView>
      </View>
    </View>
  );
};

SelectPrinterModal.propTypes = {
  devices: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default SelectPrinterModal;
