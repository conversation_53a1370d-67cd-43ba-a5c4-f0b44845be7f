import { Text, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";

import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";

export const Tag = ({ bold, color, expand, highlighted, label, onPress }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  return (
    <TouchableOpacity
      style={{
        alignItems: "center",
        backgroundColor: highlighted ? color || "#0E9BD8" : "transparent",
        marginHorizontal: highlighted ? 4.5 : 3,
        paddingHorizontal: expand ? 0 : 7,
        paddingVertical: 5,
        borderColor: highlighted ? "transparent" : theme.appColors.text,
        borderRadius: 8,
        borderWidth: 1.5,
        flex: expand ? 1 : 0,
      }}
      onPress={onPress}
    >
      <Text
        style={[
          bold ? globalStyles.headlineSmall : globalStyles.bodyLarge,
          highlighted ? globalStyles.textNeutral : globalStyles.text,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

Tag.propTypes = {
  bold: PropTypes.bool,
  color: PropTypes.string,
  expand: PropTypes.bool,
  highlighted: PropTypes.bool,
  label: PropTypes.string.isRequired,
  onPress: PropTypes.func,
};

const TagList = ({ bold, expand, onSelectTag, selectedTag, tags }) =>
  tags.map((tag, index) => (
    <Tag
      bold={bold}
      color={tag.color}
      expand={expand}
      highlighted={selectedTag && selectedTag.index == index}
      key={index}
      label={tag.name}
      onPress={() => onSelectTag(tag, index)}
    />
  ));

TagList.propTypes = {
  bold: PropTypes.bool,
  expand: PropTypes.bool,
  onSelectTag: PropTypes.func.isRequired,
  selectedTag: PropTypes.object,
  tags: PropTypes.array.isRequired,
};

export default TagList;
