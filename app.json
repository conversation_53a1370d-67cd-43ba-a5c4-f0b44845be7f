{
  "expo": {
    "name": "Attain Wholesale",
    "slug": "Attain",
    "version": "682",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "scheme": "joinattain.com",
    "privacy": "unlisted",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#2D8CFF"
    },
    "updates": {
      "fallbackToCacheTimeout": 0,
      "url": "https://u.expo.dev/c9f62a35-03e2-406c-a942-b7421a9ccc39",
      "channel": "staging"
    },
    "assetBundlePatterns": ["**/*"],
    "ios": {
      "buildNumber": "682",
      "icon": "./assets/icon.png",
      "supportsTablet": false,
      "infoPlist": {
        "NSCameraUsageDescription": "$(PRODUCT_NAME) needs access to your Camera in order to scan items/products.",
        "NSMicrophoneUsageDescription": "$(PRODUCT_NAME) needs access to your Microphone.",
        "NSBluetoothPeripheralUsageDescription": "$(PRODUCT_NAME) needs access to your Bluetooth in order to connect to printers.",
        "NSBluetoothAlwaysUsageDescription": "$(PRODUCT_NAME) needs access to your Bluetooth in order to connect to printers.",
        "UISupportedExternalAccessoryProtocols": ["com.zebra.rawport"],
        "UIBackgroundModes": ["external-accessory", "bluetooth-central"]
      },
      "entitlements": {
        "com.apple.external-accessory.wireless-configuration": true
      },
      "bundleIdentifier": "joinattain.com"
    },
    "android": {
      "versionCode": 682,
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      },
      "permissions": [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.BLUETOOTH",
        "android.permission.BLUETOOTH_ADMIN",
        "android.permission.BLUETOOTH_SCAN",
        "android.permission.BLUETOOTH_CONNECT",
        "android.permission.ACCESS_FINE_LOCATION"
      ],
      "package": "joinattain.com",
      "googleServicesFile": "./google-services.json"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    },
    "extra": {
      "eas": {
        "projectId": "c9f62a35-03e2-406c-a942-b7421a9ccc39"
      }
    },
    "plugins": [
      [
        "expo-build-properties",
        {
          "android": {
            "minSdkVersion": 24
          }
        }
      ],
      ["./prevent-dso"],
      // ["./plugins/withZebraSdk"],
      [
        "react-native-permissions",
        {
          "iosPermissions": ["Bluetooth"]
        }
      ],
      "expo-localization",
      "expo-font"
    ],
    "owner": "attain_wholesale",
    "runtimeVersion": "1.0.0"
  }
}
