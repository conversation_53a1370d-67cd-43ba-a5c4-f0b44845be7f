import { Ionicons } from "@expo/vector-icons";
import { useHeaderHeight } from "@react-navigation/elements";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { useRef, useState } from "react";
import {
  KeyboardAvoidingView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Signature from "react-native-signature-canvas";
import useGlobalStyles from "../lib/useGlobalStyles";
import Button from "./Button";
import Text from "./Text";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    safeContainer: {
      backgroundColor: "white",
      borderRadius: 20,
      // flex: 1,
      flexDirection: "column",
      paddingBottom: 50,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    signature: {
      borderBottomLeftRadius: 20,
      borderBottomRightRadius: 20,
      borderColor: theme.appColors.backdropDark,
      borderWidth: 3,
      height: 150,
      overflow: "hidden",
      width: "100%",
    },
    signatureContainer: {
      alignItems: "center",
      backgroundColor: "lightgray",
      borderRadius: 20,
      flexDirection: "column",
      overflow: "hidden",
    },
    signatureNameInput: {
      backgroundColor: "white",
      borderColor: theme.appColors.backdropDark,
      borderLeftWidth: 3,
      borderRightWidth: 3,
      borderTopWidth: 3,
      padding: 10,
      width: "100%",
    },
  });
  return styles;
};

const SignatureModal = ({ onClose, signature, signatureName }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);
  const headerHeight = useHeaderHeight();
  const ref = useRef();
  const [signatureNameInput, setSignatureNameInput] = useState(
    signatureName || ""
  );

  return (
    <KeyboardAvoidingView
      behavior="padding"
      keyboardVerticalOffset={headerHeight}
    >
      <View style={styles.safeContainer}>
        <View
          style={{
            width: "100%",
            marginBottom: 10,
            flexDirection: "row",
            justifyContent: "flex-start",
          }}
        >
          <TouchableOpacity onPress={() => onClose(undefined)}>
            <Ionicons name="close" size={35} color={theme.appColors.primary} />
          </TouchableOpacity>
        </View>
        <View style={styles.signatureContainer}>
          <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
            Signature
          </Text>
          {/* <View
            style={{
              flexDirection: "column",
              alignItems: "center",
              marginVertical: 20,
              paddingHorizontal: 15,
            }}
          >
          </View> */}
          <TextInput
            style={styles.signatureNameInput}
            placeholderTextColor="gray"
            placeholder="Signature Name"
            onChangeText={(text) => {
              setSignatureNameInput(text);
            }}
            value={signatureNameInput}
            autoCorrect={true}
          />
          <View style={styles.signature}>
            <Signature
              webStyle={`.m-signature-pad {box-shadow: none; border: none; } 
              .m-signature-pad--body {border: none;}
              .m-signature-pad--footer {display: none; margin: 0px;}
              body,html { width: "100%"; height: 150px;}`}
              ref={ref}
              onOK={(signatureImgData) =>
                onClose(signatureImgData, signatureNameInput)
              }
              dataURL={signature || ""}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
        {!signature ? (
          <View style={{ flexDirection: "row", gap: 20, marginTop: 20 }}>
            <Button
              variant="light"
              size="md"
              style={{ flex: 1 }}
              onPress={() => {
                ref.current.clearSignature();
                setSignatureNameInput("");
              }}
            >
              <Text>Clear</Text>
            </Button>
            <Button
              variant="light"
              size="md"
              style={{ flex: 1 }}
              onPress={() => {
                ref.current.readSignature();
              }}
              disabled={!signatureNameInput}
            >
              <Text>Confirm</Text>
            </Button>
          </View>
        ) : null}
      </View>
    </KeyboardAvoidingView>
  );
};

SignatureModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  signature: PropTypes.string,
  signatureName: PropTypes.string,
};

export default SignatureModal;
