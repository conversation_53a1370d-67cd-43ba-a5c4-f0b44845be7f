import { StyleSheet, View, TouchableOpacity } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { Icon } from "react-native-elements";
import Text from "./Text.js";

const styles = StyleSheet.create({
  deliveryContent: {
    alignItems: "center",
    borderColor: "black",
    borderRadius: 20,
    borderWidth: 1,
    flexDirection: "row",
    height: 120,
    justifyContent: "center",
    marginHorizontal: 10,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  deliveryText: {
    fontSize: 22,
    fontWeight: "600",
  },
  deliveryTextContainer: {
    width: "40%",
  },
  normalText: {
    color: "gray",
    fontSize: 15,
  },
  statusSection: {
    alignItems: "center",
    flex: 1,
    paddingTop: 10,
  },
  statusText: {
    fontSize: 12,
  },
});

function RadioButton(props) {
  const theme = useTheme();
  return (
    <View
      style={[
        {
          height: 18,
          width: 18,
          borderRadius: 12,
          borderWidth: 2,
          borderColor: theme.appColors.backdropDark,
          alignItems: "center",
          justifyContent: "center",
        },
        props.style,
      ]}
    >
      {props.selected ? (
        <View
          style={{
            height: 18,
            width: 18,
            borderRadius: 12,
            backgroundColor: theme.appColors.backdropDark,
          }}
        />
      ) : null}
    </View>
  );
}

RadioButton.propTypes = {
  selected: PropTypes.bool.isRequired,
  style: PropTypes.object,
};

const DeliveryStatus = ({
  name,
  date,
  status,
  handleSeeDetails,
  orderStatus,
}) => {
  return (
    <TouchableOpacity
      style={styles.deliveryContent}
      onPress={() => handleSeeDetails(orderStatus)}
    >
      <View style={styles.deliveryTextContainer}>
        <Text numberOfLines={2} style={styles.deliveryText}>
          {name}
        </Text>
        <Text style={styles.normalText}>Est. Delivery</Text>
        <Text style={styles.normalText}>{date}</Text>
      </View>
      <View style={styles.statusSection}>
        <RadioButton
          selected={
            status === "Submitted" ||
            status === "Delivering" ||
            status === "Delivered"
          }
        />
        <Icon name="done" />
        <Text style={styles.statusText}>Submitted</Text>
      </View>
      <View style={styles.statusSection}>
        <RadioButton
          selected={status === "Delivered" || status === "Delivering"}
        />
        <Icon name="truck" type="font-awesome" />
        <Text style={styles.statusText}>Delivering</Text>
      </View>
      <View style={styles.statusSection}>
        <RadioButton selected={status === "Delivered"} />
        <Icon name="archive" type="evilicon" size={32} />
        <Text style={styles.statusText}>Delivered</Text>
      </View>
    </TouchableOpacity>
  );
};

DeliveryStatus.propTypes = {
  date: PropTypes.string.isRequired,
  handleSeeDetails: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  orderStatus: PropTypes.object.isRequired,
  status: PropTypes.string.isRequired,
};

export default DeliveryStatus;
