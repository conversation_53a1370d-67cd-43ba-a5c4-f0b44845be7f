import { useContext, useState } from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import Modal from "react-native-modal";
import { useNavigation, useRoute } from "@react-navigation/native";

import { UserContext } from "../../context/userContext";
import AddItemModal from "../AddItemModal";
import ItemCardButton from "./ItemCardButton";
import ItemCardTag from "./ItemCardTag";
import useGlobalStyles from "../../lib/useGlobalStyles";
import formatDate from "../../lib/formatDate";
import { posthog } from "../../src/posthog";
import { useTranslation } from "react-i18next";
import { getUomQuantity } from "../../lib/uomHelpers";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    buttonContainer: {
      position: "absolute",
      zIndex: 5,
    },
    imageContainer: {
      marginBottom: 12,
      marginTop: 15,
    },
    itemImage: {
      aspectRatio: 1,
      width: "75%",
    },
    lastOrderedContainer: {
      alignItems: "center",
      backgroundColor: theme.appColors.disabled,
      height: 30,
      justifyContent: "center",
    },
    modal: {
      justifyContent: "flex-end",
      margin: 0,
    },
    recommendationRank: {
      alignContent: "center",
      backgroundColor: theme.appColors.accent,
      borderRadius: 15,
      height: 30,
      justifyContent: "center",
      paddingLeft: 1,
      position: "absolute",
      shadowBlur: 2,
      shadowColor: "black",
      shadowOffset: { height: 1, width: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 0.5,
      width: 30,
      zIndex: 5,
    },
    recommendationRankText: {
      textAlign: "center",
    },
    topContainer: {
      left: 0,
      position: "absolute",
      right: 0,
      top: 0,
      zIndex: 6,
    },
  });
  return styles;
};

const ItemCardContainer = ({
  buttonPosition,
  children,
  item,
  style,
  disabled,
  section,
  recommendedRank,
  userApproved,
  onPress,
  showButton = true,
}) => {
  const { user } = useContext(UserContext);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  const [modalVisible, setModalVisible] = useState(false);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();

  const itemClicked = (_) => {
    navigation.navigate("ItemDetail", {
      id: item.id,
    });
  };

  const handlePress = onPress || itemClicked;

  const addItemToCart = () => {
    posthog.capture("add_item_simple_clicked", {
      screen: route.name,
      itemId: item.id,
      section,
    });
    setModalVisible(true);
  };

  const { cartItems } = user.cart;
  const getItemQuantityInCart = () => {
    const checkItemId = (cartItem) => cartItem.item_id == item.id;
    if (cartItems.some(checkItemId)) {
      const cartItem = cartItems.find(
        (cartItem) => cartItem.item_id == item.id
      );

      // Calculate UOM-adjusted display quantity
      const uomQuantity = cartItem?.item_uom_id
        ? getUomQuantity(cartItem, cartItem.item_uom_id)
        : 1;
      const displayQuantity = Number(
        (cartItem.quantity / uomQuantity).toFixed(2)
      );
      return displayQuantity;
    }
    return 0;
  };

  const supplier = user.suppliers.find(
    (supplier) => supplier.name === item.supplier
  );

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={() => handlePress(item)}
      style={style}
    >
      <View style={styles.topContainer}>
        {!recommendedRank && item.last_ordered_date && (
          <View style={styles.lastOrderedContainer}>
            <Text style={[globalStyles.bodySmall, globalStyles.text]}>
              {t("item.ordered_on") +
                " " +
                formatDate(new Date(item.last_ordered_date), "/")}
            </Text>
          </View>
        )}
        {recommendedRank && (
          <View style={styles.recommendationRank}>
            <Text
              style={[
                globalStyles.headlineLarge,
                globalStyles.textNeutral,
                styles.recommendationRankText,
              ]}
            >
              {recommendedRank}
            </Text>
          </View>
        )}
        <ItemCardTag item={item} />
      </View>
      <Modal
        animationIn="slideInUp"
        isVisible={modalVisible}
        hasBackdrop
        onBackdropPress={() => setModalVisible(false)}
        onRequestClose={() => setModalVisible(!modalVisible)}
        style={styles.modal}
      >
        <AddItemModal item={item} onClose={() => setModalVisible(false)} />
      </Modal>
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri:
              item.image ||
              supplier?.config?.defaultItemImage ||
              "https://fakeimg.pl/100x100?text=no+image",
          }}
          style={styles.itemImage}
        />
        {showButton && userApproved && !disabled && (
          <View style={[styles.buttonContainer, buttonPosition]}>
            <ItemCardButton
              onPress={addItemToCart}
              quantity={getItemQuantityInCart()}
            />
          </View>
        )}
      </View>
      {children}
    </TouchableOpacity>
  );
};

ItemCardContainer.propTypes = {
  buttonPosition: PropTypes.object.isRequired,
  children: PropTypes.node.isRequired,
  item: PropTypes.object.isRequired,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]).isRequired,
  section: PropTypes.string,
  disabled: PropTypes.bool,
  recommendedRank: PropTypes.number,
  userApproved: PropTypes.bool,
  onPress: PropTypes.func,
  showButton: PropTypes.bool,
};

export default ItemCardContainer;
