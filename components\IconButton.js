import { Image, StyleSheet, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";
import { useNavigation, useTheme } from "@react-navigation/native";

import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";
import { posthog } from "../src/posthog";

const styles = StyleSheet.create({
  centered: {
    marginTop: 25,
  },
  container: {
    alignItems: "center",
    marginVertical: 10,
    paddingHorizontal: 4,
    width: 80,
  },
  detail: {
    lineHeight: 16,
    textAlign: "center",
  },
  icon: {
    height: 40,
    width: 40,
  },
  label: {
    lineHeight: 16,
    marginTop: 10,
    textAlign: "center",
  },
});

const IconButton = ({ label, onPress, uri, minimum }) => {
  const globalStyles = useGlobalStyles();

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      {uri && <Image source={{ uri }} style={styles.icon} />}
      {label && (
        <Text
          numberOfLines={1}
          style={[
            globalStyles.titleLarge,
            globalStyles.text,
            styles.label,
          ].concat(uri ? [] : [styles.centered])}
        >
          {label}
        </Text>
      )}

      <Text
        numberOfLines={2}
        style={[
          globalStyles.bodyMedium,
          globalStyles.text,
          styles.detail,
        ].concat(uri ? [] : [styles.centered])}
      >
        {minimum ? `$${minimum} min.` : " "}
      </Text>
    </TouchableOpacity>
  );
};

IconButton.propTypes = {
  label: PropTypes.string.isRequired,
  onPress: PropTypes.func,
  uri: PropTypes.string,
};

export const AllVendorsButton = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const globalStyles = useGlobalStyles();

  return (
    <TouchableOpacity
      onPress={() => {
        posthog.capture("all_vendors_button_clicked");
        navigation.navigate("AllVendors");
      }}
      style={styles.container}
    >
      <Image source={theme.assets.allVendors} style={styles.icon} />
      <Text
        style={[globalStyles.labelMedium, globalStyles.textLight, styles.label]}
      >
        All Vendors
      </Text>
    </TouchableOpacity>
  );
};

export default IconButton;
