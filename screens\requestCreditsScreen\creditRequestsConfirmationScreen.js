import { useState, useContext } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  Text,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { UserContext } from "../../context/userContext";
import OrderSupplierIcon from "../../components/OrderSupplierIcon";
import Header from "../../components/Header";
import Button from "../../components/Button";
import OrderItemWithoutPrice from "../../components/OrderItemWithoutPrice";
import CreditRequestReasons from "../../components/CreditRequestReasons";
import { useSubmitCreditRequestsMutation } from "../../generated/graphql";
import Modal from "react-native-modal";

const styles = StyleSheet.create({
  headerContainer: {
    alignItems: "center",
    paddingBottom: 20,
    paddingHorizontal: 20,
  },

  list: {
    backgroundColor: "white",
    height: "100%",
  },

  orderNameContainer: {
    alignItems: "center",
    flexDirection: "row",
    paddingBottom: 6,
  },
  orderSummaryView: {
    alignSelf: "stretch",
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
  },
});

export default function CreditRequestsConfirmationScreen({
  navigation,
  route,
}) {
  const { user } = useContext(UserContext);
  const { items, order } = route.params;

  const globalStyles = useGlobalStyles();
  const [submitCreditRequests] = useSubmitCreditRequestsMutation();
  const [modalOpen, setIsModalOpen] = useState(false);
  const confirmSubmitCreditRequests = async () => {
    console.log(items);
    await submitCreditRequests({
      variables: {
        submitCreditRequestsInput: {
          supplier: order.supplier,
          userId: user.id,
          orderId: order.id,
          creditRequests: items.map((item) => ({
            itemId: item.item_id,
            quantity: item.quantityAffected,
            isDamaged: item.isDamaged,
            isExpired: item.isExpired,
            isMispick: item.isMispick,
            pricePurchasedAt: item.price_purchased_at,
          })),
        },
      },
    });
    setIsModalOpen(true);
    setTimeout(() => {
      setIsModalOpen(false);
      navigation.pop(2);
    }, 1500);
  };

  const renderItem = ({ item, index }) => {
    const reasons = [];
    if (item.isMispick) reasons.push("mispick");
    if (item.isDamaged) reasons.push("damaged");
    if (item.isExpired) reasons.push("expired");

    return (
      <View
        style={{
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          marginHorizontal: 10,
          marginBottom: 50,
        }}
      >
        <View>
          <CreditRequestReasons reasons={reasons} />
          <OrderItemWithoutPrice item={item} />
        </View>
        <View
          style={{
            flexDirection: "column",
            alignItems: "center",
            marginLeft: 30,
          }}
        >
          <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
            QTY
          </Text>
          <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
            Affected
          </Text>
          <Text
            style={[
              globalStyles.headlineSmall,
              globalStyles.text,
              { marginTop: 10 },
            ]}
          >
            {item.quantityAffected}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <Header goBack={navigation.goBack} title="Check In" />
      <Modal animationIn="slideInUp" isVisible={modalOpen} hasBackdrop>
        <View
          style={{
            backgroundColor: "white",
            borderRadius: 20,
            justifyContent: "center",
            alignItems: "center",
            shadowColor: "black",
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
            height: "10%",
          }}
        >
          <Text style={[globalStyles.headlineLarge, globalStyles.text]}>
            Credit request submitted!
          </Text>
        </View>
      </Modal>
      <View style={{ flex: 1 }}>
        <FlatList
          ListHeaderComponent={
            <>
              <View style={styles.headerContainer}>
                <View style={styles.orderSummaryView}>
                  <View>
                    <TouchableOpacity style={styles.orderNameContainer}>
                      <TextInput
                        style={[globalStyles.headlineLarge, globalStyles.text]}
                        defaultValue={`Order #${order.order_number}`}
                        maxLength={32}
                        numberOfLines={1}
                      />
                    </TouchableOpacity>
                    <View>
                      <OrderSupplierIcon order={order} dark />
                    </View>
                  </View>
                </View>
              </View>
            </>
          }
          data={items}
          numColumns={1}
          renderItem={renderItem}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 80 }}
          keyExtractor={(item) => item.id}
          style={styles.list}
        />

        <Button
          size="lg"
          style={{
            position: "absolute",
            bottom: 20,
            alignSelf: "center",
          }}
          onPress={confirmSubmitCreditRequests}
        >
          <Text>Continue</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}
