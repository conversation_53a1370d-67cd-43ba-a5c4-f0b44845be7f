query GetCarts($getCartsInput: GetCartsInput!) {
carts(getCartsInput: $getCartsInput) {
    id
    cartItems {
        id
        image
        item_id
        nacs_category
        nacs_subcategory
        name
        oos
        price
        discounted_price
        custom_price
        quantity
        supplier
        supplier_code
        unit_size
        qoh
        upc1
        upc2
        metadata
        item_uom_id
        uoms {
          id
          name
          supplier_id
          uom_id
          quantity
          item_id
          price
          upc
          archived
        }
        crv
    }
    subCarts {
      cartItems {
        id
        image
        item_id
        nacs_category
        nacs_subcategory
        name
        oos
        price
        discounted_price
        custom_price
        quantity
        supplier
        supplier_code
        unit_size
        qoh
        upc1
        upc2
        metadata
        crv
        item_uom_id
        uoms {
          id
          name
          supplier_id
          uom_id
          quantity
          item_id
          price
          upc
          archived
        }
      }
      supplier
    }
  }
}
