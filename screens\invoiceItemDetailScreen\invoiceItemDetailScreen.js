import { useState, useContext, useMemo } from "react";
import {
  StyleSheet,
  View,
  StatusBar,
  TouchableOpacity,
  Image,
  ScrollView,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import CheckBox from "@react-native-community/checkbox";
import Text from "../../components/Text";
import { useReconcileInvoiceWithItemMutation } from "../../generated/graphql";
import { UserContext } from "../../context/userContext";
import Header from "../../components/Header";
import { useTheme } from "@react-navigation/native";
import useGlobalStyles from "../../lib/useGlobalStyles";
import Button from "../../components/Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 15,
    },
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
    },
    buttonContainer: {
      marginBottom: 0,
      marginTop: 10,
    },
    buttonText: {
      color: theme.appColors.surface,
      fontWeight: "bold",
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    errorContainer: {},
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    itemCountContainer: {
      alignItems: "center",
      borderColor: theme.appColors.textLight,
      borderRadius: 5,
      borderWidth: 0.5,
      flexDirection: "row",
      justifyContent: "center",
      paddingHorizontal: 10,
      paddingVertical: 5,
    },
    itemDetailLine: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 120,
      width: 120,
    },
    itemTextContainer: {
      flex: 1,
    },
    lastOrderedContainer: {
      marginTop: 20,
    },
    mainButton: {
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 20,
      color: "white",
    },
    mainButtonText: {
      color: "white",
      fontSize: 20,
    },
    minusButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.textLight,
      borderColor: theme.appColors.backdropLight,
      borderRadius: 5,
      borderWidth: 1,
      display: "flex",
      height: 25,
      justifyContent: "center",
      width: 25,
    },
    originalPriceText: {
      color: theme.appColors.redText,
    },
    plusButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.textLight,
      borderColor: theme.appColors.backdropLight,
      borderRadius: 5,
      borderWidth: 1,
      display: "flex",
      height: 25,
      justifyContent: "center",
      width: 25,
    },
    pricingContainer: {
      marginBottom: 30,
      marginTop: 20,
    },
    quantityContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      marginBottom: 30,
      marginTop: 50,
    },
    quantityText: {
      color: theme.appColors.textLight,
      fontSize: 15,
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
    safeContainer: {
      backgroundColor: "white",
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
  });
  return styles;
};

export default function InvoiceItemDetailScreen({
  navigation,
  route,
  presetQuantity,
}) {
  const { user } = useContext(UserContext);
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { invoiceId, invoiceItem } = route.params;
  const [quantity, setQuantity] = useState(
    invoiceItem.checked_in_quantity || invoiceItem.quantity
  );
  const [checked, setChecked] = useState(invoiceItem.is_mispick);
  const [reconcileInvoiceWithItem] = useReconcileInvoiceWithItemMutation();
  const checkInItem = async () => {
    const result = await reconcileInvoiceWithItem({
      variables: {
        reconcileInvoiceWithItemInput: {
          invoice_id: invoiceId,
          invoice_item_id: invoiceItem.id,
          quantity: quantity,
          is_mispick: checked,
          checked_in: !checked,
        },
      },
    });

    navigation.navigate("ActionItemsScreen", {
      invoiceItemId: invoiceItem.id,
      invoiceId: invoiceId,
    });
  };

  const onEditQuantity = (quantity) => {
    setQuantity(quantity);
  };
  return (
    <SafeAreaView style={globalStyles.container}>
      <StatusBar
        backgroundColor={theme.appColors.primary}
        barStyle="light-content"
      />
      <Header goBack={navigation.goBack} title={"Edit Quantity"} />
      <View style={styles.safeContainer}>
        <View style={styles.itemContainer}>
          <View style={styles.itemTextContainer}>
            <Text
              style={[
                globalStyles.headlineMedium,
                { textDecorationLine: "underline" },
                globalStyles.text,
                { marginBottom: 8 },
              ]}
            >
              {/* {invoiceItem.supplier} */}
            </Text>
            <Text
              style={[
                globalStyles.headlineMedium,
                globalStyles.text,
                { marginBottom: 20 },
              ]}
            >
              {invoiceItem.name}
            </Text>
            <View style={styles.itemDetailLine}>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                UPC
              </Text>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                {invoiceItem.upc1}
              </Text>
            </View>
            <View style={styles.itemDetailLine}>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                Unit Size
              </Text>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                {invoiceItem.unit_size} ct
              </Text>
            </View>
            <View style={styles.itemDetailLine}>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                Net Weight
              </Text>
              <Text style={[globalStyles.bodyLarge, globalStyles.textLight]}>
                {invoiceItem.size}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.quantityContainer}>
          <View style={styles.itemCountContainer}>
            <TouchableOpacity
              style={styles.minusButton}
              onPress={() => onEditQuantity(quantity - 1)}
            >
              <Text style={styles.buttonText}>-</Text>
            </TouchableOpacity>

            <Text style={styles.quantityText}>
              {quantity}/{invoiceItem.quantity}
            </Text>

            <TouchableOpacity
              style={styles.plusButton}
              onPress={() => onEditQuantity(quantity + 1)}
            >
              <Text style={styles.buttonText}>+</Text>
            </TouchableOpacity>
          </View>

          <View
            style={{
              flexDirection: "column",
              justifyContent: "center",
              flexWrap: "wrap",
              verticalAlign: "middle",
              marginLeft: 10,
            }}
          >
            <View style={{ flexDirection: "row" }}>
              <CheckBox
                boxType="square"
                onFillColor={theme.appColors.primary}
                onCheckColor={theme.appColors.surface}
                tintColor={theme.appColors.primary}
                onTintColor={theme.appColors.primary}
                animationDuration={0}
                style={{ width: 20, height: 20, marginRight: 10 }}
                onValueChange={(value) => {
                  setChecked(value);
                }}
                value={checked}
              />
              <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
                Is Mispick
              </Text>
            </View>
          </View>
        </View>
        <View style={{ justifyContent: "center", alignItems: "center" }}>
          <Button
            variant="light"
            size="lg"
            onPress={checkInItem}
            style={{ marginTop: 20 }}
          >
            <Text>Check In</Text>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}
