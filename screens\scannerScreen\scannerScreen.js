import React, { useEffect, useRef, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AppState } from "react-native";
import {
  BarcodeCapture,
  BarcodeCaptureOverlay,
  BarcodeCaptureOverlayStyle,
  BarcodeCaptureSettings,
  Symbology,
  SymbologyDescription,
} from "scandit-react-native-datacapture-barcode";
import {
  Camera,
  CameraSettings,
  DataCaptureContext,
  DataCaptureView,
  FrameSourceState,
  RectangularViewfinder,
  RectangularViewfinderStyle,
  RectangularViewfinderLineStyle,
  VideoResolution,
  Feedback,
} from "scandit-react-native-datacapture-core";
import { Camera as ExpoCamera, CameraType } from "expo-camera";
import { useIsFocused } from "@react-navigation/native";
import { posthog } from "../../src/posthog";
import {
  useGetItemsLazyQuery,
  useGetItemsBySupplierLazyQuery,
} from "../../generated/graphql";
import { useContext } from "react";
import { UserContext } from "../../context/userContext";

const requestCameraPermissionsIfNeeded = async () => {
  try {
    const { status } = await ExpoCamera.requestCameraPermissionsAsync();
    if (status !== "granted") {
      throw Error("asdasdasd");
    }
    if (status === "granted") {
      return Promise.resolve();
    }
  } catch (error) {
    throw Error("here");
  }
};

const ScannerScreen = ({ navigation, route }) => {
  const supplier = route && route.params && route.params.supplier;
  const type = route && route.params && route.params.type;
  const invoiceId = route && route.params && route.params.invoiceId;
  const resultScreen =
    type === "invoiceItem" ? "InvoiceItemScanResultScreen" : "ScanResult";
  const params = type === "invoiceItem" ? { invoiceId } : { supplier };
  const viewRef = useRef(null);
  const licenseKey =
    "AfUjWNKBRgP5FdlPFQDhBu4V3j5LEttyEFbMaWYoZ0iMXx41pUGAzMlRc1OOYsqU+W24Cctp45PlYFexvgtGl6dsUXonKWCh6EBCtJNfvFflVwQ/FgOvBqMWUwfWAcb+AgLic7Nzrth4fGrAdXSDOjcpBQZ46mtHz/FW2NEnXZzUivCN/6NNXklP20eNIrtalgvRFDAzGwi3nbVVfpiLItmCRj+JAsSuQblSAxSkNAHt4mXVEN5P5CrbRF6e1AiJwO6aE0UHVsbStgm3iRwfz9pc3c/X7nkLO65Kf9YM1xuTaM4T1JC4gEVeqfxXltGY3OY+YLCPBHwQTVfulCUg2Lm7WsqciZsHcS0Lcq6clDgXKLefrHXGdU/0YqAq9AmvdrW1PmJf4L1e6sD55IGzg6Jk9dMZTl+WDx6X6RfcIABlm4VBs1bjOZtU1HxDUSjf5BHyqDTPITJjbIqUj6eCX2aK+XTd3Pe8/PEqRNEV5ByxBPwmWARlG32mlVFXC5jOk+GmbNS1XdUefEygXGjYMviiqc0T0YTm/p1YP4TIB/T3qf08WshwrDhGuV4QyBY0F9w5xGqeFPq4+9jmLaoHhXKrU5ICC1e4AzsxPtKYj9Fsl3OFG8+aEPavDa5FLLnjzI873B3+JpZyDV28NfBJSotMkasOq6mW/NCdYCWGxMkLlq89yv9U1qkSIx6ieLvqGWwqk1ybtxewTByyxdiIkETWjE8Zt9TxyVOipke6EslHem+05vC9uQx9vWkHND2O7mAbBsj9zaF8lF9oEThbL7Hq1YalArXDsKhQWgyRjCcotdqxvA==";

  const dataCaptureContext = useMemo(() => {
    // There is a Scandit sample license key set below here.
    // This license key is enabled for sample evaluation only.
    // If you want to build your own application, get your license key
    // by signing up for a trial at https://ssl.scandit.com/dashboard/sign-up?p=test
    return DataCaptureContext.forLicenseKey(licenseKey);
  }, []);

  const { user } = useContext(UserContext);
  const [camera, setCamera] = useState(null);
  const [barcodeCaptureMode, setBarcodeCaptureMode] = useState(null);
  const [isBarcodeCaptureEnabled, setIsBarcodeCaptureEnabled] = useState(false);
  const [getItems] = useGetItemsLazyQuery();
  const [getItemsBySupplier] = useGetItemsBySupplierLazyQuery();

  const [cameraState, setCameraState] = useState(FrameSourceState.Off);

  useEffect(() => {
    (async () => {
      const { status } = await ExpoCamera.requestCameraPermissionsAsync();
      if (status === "granted") {
        posthog.capture("scanning_screen_viewed");
        setupScanning();
        startCapture();
        navigation.addListener("focus", () => {
          setIsBarcodeCaptureEnabled(true);
          setCameraState(FrameSourceState.On);
        });
        navigation.addListener("blur", () => {
          setIsBarcodeCaptureEnabled(false);
          setCameraState(FrameSourceState.Off);
        });
      }
    })();

    return () => {
      dataCaptureContext.dispose();
    };
  }, []);

  useEffect(() => {
    if (camera) {
      camera.switchToDesiredState(cameraState);
    }
    return () => {
      if (camera && !viewRef.current) {
        camera.switchToDesiredState(FrameSourceState.Off);
      }
    };
  }, [cameraState]);

  useEffect(() => {
    if (barcodeCaptureMode) {
      barcodeCaptureMode.isEnabled = isBarcodeCaptureEnabled;
    }
    return () => {
      if (barcodeCaptureMode && !viewRef.current) {
        barcodeCaptureMode.isEnabled = false;
      }
    };
  }, [isBarcodeCaptureEnabled]);

  const fetchAndHandleItem = async (upcCode, supplier) => {
    try {
      const bySupplier = supplier && supplier.id;

      let response;
      if (bySupplier) {
        response = await getItemsBySupplier({
          variables: {
            getItemsBySupplierInput: {
              supplierId: supplier.id,
              userId: user.id,
              upc: upcCode,
              pagination: {
                limit: 1,
                offset: 0,
              },
            },
          },
        });
      } else {
        response = await getItems({
          variables: {
            getItemsInput: {
              userId: user.id,
              upcs: [upcCode],
              pagination: {
                limit: 1,
                offset: 0,
              },
            },
          },
        });
      }

      const items = bySupplier
        ? response.data.itemsBySupplier
        : response.data.items;

      if (!items || items.length === 0) {
        // Alert.alert(
        //   "Item Not Found",
        //   "Sorry, we can't find this item. Try searching for it instead.",
        //   [{ text: "OK" }]
        // );
        return null;
      }

      const mySupplierItems = items.filter((item) =>
        user.suppliers.map((supplier) => supplier.name).includes(item.supplier)
      );

      if (user.supplier_beta && mySupplierItems.length === 0) {
        // Alert.alert(
        //   "Item Not Found",
        //   "Sorry, we can't find this item from your suppliers.",
        //   [{ text: "OK" }]
        // );
        return null;
      }

      return items[0];
    } catch (error) {
      // Alert.alert(
      //   "Error",
      //   error.message || "An error occurred while fetching the item.",
      //   [{ text: "OK" }]
      // );
      return null;
    }
  };

  const setupScanning = () => {
    const settings = new BarcodeCaptureSettings();

    // The settings instance initially has all types of barcodes (symbologies) disabled. For the purpose of this
    // sample we enable a very generous set of symbologies. In your own app ensure that you only enable the
    // symbologies that your app requires as every additional enabled symbology has an impact on processing times.
    settings.enableSymbologies([
      Symbology.EAN13UPCA,
      Symbology.EAN8,
      Symbology.UPCE,
      Symbology.Code128,
    ]);

    settings.codeDuplicateFilter = 1000000;

    // Some linear/1d barcode symbologies allow you to encode variable-length data. By default, the Scandit
    // Data Capture SDK only scans barcodes in a certain length range. If your application requires scanning of one
    // of these symbologies, and the length is falling outside the default range, you may need to adjust the
    // 'active symbol counts' for this symbology. This is shown in the following few lines of code for one of the
    // variable-length symbologies.
    const symbologySettings = settings.settingsForSymbology(Symbology.Code39);
    symbologySettings.activeSymbolCounts = [
      7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    ];

    // Create new barcode capture mode with the settings from above.
    const barcodeCaptureMode = BarcodeCapture.forContext(
      dataCaptureContext,
      settings
    );

    const barcodeCaptureListener = {
      didScan: async (_, session) => {
        const barcode = session.newlyRecognizedBarcodes[0];
        const symbology = new SymbologyDescription(barcode.symbology);

        setIsBarcodeCaptureEnabled(false);

        const item = await fetchAndHandleItem(barcode.data, supplier);

        if (item) {
          const detailScreen =
            type === "invoiceItem" ? "InvoiceItemDetailScreen" : "ItemDetail";

          navigation.navigate(detailScreen, {
            id: item.id,
          });
        } else {
          navigation.navigate(resultScreen, {
            ...params,
            upcCode: barcode.data,
          });
        }
      },
    };

    barcodeCaptureMode.addListener(barcodeCaptureListener);

    const barcodeCaptureOverlay =
      BarcodeCaptureOverlay.withBarcodeCaptureForViewWithStyle(
        barcodeCaptureMode,
        null,
        BarcodeCaptureOverlayStyle.Frame
      );

    barcodeCaptureOverlay.viewfinder = new RectangularViewfinder(
      RectangularViewfinderStyle.Square,
      RectangularViewfinderLineStyle.Light
    );
    viewRef.current.addOverlay(barcodeCaptureOverlay);
    setBarcodeCaptureMode(barcodeCaptureMode);
  };

  const startCapture = () => {
    startCamera();
    setIsBarcodeCaptureEnabled(true);
  };

  const startCamera = () => {
    if (!camera) {
      // Use the world-facing (back) camera and set it as the frame source of the context. The camera is off by
      // default and must be turned on to start streaming frames to the data capture context for recognition.
      const camera = Camera.default;
      dataCaptureContext.setFrameSource(camera);

      const cameraSettings = new CameraSettings();
      cameraSettings.preferredResolution = VideoResolution.UHD4K;
      camera.applySettings(cameraSettings);
      setCamera(camera);
    }

    setCameraState(FrameSourceState.On);
    requestCameraPermissionsIfNeeded()
      .then(() => setCameraState(FrameSourceState.On))
      .catch(() => BackHandler.exitApp());
  };

  return (
    <DataCaptureView
      style={{ flex: 1 }}
      context={dataCaptureContext}
      ref={viewRef}
    />
  );
};

export default ScannerScreen;
