import { StyleSheet, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { useContext } from "react";
import { UserContext } from "../../context/userContext";
import Text from "../Text";
import Highlight from "../Highlight";
import { discountExists } from "../../lib/discountExists";
import { getPrice } from "../../lib/getPrice";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    name: {
      lineHeight: 14,
      paddingTop: 2,
    },
    originalPriceText: {
      color: theme.appColors.redText,
    },
    recommendedSold: {
      color: theme.appColors.accent,
      marginTop: 10,
      textAlign: "center",
    },
  });
  return styles;
};

// Helper function to format unit size
const formatUnitSize = (unitSize) => {
  if (!unitSize) return "";
  // Check if unit_size is just numeric (only digits, possibly with decimal)
  const isNumeric = /^\d+(\.\d+)?$/.test(unitSize.toString().trim());
  return isNumeric ? `${unitSize} ct` : unitSize;
};

const ItemCardText = ({
  highlighted,
  item,
  largeStyle,
  smallStyle,
  recommendedSold,
  userApproved,
  customPrices,
}) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);
  const { user } = useContext(UserContext);
  const item_price = getPrice(item, customPrices);

  const itemName = highlighted ? (
    <>
      {item.metadata ? (
        <Text>
          {"("}
          <Highlight attribute="metadata" hit={item} />
          {") "}
        </Text>
      ) : null}
      <Highlight attribute="name" hit={item} />
    </>
  ) : (
    `${item.metadata ? "(" + item.metadata + ") " : ""}${item.name}`
  );

  if (!discountExists(item)) {
    return (
      <>
        <Text
          numberOfLines={2}
          style={[largeStyle, globalStyles.text, styles.name]}
        >
          {itemName}
        </Text>
        {userApproved && user.show_prices ? (
          <Text style={[smallStyle, globalStyles.text]}>${item_price}</Text>
        ) : null}
        <Text style={[smallStyle, globalStyles.textLight]}>
          {formatUnitSize(item.unit_size)}
        </Text>
        {recommendedSold !== undefined && recommendedSold !== null && (
          <Text
            style={[largeStyle, theme.appColors.accent, styles.recommendedSold]}
          >
            {recommendedSold} sold last week
          </Text>
        )}
      </>
    );
  } else if (discountExists(item)) {
    return (
      <>
        <Text
          numberOfLines={2}
          style={[largeStyle, globalStyles.text, styles.name]}
        >
          {itemName}
        </Text>
        {userApproved && user.show_prices ? (
          <View
            style={{
              flexDirection: "row",
              flexWrap: "wrap",
              alignItems: "flex-start",
            }}
          >
            <Text
              style={[
                { color: theme.appColors.accent },
                globalStyles.titleLarge,
              ]}
            >
              ${item_price}{" "}
            </Text>
            <Text
              style={[
                { textDecorationLine: "line-through" },
                smallStyle,
                globalStyles.text,
                styles.originalPriceText,
              ]}
            >
              ${parseFloat(item.price).toFixed(2)}
            </Text>
          </View>
        ) : null}
        <Text style={[smallStyle, globalStyles.textLight]}>
          {formatUnitSize(item.unit_size)}
        </Text>
        {recommendedSold !== undefined && recommendedSold !== null && (
          <Text style={[largeStyle, styles.recommendedSold]}>
            {recommendedSold} sold last week
          </Text>
        )}
      </>
    );
  }
};

ItemCardText.propTypes = {
  highlighted: PropTypes.bool,
  item: PropTypes.object.isRequired,
  largeStyle: PropTypes.object.isRequired,
  smallStyle: PropTypes.object.isRequired,
  recommendedSold: PropTypes.number,
  userApproved: PropTypes.bool,
  customPrices: PropTypes.array,
};

export default ItemCardText;
