query GetOrdersInfo($ordersV2Input: OrdersInputV2!) {
  ordersV2(ordersInput: $ordersV2Input) {
    orders {
        customerDetails {
            id
            name
        }
        id
        status
        subtotal
        config
        delivery_date
        invoice {
          id
          payment_status
          paid
          subtotal
          total
          config
        }
      }
    }
  }
