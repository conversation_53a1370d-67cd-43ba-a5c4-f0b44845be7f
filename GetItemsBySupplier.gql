query GetItemsBySupplier($getItemsBySupplierInput: GetItemsBySupplierInput) {
  itemsBySupplier(getItemsBySupplierInput: $getItemsBySupplierInput) {
    id
    name
    unit_size
    size
    price
    discounted_price
    upc1
    upc2
    nacs_category
    nacs_subcategory
    image
    last_ordered_date
    avg_cases_per_week
    supplier
    qoh
    qty_on_hand
    oos
    created_at
    metadata
    moq
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    min_sale_price
    isFavorited
    back_in_stock_date
  }
}
