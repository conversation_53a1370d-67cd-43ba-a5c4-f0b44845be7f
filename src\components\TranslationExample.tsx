import React from "react";
import { View, Text, Button } from "react-native";
import { useTranslation } from "react-i18next";

export const TranslationExample = () => {
  const { t, i18n } = useTranslation();

  // Function to change language
  const toggleLanguage = () => {
    const newLang = i18n.language === "en" ? "es" : "en";
    i18n.changeLanguage(newLang);
  };

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 24, marginBottom: 20 }}>
        {t("common.welcome")}
      </Text>

      <View style={{ marginBottom: 20 }}>
        <Text>{t("auth.email")}</Text>
        <Text>{t("auth.password")}</Text>
      </View>

      <Button
        title={t("common.language", "Change Language")}
        onPress={toggleLanguage}
      />
    </View>
  );
};
