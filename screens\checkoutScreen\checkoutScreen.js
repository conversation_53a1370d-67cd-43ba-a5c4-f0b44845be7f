import { useTheme } from "@react-navigation/native";
import { useContext, useMemo, useState } from "react";
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from "react-native";
import CheckoutItem from "../../components/CheckoutItem";
import CheckoutSummary from "../../components/CheckoutSummary";
import Header from "../../components/Header";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import OrderSummary from "../../components/OrderSummary";
import SafeAreaView from "../../components/SafeAreaView";
import { UserContext } from "../../context/userContext";
import {
  useGetAccountsQuery,
  useSubmitOrderMutation,
} from "../../generated/graphql";
import { getCustomPrice, getPrice } from "../../lib/getPrice";
import { getTotal } from "../../lib/getTotal";
import { posthog } from "../../src/posthog";
import { getUomQuantity } from "../../lib/uomHelpers";
import dayjs from "dayjs";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldMainText: {
      color: theme.appColors.primary,
      fontSize: 20,
      fontWeight: "700",
    },
    container: {
      backgroundColor: theme.appColors.primary,
      flex: 1,
    },
    itemsHeader: {
      backgroundColor: "white",
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    scrollView: {
      backgroundColor: "white",
      flexGrow: 1,
      paddingHorizontal: 20,
    },
  });
  return styles;
};

export default function CheckoutScreen({ navigation, route }) {
  const { user, orderRoute } = useContext(UserContext);
  const [submitOrder] = useSubmitOrderMutation();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const [loadingState, setLoadingState] = useState(false);

  const { loading, data, error } = useGetAccountsQuery({
    fetchPolicy: "network-only",
    variables: {
      businessId: user.id,
    },
    pollInterval: 500,
  });

  const { supplier, imageUri, isCredit } = route.params;
  const cart = user.cart;
  const subCart = cart.subCarts.find((cart) => cart.supplier === supplier) || {
    cartItems: [],
  };
  const [notes, setNotes] = useState(null);
  const supplierObj = user.suppliers.find(
    (element) => element.name === supplier
  );
  const [deliveryDate, setDeliveryDate] = useState(null);

  // Calculate UOM-adjusted item count
  const getUomAdjustedItemCount = () => {
    if (supplier === "C&G Snacks") {
      return subCart.cartItems.reduce((acc, cartItem) => {
        const uomQuantity = cartItem?.item_uom_id
          ? getUomQuantity(cartItem, cartItem.item_uom_id)
          : 1;
        const displayQuantity = Number(
          (cartItem.quantity / uomQuantity).toFixed(2)
        );
        return acc + displayQuantity;
      }, 0);
    } else {
      return subCart.cartItems.length;
    }
  };

  const submitOrderClicked = async () => {
    console.log("pre submit order clicked");
    if (user.suppliers[0].id === "68" && !deliveryDate) {
      Alert.alert("Please select a delivery date");
      return;
    }
    try {
      setLoadingState(true);
      const orderNotes = notes ? notes : null;
      const submitOrderRes = await submitOrder({
        variables: {
          submitOrderInput: {
            cartId: cart.id,
            userId: user.id,
            supplier: supplier,
            notes: orderNotes,
            discount: promoDiscount,
            isCredit,
            deliveryDate: deliveryDate
              ? dayjs(deliveryDate).format("YYYY-MM-DD")
              : null,
            config: {
              submitted_by_driver: user.driver ? user.driver : false,
              ...(orderRoute ? { custom_route: orderRoute } : {}),
            },
          },
        },
      });

      posthog.capture("order_submitted", {
        supplier: supplier,
        cartId: cart.id,
        userId: user.id,
        notes: orderNotes,
      });
      if (!user.driver) {
        navigation.navigate("OrderSubmittedScreen2", {
          total: (getTotal(subCart.cartItems) - promoDiscount).toFixed(2),
        });
      } else {
        navigation.reset({
          index: 0,
          routes: [
            {
              name: "Attain",
              state: {
                index: 2,
                routes: [
                  { name: "Home" },
                  { name: "Browse" },
                  {
                    name: "Orders",
                    state: {
                      index: 1,
                      routes: [
                        { name: "OrdersScreen" },
                        {
                          name: "OrderDetail",
                          params: {
                            orderId:
                              submitOrderRes.data.submitOrder.order_number,
                          },
                        },
                      ],
                    },
                  },
                  { name: "Cart" },
                ],
              },
            },
          ],
        });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoadingState(false);
    }
  };

  const goToPayment = async () => {
    navigation.navigate("PaymentMethods");
  };

  if ((loading && !data) || loadingState)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (error)
    return (
      <LoadingErrorStatus
        message="Uh oh! Something went wrong!"
        errorStatus={true}
      />
    );

  const account = data.accounts.find((account) => account.is_default);
  var discount = 0;
  for (var cartItem of subCart.cartItems) {
    discount =
      discount +
      Math.round(cartItem.price * cartItem.quantity * 100) / 100 -
      Math.round(getPrice(cartItem) * cartItem.quantity * 100) / 100;
  }
  const currentSupplier = user.suppliers.find((s) => s.name === supplier);
  const currentSupplierConfig = currentSupplier.config;
  const promoPresent = currentSupplierConfig
    ? currentSupplierConfig.promo
    : null;
  let promoDiscount = 0;
  if (promoPresent && promoPresent.numOrders > currentSupplier.orderCount) {
    switch (promoPresent.type) {
      case "fraction":
        promoDiscount = subCart.cartItems
          .map(
            (cartItem) =>
              Math.round(
                getPrice(cartItem) *
                  cartItem.quantity *
                  promoPresent.value *
                  100
              ) / 100
          )
          .reduce((total, cartItemTotal) => total + cartItemTotal, 0);
        break;
      case "flat":
        promoDiscount = promoPresent.value;
        break;
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        goBack={navigation.goBack}
        title={!isCredit ? "Checkout" : "Credit Request"}
      />
      <CheckoutSummary
        store={supplier}
        imageUri={imageUri}
        account={account}
        bankOnPress={goToPayment}
        orderNotes={notes}
        setOrderNotes={setNotes}
        singleSupplier={user.supplier_beta}
        deliveryDate={deliveryDate}
        setDeliveryDate={setDeliveryDate}
      />
      <View style={styles.itemsHeader}>
        <Text style={styles.boldMainText}>
          Items ({getUomAdjustedItemCount()})
        </Text>
      </View>
      <ScrollView style={styles.scrollView}>
        {subCart.cartItems.map((cartItem) => {
          return (
            <CheckoutItem
              key={cartItem.id}
              imageUri={
                cartItem.image ||
                supplierObj?.config?.defaultItemImage ||
                "https://fakeimg.pl/100x100?text=no+image"
              }
              itemName={cartItem.name}
              unitSize={cartItem.unitSize}
              unitPrice={cartItem.price}
              discountedUnitPrice={getCustomPrice(cartItem)}
              quantity={cartItem.quantity}
              amount={
                ((isCredit ? -1 : 1) *
                  Math.round(cartItem.price * cartItem.quantity * 100)) /
                100
              }
              discountedAmount={
                ((isCredit ? -1 : 1) *
                  Math.round(
                    getCustomPrice(cartItem) * cartItem.quantity * 100
                  )) /
                100
              }
              item={cartItem}
            />
          );
        })}
      </ScrollView>
      <OrderSummary
        subtotal={parseFloat(getTotal(subCart.cartItems))}
        discount={promoDiscount}
        buttonText={!isCredit ? "Submit Order" : "Submit Credit"}
        onPress={submitOrderClicked}
        isCredit={isCredit}
      />
    </SafeAreaView>
  );
}
