import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import Constants from "expo-constants";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import { useContext, useEffect, useState } from "react";
import { PixelRatio, Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { UserContext } from "../../context/userContext";
import {
  useAddPushNotificationTokenMutation,
  useGetCutoffTimesLazyQuery,
} from "../../generated/graphql";
import convertTime from "../../lib/convertTime";
import BrowseScreen from "../browseScreen/browseScreen";
import CartScreen from "../cartScreen/cartScreen";
import HomeScreen from "../homeScreen/homeScreen";
import OrderScreen from "../orderScreen/orderScreen.js";
import CategoryScreen from "../categoryScreen/categoryScreen";
import { useTranslation } from "react-i18next";

const Tab = createBottomTabNavigator();

const fontScale = PixelRatio.getFontScale();

const NOTIFICATIONS_VERSION = "2.0.0";

// Include null value for 0 index since weekdays are based on 1-7 scale
const DAYS = [
  null,
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

const haveNotificationsPermissions = async () => {
  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let permissionGranted = existingStatus === "granted";
  if (!permissionGranted) {
    const { status } = await Notifications.requestPermissionsAsync();
    permissionGranted = status === "granted";
  }

  return permissionGranted;
};

const filterCutoffTimes = (cutoffTimes) =>
  cutoffTimes.filter(
    (data) =>
      data.cutoffTime &&
      DAYS.includes(data.cutoffDay) &&
      data.cutoffTime
        .split(":")
        .map((x) => parseInt(x))
        .every((x) => !isNaN(x))
  );

const scheduleNotifications = async (filteredCutoffTimes) => {
  const responseIds = filteredCutoffTimes.map(({ id }) => id);

  const scheduledNotifications =
    await Notifications.getAllScheduledNotificationsAsync();
  const scheduledIds = scheduledNotifications.map(
    (notification) => notification.content.data.id
  );

  const scheduledVersions = scheduledNotifications.map(
    (notification) => notification.content.data.version
  );

  // Only checks if cutoff time IDs changed
  if (
    scheduledVersions.every((version) => version === NOTIFICATIONS_VERSION) &&
    responseIds.every((id) => scheduledIds.includes(id)) &&
    scheduledIds.every((id) => responseIds.includes(id))
  ) {
    return;
  }

  await Notifications.cancelAllScheduledNotificationsAsync();

  await Promise.all(
    filteredCutoffTimes.map(async (data) => {
      const [hour] = data.cutoffTime.split(":").map((x) => parseInt(x));
      const weekday = DAYS.indexOf(data.cutoffDay);
      const sameDay = hour >= 11;
      const dayBefore = weekday === 1 ? 7 : weekday - 1;

      await Notifications.scheduleNotificationAsync({
        content: {
          title: `Place your order now`,
          body: `Your order cutoff time for ${data.supplier} is ${
            sameDay ? "tomorrow" : "in two days"
          } at ${convertTime(data.cutoffTime)}`,
          sound: "default",
          data: {
            id: data.id,
            version: NOTIFICATIONS_VERSION,
          },
        },
        trigger: {
          repeats: true,
          weekday: sameDay ? dayBefore : dayBefore === 1 ? 7 : dayBefore - 1,
          hour: 10,
          minute: 0,
          second: 0,
        },
      });

      await Notifications.scheduleNotificationAsync({
        content: {
          title: `Time to restock!`,
          body: `Your order cutoff time for ${data.supplier} is ${
            sameDay ? "today" : "tomorrow morning"
          } at ${convertTime(data.cutoffTime)}`,
          sound: "default",
          data: {
            id: data.id,
            version: NOTIFICATIONS_VERSION,
          },
        },
        trigger: {
          repeats: true,
          weekday: sameDay ? weekday : dayBefore, // Notify one day ahead if cutoff time in morning
          hour: 10,
          minute: 0,
          second: 0,
        },
      });
    })
  );
};

async function registerForPushNotificationsAsync() {
  let token;
  if (Device.isDevice) {
    token = await Notifications.getExpoPushTokenAsync({
      projectId: Constants.expoConfig.extra.eas.projectId,
    });
    console.log(token);
  } else {
    alert("Must use physical device for Push Notifications");
  }

  if (Platform.OS === "android") {
    Notifications.setNotificationChannelAsync("default", {
      name: "default",
      importance: Notifications.AndroidImportance.MAX,
    });
  }

  return token;
}

const showRecommendations = async (
  filteredCutoffTimes,
  setShowRecommendationsModal
) => {
  filteredCutoffTimes.map((data) => {
    const cutoffDay = DAYS.indexOf(data.cutoffDay);
    const currentDay = new Date().getDay();
    if (cutoffDay - 1 === currentDay || cutoffDay - 2 === currentDay) {
      // TODO: Temporarily add logic to check if recommendations were already shown once. Remove once we have a better way to show recommendations
      AsyncStorage.getItem("recommendations_modal_shown").then(
        async (value) => {
          if (value !== "true") {
            console.log(
              "Showing recommendations modal since it was not shown before"
            );
            await AsyncStorage.setItem("recommendations_modal_shown", "true");
            setShowRecommendationsModal(true);
          }
        }
      );
    }
  });
};

export default function App() {
  const { user, setShowRecommendationsModal } = useContext(UserContext);
  const [expoPushToken, setExpoPushToken] = useState("");
  const { t } = useTranslation();

  const [getCutoffTimes] = useGetCutoffTimesLazyQuery();
  const [addPushNotificationTokenMutation] =
    useAddPushNotificationTokenMutation();
  const theme = useTheme();

  useEffect(() => {
    const handleScheduledNotifications = async () => {
      return getCutoffTimes({
        fetchPolicy: "cache-and-network",
        variables: {
          getCutoffTimesInput: {
            businessId: user.id,
          },
        },
        onCompleted: async ({ cutoffTimes }) => {
          const filteredCutoffTimes = filterCutoffTimes(cutoffTimes);
          scheduleNotifications(filteredCutoffTimes);
          showRecommendations(filteredCutoffTimes, setShowRecommendationsModal);
        },
      });
    };
    const handlePushNotifications = async () => {
      return registerForPushNotificationsAsync().then(async (token) => {
        setExpoPushToken(token);
        await addPushNotificationTokenMutation({
          variables: {
            addPushNotificationTokenInput: {
              token: token.data,
              user_id: user.id,
            },
          },
        });
      });
    };

    haveNotificationsPermissions().then(async (permissionGranted) => {
      if (permissionGranted) {
        if (user.id) {
          handlePushNotifications();
          handleScheduledNotifications();
        }
      } else {
        console.log("Notifications permission not granted");
      }
    });
  }, [user.id]);

  const HomeIcon = (focused) => {
    return focused ? (
      <Ionicons name="home-sharp" size={24} color={theme.appColors.text} />
    ) : (
      <Ionicons name="home-outline" size={24} color={theme.appColors.text} />
    );
  };
  const ScanIcon = (focused) => {
    return focused ? (
      <Ionicons name="scan-sharp" size={24} color={theme.appColors.text} />
    ) : (
      <Ionicons name="scan-outline" size={24} color={theme.appColors.text} />
    );
  };

  const CartIcon = (focused) => {
    return focused ? (
      <Ionicons name="cart-sharp" size={24} color={theme.appColors.text} />
    ) : (
      <Ionicons name="cart-outline" size={24} color={theme.appColors.text} />
    );
  };

  const ExploreIcon = (focused) => {
    return focused ? (
      <Ionicons
        name="file-tray-stacked-sharp"
        size={24}
        color={theme.appColors.text}
      />
    ) : (
      <Ionicons
        name="file-tray-stacked-outline"
        size={24}
        color={theme.appColors.text}
      />
    );
  };

  const OrderIcon = (focused) => {
    return focused ? (
      <Ionicons name="receipt-sharp" size={24} color={theme.appColors.text} />
    ) : (
      <Ionicons name="receipt-outline" size={24} color={theme.appColors.text} />
    );
  };
  return (
    <Tab.Navigator
      screenOptions={() => ({
        tabBarLabelStyle: {
          fontSize: 15 / fontScale,
          color: theme.appColors.text,
        },
        tabBarStyle: {
          backgroundColor: theme.appColors.surface,
        },
      })}
    >
      <Tab.Screen
        name={t("title.home")}
        component={HomeScreen}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => HomeIcon(focused),
        }}
      />

      {!user.supplier_beta ? (
        <Tab.Screen
          name={t("title.browse")}
          component={BrowseScreen}
          options={{
            headerShown: false,
            tabBarIcon: ({ focused }) => ExploreIcon(focused),
          }}
        />
      ) : (
        <Tab.Screen
          name={t("title.browse")}
          component={CategoryScreen}
          initialParams={{ supplier: user.suppliers[0] }}
          options={{
            headerShown: false,
            tabBarIcon: ({ focused }) => ExploreIcon(focused),
          }}
        />
      )}

      <Tab.Screen
        name={t("title.orders")}
        component={OrderScreen}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => OrderIcon(focused),
        }}
      />
      <Tab.Screen
        name={t("title.cart")}
        component={CartScreen}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) => CartIcon(focused),
        }}
      />
    </Tab.Navigator>
  );
}
