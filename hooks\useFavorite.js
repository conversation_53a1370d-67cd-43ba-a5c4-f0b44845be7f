import { useState, useEffect, useCallback } from "react";
import { useToggleFavoriteMutation } from "../generated/graphql";

export const useFavorite = (item, userId) => {
  const [isFavorited, setIsFavorited] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  useEffect(() => {
    if (item && typeof item.isFavorited === "boolean") {
      setIsFavorited(item.isFavorited);
    }
  }, [item?.id, item?.isFavorited]);

  const [toggleFavoriteMutation] = useToggleFavoriteMutation({
    onCompleted: (data) => {
      if (data?.toggleFavorite?.success) {
        setIsFavorited(data.toggleFavorite.isFavorited);
      }
      setIsToggling(false);
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      setIsFavorited((prev) => !prev);
      setIsToggling(false);
    },
    refetchQueries: ["HomeScreen", "GetSections"],
  });

  const toggleFavorite = useCallback(async () => {
    if (!item || !userId || isToggling) return;

    setIsToggling(true);

    // Optimistically update UI
    setIsFavorited((prev) => !prev);

    try {
      await toggleFavoriteMutation({
        variables: {
          input: {
            userId: parseInt(userId, 10),
            itemId: parseInt(item.id, 10),
          },
        },
      });
    } catch (error) {
      // Error handling is in onError callback
    }
  }, [item, userId, isToggling, toggleFavoriteMutation]);

  return {
    isFavorited,
    toggleFavorite,
    isToggling,
  };
};
