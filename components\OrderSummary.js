import { StyleSheet, Text, View, Dimensions } from "react-native";
import { useTheme } from "@react-navigation/native";
import React, { useMemo, useContext } from "react";
import PropTypes from "prop-types";
import Button from "./Button";
import { UserContext } from "../context/userContext";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      backgroundColor: theme.appColors.textLight,
      borderRadius: 10,
      justifyContent: "center",
      marginRight: 10,
    },
    buttonText: {
      color: "white",
      fontSize: 17,
      fontWeight: theme.fonts.medium.fontWeight,
      textAlign: "center",
    },
    container: {
      backgroundColor: theme.appColors.backdropLight,
      // borderTopLeftRadius: 10,
      // borderTopRightRadius: 10,
      display: "flex",
      flexDirection: "row",
      height: 50,
      width: "100%",
      justifyContent: "space-between",
      paddingHorizontal: 0,
      // position: "absolute",
      bottom: 0,
      // left: (Dimensions.get('window').width  - 300)/ 2
    },
    mainText: {
      color: theme.appColors.primary,
      fontSize: 16,
      fontWeight: "700",
      marginLeft: 40,
    },
    preText: {
      color: theme.appColors.primary,
      fontSize: 16,
      marginLeft: 40,
    },
    standardColumn: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
    },
  });
  return styles;
};

export default function OrderSummary({
  subtotal,
  discount,
  buttonText,
  onPress,
  isCredit,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { user } = useContext(UserContext);

  const total = subtotal - discount;
  return (
    <View style={[styles.container, discount ? { height: 80 } : {}]}>
      <View style={styles.standardColumn}>
      {user.show_prices && (
        <>
          {discount ? (
            <>
              <Text style={styles.preText}>Subtotal: ${subtotal.toFixed(2)}</Text>
              <Text style={styles.preText}>Discount: ${discount.toFixed(2)}</Text>
            </>
          ) : null}
          <Text style={styles.mainText}>
            Total: {isCredit ? "-" : ""}${total.toFixed(2)}
          </Text>
        </>
      )}
      </View>
      <View style={styles.standardColumn}>
        <Button
          style={styles.button}
          variant="light"
          size="md"
          onPress={onPress}
        >
          <Text style={styles.buttonText}>{buttonText}</Text>
        </Button>
      </View>
    </View>
  );
}

OrderSummary.propTypes = {
  subtotal: PropTypes.number.isRequired,
  discount: PropTypes.number,
  buttonText: PropTypes.string.isRequired,
  onPress: PropTypes.func.isRequired,
  isCredit: PropTypes.bool,
};
