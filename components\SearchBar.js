import { useState } from "react";
import {
  Platform,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { Ionicons } from "@expo/vector-icons";

import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";

const styles = StyleSheet.create({
  bar: {
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 16,
    flexDirection: "row",
    height: 33,
    marginTop: 15,
    paddingHorizontal: 16,
    shadowColor: "black",
    shadowOffset: { height: 0, width: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 0.3,
  },
  text: {
    flex: 1,
    marginLeft: 16,
  },
});

const SearchBar = ({
  button,
  onInput,
  onPressScan,
  placeholder: customPlaceholder,
}) => {
  const [text, setText] = useState("");
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  const placeholder = customPlaceholder || "Start typing to search!";
  const textStyle = [globalStyles.bodyLarge, globalStyles.text, styles.text];

  const SearchIcon = () => (
    <Ionicons color={theme.appColors.primary} name="search" size={20} />
  );

  const ScanIcon = ({ onPressScan }) =>
    onPressScan ? (
      <TouchableOpacity onPress={onPressScan} hitSlop={20}>
        <Ionicons color={theme.appColors.primary} name="scan" size={25} />
      </TouchableOpacity>
    ) : (
      <></>
    );
  ScanIcon.propTypes = {
    onPressScan: PropTypes.func,
  };

  if (button) {
    return (
      <TouchableOpacity onPress={onInput} style={styles.bar}>
        <SearchIcon theme={theme} />
        <Text style={textStyle}>{placeholder}</Text>
        <ScanIcon onPressScan={onPressScan} theme={theme} />
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.bar}>
      <SearchIcon />
      <TextInput
        onChangeText={setText}
        onEndEditing={() => onInput(text)}
        value={text}
        hitSlop={{ bottom: 15, left: 40, right: 10, top: 15 }}
        keyboardType={Platform.OS === "ios" ? "web-search" : "default"}
        placeholder={placeholder}
        style={textStyle}
      />
      <ScanIcon onPressScan={onPressScan} />
    </View>
  );
};

SearchBar.propTypes = {
  button: PropTypes.bool,
  onInput: PropTypes.func.isRequired,
  onPressScan: PropTypes.func,
  placeholder: PropTypes.string,
};

export default SearchBar;
