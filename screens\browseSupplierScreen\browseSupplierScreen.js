import { useContext, useMemo } from "react";
import {
  FlatList,
  StatusBar,
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Image,
  PixelRatio,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";

import { UserContext } from "../../context/userContext";
import { useGetItemsBySupplierQuery } from "../../generated/graphql";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import Header from "../../components/Header";
import ItemGridCard from "../../components/ItemCard/ItemGridCard";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { Ionicons } from "@expo/vector-icons";
import { createStackNavigator } from "@react-navigation/stack";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import SearchResultsScreen from "../searchResultsScreen/searchResultsScreen";
const createStyles = (theme) => {
  const styles = StyleSheet.create({
    list: {
      backgroundColor: theme.appColors.backdropNeutral,
    },
    searchBar: {
      backgroundColor: "white",
      borderRadius: 5,
      flex: 1,
      height: 38,
      marginLeft: 5,
      padding: 0,
      shadowOffset: { height: 3 },
      shadowOpacity: 0.1,
    },
    searchBarContainer: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      flexDirection: "row",
      height: 80,
      justifyContent: "center",
      padding: 20,
      width: "100%",
    },
    searchBarInsideView: {
      alignItems: "center",
      flexDirection: "row",
      flex: 1,
      justifyContent: "space-between",
      paddingHorizontal: 10,
    },
  });
  return styles;
};

const BrowseSupplierScreenComponent = ({ navigation, route }) => {
  const { user } = useContext(UserContext);
  console.log(route);
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { id, logo, name } = route.params.supplier;
  const globalStyles = useGlobalStyles();

  const searchBarPressed = async () => {
    navigation.navigate("SearchResults", { supplier: name });
  };

  const scanButtonPressed = () => {
    navigation.navigate("SupplierScanner", {
      screen: "Scanner",
      params: {
        supplier: route.params.supplier,
      },
    });
  };

  const { loading, error, data, fetchMore } = useGetItemsBySupplierQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getItemsBySupplierInput: {
        userId: user.id,
        supplierId: id,
        pagination: {
          offset: 0,
          limit: 20,
        },
      },
    },
  });

  return (
    <SafeAreaView style={globalStyles.container}>
      <StatusBar
        backgroundColor={theme.appColors.primary}
        barStyle="light-content"
      />
      <Header
        goBack={navigation.goBack}
        iconSource={{ uri: logo }}
        title={name}
      >
        <View
          style={{
            marginTop: 20,
            flexDirection: "row",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <TouchableOpacity
            style={{
              height: 38,
              width: 38,
              paddingLeft: 6,
              paddingRight: 6,
              borderRadius: 5,
              shadowOpacity: 0.1,
              shadowOffset: { height: 3 },
              flexDirection: "row",
              alignItems: "center",
              backgroundColor: theme.appColors.backdropLight,
              justifyContent: "center",
            }}
            onPress={scanButtonPressed}
          >
            <Image
              style={{ width: 32, height: 32 }}
              source={theme.assets.barcode}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.searchBar}
            onPress={searchBarPressed}
          >
            <View style={styles.searchBarInsideView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  flex: 1,
                  height: "100%",
                }}
              >
                <Ionicons name="search" size={16} color="black" />
                <Text
                  maxFontSizeMultiplier={1.4}
                  style={[
                    globalStyles.bodyMedium,
                    { marginTop: 1, marginLeft: 5 },
                  ]}
                >
                  Search products...
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </Header>

      {loading && !data && !error && (
        <LoadingErrorStatus message="Loading..." errorStatus={false} />
      )}
      {error && (
        <LoadingErrorStatus message={error.message} errorStatus={true} />
      )}
      {!loading && !error && data && (
        <FlatList
          data={data.itemsBySupplier}
          numColumns={2}
          renderItem={({ item, index }) => (
            <ItemGridCard
              key={item.id}
              item={item}
              index={index}
              userApproved={user.approved}
              customPrices={user.custom_prices}
            />
          )}
          onEndReached={() => {
            fetchMore({
              variables: {
                getItemsBySupplierInput: {
                  userId: user.id,
                  supplierId: id,
                  pagination: {
                    offset: data.itemsBySupplier.length,
                    limit: 20,
                  },
                },
              },
              updateQuery: (previousResult, { fetchMoreResult }) => {
                // Don't do anything if there weren't any new items
                if (
                  !fetchMoreResult ||
                  fetchMoreResult.itemsBySupplier.length === 0
                ) {
                  return previousResult;
                }
                return {
                  // Append the new feed results to the old one
                  itemsBySupplier: previousResult.itemsBySupplier.concat(
                    fetchMoreResult.itemsBySupplier
                  ),
                };
              },
            });
          }}
          onEndReachedThreshold={0.5}
          style={styles.list}
        />
      )}
    </SafeAreaView>
  );
};

const BrowseSupplierStack = createStackNavigator();
const fontScale = PixelRatio.getFontScale();
export default function BrowseSupplierScreen({ route }) {
  return (
    <BrowseSupplierStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 25 / fontScale,
        },
      })}
    >
      <BrowseSupplierStack.Screen
        name="BrowseSupplierScreen"
        options={{ headerShown: false }}
        initialParams={{ supplier: route.params.supplier }}
        component={BrowseSupplierScreenComponent}
      />
      <BrowseSupplierStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{ title: "Item Details" }}
      />
      <BrowseSupplierStack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{ title: "Search Results" }}
      />
    </BrowseSupplierStack.Navigator>
  );
}
