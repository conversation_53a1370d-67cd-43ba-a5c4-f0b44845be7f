import { createContext } from "react";

export const UserContext = createContext({
  isLoggedIn: false,
  setIsLoggedIn: () => {},
  user: {
    id: "",
    name: "",
    driver: false,
    suppliers: [],
    supplier_beta: false,
    approved: false,
    custom_prices: [],
    doesnt_use_uom: false,
    custom_uom_prices: [],
    is_user_account: false,
    show_prices: true,
    is_user_account_selected_by_driver: false,
    delivery_window: {
      days_of_week: [],
      start_time: "",
      end_time: "",
    },
  },
  showRecommendationsModal: false,
  setShowRecommendationsModal: () => {},
  setTheme: () => {},
  setUserId: () => {},
  setCustomPrices: () => {},
  setCustomUomPrices: () => {},
  setShowPrices: () => {},
  setOrderRoute: () => {},
  frontendUrl: "",
  backendUrl: "",
  orderRoute: "",
});
