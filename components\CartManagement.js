import { StyleSheet, View, TouchableOpacity } from "react-native";
import Checkbox from "expo-checkbox";
import Text from "../components/Text";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo } from "react";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    checkBox: {
      borderRadius: 5,
      color: theme.appColors.primary,
    },
    deleteButton: {
      flexDirection: "row",
      justifyContent: "center",
      width: 60,
    },
    deleteText: {
      color: theme.appColors.textLight,
      textDecorationLine: "underline",
    },
    itemManagement: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 15,
    },
    selectAllText: {
      color: theme.appColors.primary,
      fontSize: 15,
      fontWeight: theme.fonts.medium.fontWeight,
      justifyContent: "center",
      marginLeft: 10,
    },
  });
  return styles;
};

export default function CartManagement({
  boolCheck,
  onValueChange,
  deleteItems,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.itemManagement}>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <Checkbox
          style={styles.checkBox}
          value={boolCheck}
          onValueChange={onValueChange}
          color={theme.appColors.primary}
        />
        <Text style={styles.selectAllText}>Select All</Text>
      </View>
      <TouchableOpacity style={styles.deleteButton} onPress={deleteItems}>
        <Text style={styles.deleteText}>Remove</Text>
      </TouchableOpacity>
    </View>
  );
}
