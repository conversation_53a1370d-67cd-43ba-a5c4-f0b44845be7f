import { useContext, useEffect } from "react";
import { View, ScrollView } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import { createStackNavigator } from "@react-navigation/stack";
import CheckoutScreen from "../checkoutScreen/checkoutScreen.js";
import PaymentScreen from "../paymentScreen/paymentScreen";
import PaymentScreen2 from "../paymentScreen/paymentScreen2";
import PaymentScreen3 from "../paymentScreen/paymentScreen3";
import SubCartScreen from "../subCartScreen/subCartScreen";
import SubCart from "./components/subCart";
import { UserContext } from "../../context/userContext";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { useUpdateItemInCartMutation } from "../../generated/graphql";
import { useTranslation } from "react-i18next";

function CartScreenComponent({ navigation }) {
  const theme = useTheme();
  const { t } = useTranslation();
  const { user } = useContext(UserContext);
  const supplier_id = user.suppliers[0].id;

  const globalStyles = useGlobalStyles();
  const subCarts = user.cart.subCarts;
  const [updateItemInCart] = useUpdateItemInCartMutation();

  useEffect(() => {
    if (
      user &&
      user.suppliers &&
      user.suppliers[0] &&
      user.suppliers[0].id == "68"
    ) {
      subCarts.forEach((subCart) => {
        const orderItems = subCart.cartItems;

        const crv5Quantity = orderItems.reduce((total, item) => {
          if (item.crv === "CA CRV $0.05") {
            return total + (Number(item.quantity) || 0);
          }
          return total;
        }, 0);

        const crv10Quantity = orderItems.reduce((total, item) => {
          if (item.crv === "CA CRV $0.10") {
            return total + (Number(item.quantity) || 0);
          }
          return total;
        }, 0);

        if (crv5Quantity > 0) {
          updateItemInCart({
            variables: {
              updateItemInCartInput: {
                cartId: user.cart.id,
                itemId: "391457",
                quantity: crv5Quantity,
                userId: user.id,
                supplierId: supplier_id,
              },
            },
          });
        }
        if (crv10Quantity > 0) {
          updateItemInCart({
            variables: {
              updateItemInCartInput: {
                cartId: user.cart.id,
                itemId: "391456",
                quantity: crv10Quantity,
                userId: user.id,
                supplierId: supplier_id,
              },
            },
          });
        }
      });
    }
  }, [subCarts]);

  return (
    <SafeAreaView style={globalStyles.container}>
      <View style={globalStyles.navBar}>
        <Text style={[globalStyles.headlineLarge, globalStyles.textNeutral]}>
          {t("cart.your_cart")}
        </Text>
        <Text
          style={[
            globalStyles.labelLarge,
            globalStyles.textNeutral,
            { marginBottom: 10 },
          ]}
        >
          {subCarts.length} supplier{subCarts.length > 1 ? "s" : ""} ready for
          checkout
        </Text>
      </View>
      <ScrollView
        style={[
          globalStyles.scrollView,
          { backgroundColor: theme.appColors.backdropLight },
        ]}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        {subCarts.map((subCart) => {
          return (
            <SubCart
              style={{ marginVertical: 10 }}
              key={subCart.supplier}
              subCart={subCart}
            />
          );
        })}
      </ScrollView>
    </SafeAreaView>
  );
}

const CartStack = createStackNavigator();
export default function CartScreen() {
  return (
    <CartStack.Navigator>
      <CartStack.Screen
        name="CartScreen"
        component={CartScreenComponent}
        options={{ title: "Cart", headerShown: false }}
      />
      <CartStack.Screen
        name="SubCartScreen"
        component={SubCartScreen}
        options={{
          headerShown: false,
        }}
      />
      <CartStack.Screen
        name="CheckoutScreen"
        component={CheckoutScreen}
        options={{
          headerShown: false,
        }}
      />
      <CartStack.Screen
        name="PaymentMethods"
        component={PaymentScreen}
        options={{ title: "Payment Method" }}
      />
      <CartStack.Screen
        name="AddPaymentMethod"
        component={PaymentScreen2}
        options={{ title: "Add Payment Method" }}
      />
      <CartStack.Screen
        name="AddPaymentMethodRedirect"
        component={PaymentScreen3}
        options={{ title: "Bank Authorization" }}
      />
    </CartStack.Navigator>
  );
}
