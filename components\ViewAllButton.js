import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import PropTypes from "prop-types";
import { useTheme, useNavigation } from "@react-navigation/native";
import { useMemo } from "react";
import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
    },
    image: {
      height: 30,
      width: 30,
    },
  });
  return styles;
};

export const ViewAllButton = ({ to, params, variant }) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const image =
    variant === "light"
      ? theme.assets.seeAllRightLight
      : theme.assets.seeAllRightDark;
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.push(to, params);
      }}
      style={styles.container}
    >
      <View
        style={{
          flexDirection: "column",
          justifyContent: "center",
          marginRight: 10,
        }}
      >
        <Text style={[globalStyles.text, globalStyles.headlineSmall]}>
          More
        </Text>
      </View>
      <Image style={styles.image} source={image} />
    </TouchableOpacity>
  );
};

export default ViewAllButton;
