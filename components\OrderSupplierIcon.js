import { Image, StyleSheet, Text, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { Entypo } from "@expo/vector-icons";

import useGlobalStyles from "../lib/useGlobalStyles";

const styles = StyleSheet.create({
  supplier: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: 3,
  },
  supplierLogo: {
    height: 25,
    marginRight: 5,
    width: 25,
  },
});

const OrderSupplierIcon = ({ light, order, supplier = {} }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  if (!order?.supplier) {
    return <></>;
  }

  const squareLogo =
    supplier && supplier?.config ? supplier?.config?.squareLogo : null;

  return (
    <View style={styles.supplier}>
      {squareLogo || order?.supplier_logo ? (
        <Image
          source={{
            uri: squareLogo ?? order?.supplier_logo,
          }}
          style={styles.supplierLogo}
        />
      ) : (
        <Entypo
          name="shop"
          color={
            light ? theme.appColors.textNeutral : theme.appColors.textLight
          }
          size={20}
          style={{ marginRight: 5 }}
        />
      )}
      <Text
        style={[
          globalStyles.bodyLarge,
          light ? globalStyles.textNeutral : globalStyles.text,
        ]}
      >
        {order?.supplier}
      </Text>
    </View>
  );
};

OrderSupplierIcon.propTypes = {
  light: PropTypes.bool,
  order: PropTypes.object.isRequired,
  supplier: PropTypes.object,
};

export default OrderSupplierIcon;
