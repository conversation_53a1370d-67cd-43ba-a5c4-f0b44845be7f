import {
  useState,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
} from "react";
import {
  StyleSheet,
  View,
  StatusBar,
  TouchableOpacity,
  Image,
  ScrollView,
  TextInput,
} from "react-native";
import Text from "../../components/Text";
import Modal from "react-native-modal";
import {
  useGetItemsQuery,
  useGetItemsBySupplierQuery,
  useUpdateItemInCartMutation,
} from "../../generated/graphql";
import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import { useTheme } from "@react-navigation/native";
import useGlobalStyles from "../../lib/useGlobalStyles";
import Button from "../../components/Button";
import NumericInput from "../../components/NumericInput";
import { discountExists } from "../../lib/discountExists";
import { Feather, Ionicons } from "@expo/vector-icons";
import { getPrice } from "../../lib/getPrice";
import formatDate from "../../lib/formatDate";
import ItemsSection from "../../components/ItemsSection";
import { useTranslation } from "react-i18next";
import Animated, { FadeIn } from "react-native-reanimated";
import PriceInput from "./components/priceInput";
import MinPrice from "./components/minPrice";
import OfferForFreeCheckbox from "./components/OfferForFreeCheckbox";
import FavoriteIcon from "../../components/FavoriteIcon";
import { useFavorite } from "../../hooks/useFavorite";
import isBackInStock from "../../lib/isBackInStock";
import {
  getUomOptions,
  getUomPrice,
  getUomQuantity,
  getUomUpc,
  getUomName,
  hasUoms,
} from "../../lib/uomHelpers";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    backInStockBadge: {
      backgroundColor: theme.appColors.greenText,
      borderRadius: 25,
      marginBottom: 8,
      paddingHorizontal: 8,
      paddingVertical: 4,
      width: 110,
    },
    backInStockText: {
      color: "white",
      fontSize: 12,
      fontWeight: "bold",
      textAlign: "center",
    },
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 15,
    },
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
    },
    buttonContainer: {
      marginBottom: 0,
      marginTop: 10,
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    dealsButton: {
      alignItems: "center",
      borderColor: theme.appColors.primary,
      borderRadius: 7,

      borderWidth: 2,
      height: 35,
      justifyContent: "center",
      marginHorizontal: "10%",
      marginTop: 10,
      paddingVertical: 5,
      width: "80%",
    },
    dealsButtonText: {
      color: theme.appColors.primary,
    },
    disabledAppearance: {
      backgroundColor: theme.appColors.disabled,
      opacity: 0.5,
    },
    errorContainer: {},
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    itemDetailLine: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 120,
      width: 120,
    },
    itemTextContainer: {
      flex: 2,
      marginLeft: 20,
    },
    lastOrderedContainer: {
      marginTop: 10,
    },
    originalPriceText: {
      color: theme.appColors.redText,
    },
    originalPriceText: {
      color: theme.appColors.redText,
    },
    pricingContainer: {
      marginTop: 10,
    },
    quantityContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      marginTop: 10,
      marginBottom: 40,
    },
    uomSelectorContainer: {
      marginTop: 10,
    },
    uomSelector: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 15,
      paddingHorizontal: 0,
      borderTopWidth: 1,
      borderTopColor: theme.appColors.backdropDark,
    },
    uomOptionsContainer: {
      marginTop: 10,
      paddingVertical: 10,
    },
    uomOptionsContent: {
      paddingHorizontal: 0,
    },
    uomOption: {
      paddingHorizontal: 15,
      paddingVertical: 10,
      marginRight: 10,
      borderWidth: 1,
      borderColor: theme.appColors.backdropDark,
      borderRadius: 8,
      backgroundColor: "white",
    },
    selectedUomOption: {
      backgroundColor: theme.appColors.primary,
      borderColor: theme.appColors.primary,
    },
    selectedUomText: {
      color: "white",
    },
    safeContainer: {
      backgroundColor: "white",
      paddingHorizontal: 20,
      paddingTop: 20,
    },
  });
  return styles;
};

export default function ItemDetailScreen({ navigation, route }) {
  const [showAddedToCartPopup, setShowAddedToCartPopup] = useState(false);
  const { user } = useContext(UserContext);
  const globalStyles = useGlobalStyles();

  const [isEditingPrice, setIsEditingPrice] = useState(false);
  const [customPrice, setCustomPrice] = useState(0);
  const [offerForFree, setOfferForFree] = useState(false);
  const [customNotes, setCustomNotes] = useState("");
  const [priceError, setPriceError] = useState("");

  const theme = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const { id, supplier, upcCode, fromOrderEdit, order, invoiceItems } =
    route.params;

  const bySupplier = supplier && supplier.id;

  const itemsResult = useGetItemsQuery({
    fetchPolicy: "cache-and-network",
    skip: bySupplier,
    variables: {
      getItemsInput: {
        userId: user.id,
        ids: id ? [id] : null,
        upcs: upcCode ? [upcCode] : null,
        pagination: { limit: 1, offset: 0 },
      },
    },
  });

  // console.log("Items Result: ", itemsResult);

  const itemsBySupplierResult = useGetItemsBySupplierQuery({
    fetchPolicy: "cache-and-network",
    skip: !bySupplier,
    variables: {
      getItemsBySupplierInput: {
        supplierId: bySupplier ? supplier.id : null,
        userId: user.id,
        upc: upcCode ? upcCode : null,
        pagination: { limit: 1, offset: 0 },
      },
    },
  });

  const { loading, error, data } = bySupplier
    ? itemsBySupplierResult
    : itemsResult;

  const [updateItemInCart] = useUpdateItemInCartMutation();

  const items = bySupplier ? data?.itemsBySupplier : data?.items;
  const item = items?.[0];
  const [quantity, setQuantity] = useState(1);

  const { isFavorited, toggleFavorite, isToggling } = useFavorite(
    item,
    user.id
  );

  // UOM state
  const [selectedUomId, setSelectedUomId] = useState("default");
  const [uomDropdownOpen, setUomDropdownOpen] = useState(false);

  useEffect(() => {
    setQuantity(1);
    setSelectedUomId("default");
  }, [id]);

  // Initialize custom price when item is loaded
  useEffect(() => {
    if (data && !loading) {
      const items = bySupplier ? data.itemsBySupplier : data.items;
      if (items.length > 0) {
        const item = items[0];
        const item_price = getPrice(item, user.custom_prices);
        setCustomPrice(item_price.toString());
        setPriceError(""); // Clear any existing price errors
      }
    }
  }, [data, loading, bySupplier, user.custom_prices]);

  const supplierObj = useMemo(() => {
    if (!data || loading) return null;
    const items = bySupplier ? data.itemsBySupplier : data.items;
    if (items.length === 0) return null;
    const item = items[0];

    const _supplier = user.suppliers.find(
      (supplier) => supplier.name === item.supplier
    );
    return _supplier;
  }, [data, loading, bySupplier, user.suppliers]);

  const config = supplierObj?.config;

  const enablePriceEditing = user.driver && config?.enable_minimum_pricing;
  const canSellForFree =
    enablePriceEditing && (item?.min_sale_price === 0 || !item?.min_sale_price);

  useLayoutEffect(() => {
    if (user.driver) {
      navigation.setOptions({
        headerRight: () => (
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <TouchableOpacity
              style={{
                marginRight: 10,
                padding: 8,
                borderRadius: 5,
                minWidth: 40,
                minHeight: 40,
                alignItems: "center",
                justifyContent: "center",
              }}
              onPress={() => {
                setIsEditingPrice((prev) => {
                  if (prev) {
                    // When exiting edit mode, clear any price errors
                    setPriceError("");
                  }
                  return !prev;
                });
              }}
            >
              <Feather name="edit-3" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <FavoriteIcon
              isFavorited={isFavorited}
              onPress={toggleFavorite}
              size={28}
              style={{
                opacity: isToggling ? 0.5 : 1,
              }}
            />
          </View>
        ),
        headerRightContainerStyle: {
          paddingRight: 10,
        },
      });
    } else {
      navigation.setOptions({
        headerRight: () => (
          <FavoriteIcon
            isFavorited={isFavorited}
            onPress={toggleFavorite}
            size={28}
            style={{
              opacity: isToggling ? 0.5 : 1,
            }}
          />
        ),
        headerRightContainerStyle: {
          paddingRight: 10,
        },
      });
    }
  }, [
    navigation,
    user.driver,
    globalStyles.textLight.color,
    isFavorited,
    toggleFavorite,
    isToggling,
  ]);

  useEffect(() => {
    setIsEditingPrice(false);
    setCustomPrice(0);
    setPriceError("");
    setOfferForFree(false);
    setCustomNotes("");
  }, [id]);

  const cart = user.cart;
  const supplier_id = user.suppliers[0].id;

  const onEditQuantity = (value) => {
    setQuantity(value);
  };

  const getItemQuantityInCart = () => {
    // Find cart item with matching item_id AND UOM
    const checkItemIdAndUom = (cartItem) =>
      cartItem.item_id == item.id &&
      cartItem.item_uom_id ===
        (selectedUomId === "default" ? null : selectedUomId);

    if (cart.cartItems.some(checkItemIdAndUom)) {
      const cartItem = cart.cartItems.find(checkItemIdAndUom);
      // Calculate UOM-adjusted display quantity
      const uomQuantity = cartItem?.item_uom_id
        ? getUomQuantity(cartItem, cartItem.item_uom_id)
        : 1;
      const displayQuantity = Number(
        (cartItem.quantity / uomQuantity).toFixed(2)
      );
      return displayQuantity;
    }
    return 0;
  };

  const onAddToCartPressed = () => {
    if (fromOrderEdit && order) {
      // Handle adding to order (from edit order flow)
      const uomQuantityMultiplier = getUomQuantity(item, selectedUomId);
      const backendQuantity = quantity * uomQuantityMultiplier;

      const formattedOrderItem = {
        item_id: item.id,
        quantity: backendQuantity,
        price_purchased_at: parseFloat(item.price),
        name: item.name,
        description: item.description,
        image: item.image,
        crv: item.crv,
        item_uom_id: selectedUomId === "default" ? null : selectedUomId,
        uoms: item.uoms, // Include UOMs for display
      };

      const updatedOrder = {
        ...order,
        orderItems: [...order.orderItems, formattedOrderItem],
      };

      let updatedInvoiceItems = invoiceItems;
      if (invoiceItems && invoiceItems.length !== 0) {
        const formattedInvoiceItem = {
          item_id: item.id,
          quantity: backendQuantity,
          price: parseFloat(item.discounted_price ?? item.price),
          name: item.name,
          unit_size: item.unit_size,
          cog_price: parseFloat(item.cog_price),
          upc1: item.upc1,
          size: item.size,
          image: item.image,
          crv: item.crv,
          item_uom_id: selectedUomId === "default" ? null : selectedUomId,
          uoms: item.uoms, // Include UOMs for display
        };
        updatedInvoiceItems = [...invoiceItems, formattedInvoiceItem];
      }

      // Navigate back to EditOrderScreen with updated order
      navigation.navigate("EditOrderScreen", {
        order: updatedOrder,
        invoiceItems: updatedInvoiceItems,
      });
    } else {
      // Handle regular add to cart flow
      const uomQuantityMultiplier = getUomQuantity(item, selectedUomId);
      const backendQuantity = quantity * uomQuantityMultiplier;

      // Only add existing quantity if it's the same UOM (separate entries for different UOMs)
      var quantityToBeUpdated = backendQuantity;
      quantityToBeUpdated += getItemQuantityInCart(); // This now only finds same UOM

      // Use the custom price if we're editing
      const updateVariables = {
        cartId: cart.id,
        itemId: item.id,
        quantity: quantityToBeUpdated,
        userId: user.id,
        supplierId: supplier_id,
        itemUomId: selectedUomId === "default" ? null : selectedUomId,
      };

      // Only add custom price and notes if we're in edit mode
      if (isEditingPrice && user.driver) {
        if (config.enable_minimum_pricing) {
          updateVariables.customPrice = parseFloat(customPrice);
        }
        if (customNotes.trim()) {
          updateVariables.notes = customNotes.trim();
        }
      }

      updateItemInCart({
        variables: {
          updateItemInCartInput: updateVariables,
        },
      });

      setShowAddedToCartPopup(true);
      setTimeout(() => setShowAddedToCartPopup(false), 2500);
    }
  };

  const viewCartButtonPressed = () => {
    navigation.navigate("Cart");
  };

  // Handler for offer for free checkbox
  const handleOfferForFreeChange = (newValue) => {
    setOfferForFree(newValue);
    if (newValue) {
      setCustomPrice("0");
    } else {
      setCustomPrice(item_price);
    }
  };

  // Add this helper function to handle price changes
  const handlePriceChange = (value) => {
    const numValue = parseFloat(value);
    const minPrice = item?.min_sale_price || 0;

    // Always update the display value to allow free editing
    setCustomPrice(value);

    // Clear previous errors
    setPriceError("");

    if (value === "" || isNaN(numValue)) {
      // Empty or invalid input - show error if there's a minimum price
      if (minPrice > 0) {
        setPriceError(`Price must be at least $${minPrice.toFixed(2)}`);
      }
      setOfferForFree(false);
      return;
    }

    // Check if price is below minimum
    if (minPrice > 0 && numValue < minPrice) {
      setPriceError(`Price must be at least $${minPrice.toFixed(2)}`);
      setOfferForFree(false);
    } else if (numValue === 0 && minPrice === 0) {
      // Only allow free when no minimum price
      setOfferForFree(true);
      setPriceError("");
    } else {
      setOfferForFree(false);
      setPriceError("");
    }
  };

  // Get UOM-specific pricing and info
  const uomOptions = getUomOptions(item, user);
  const item_price = getUomPrice(item, user, selectedUomId);
  const itemHasUoms = hasUoms(item);
  const selectedUomUpc = getUomUpc(item, selectedUomId);
  const selectedUomName = getUomName(item, selectedUomId);

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return (
      <LoadingErrorStatus
        message="Uh oh! Something went wrong!"
        errorStatus={true}
      />
    );
  if (items.length === 0) {
    return (
      <LoadingErrorStatus
        message="Sorry! We don't have that item yet."
        errorStatus={true}
      />
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        <StatusBar
          backgroundColor={theme.appColors.primary}
          barStyle="light-content"
        />
        <View style={styles.safeContainer}>
          <View style={styles.itemContainer}>
            <View style={{ flexDirection: "column" }}>
              <Image
                style={styles.itemImage}
                source={{
                  uri:
                    item.image ||
                    supplierObj?.config?.defaultItemImage ||
                    "https://fakeimg.pl/100x100?text=no+image",
                }}
              />
            </View>
            <View style={styles.itemTextContainer}>
              {isBackInStock(item.back_in_stock_date) && !item.oos && (
                <View style={styles.backInStockBadge}>
                  <Text style={styles.backInStockText}>
                    {t("item.back_in_stock")}
                  </Text>
                </View>
              )}
              <View style={styles.itemDetailLine}>
                <Text
                  style={[
                    globalStyles.headlineMedium,
                    { textDecorationLine: "underline" },
                    globalStyles.text,
                    { marginBottom: 8 },
                  ]}
                >
                  {item.supplier}
                </Text>
              </View>

              <Text
                style={[
                  globalStyles.headlineMedium,
                  globalStyles.text,
                  { marginBottom: 8 },
                ]}
              >
                {item.name}
              </Text>
              {selectedUomUpc && selectedUomUpc !== "-" && (
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    UPC
                  </Text>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    {selectedUomUpc}
                  </Text>
                </View>
              )}
              {Boolean(item.unit_size) && (
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    Unit Size
                  </Text>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    {item.unit_size} ct
                  </Text>
                </View>
              )}
              {Boolean(item.size) && (
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    Net Weight
                  </Text>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    {item.size}
                  </Text>
                </View>
              )}
              {Boolean(item.moq) && (
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    MOQ
                  </Text>
                  <Text
                    style={[globalStyles.bodyLarge, globalStyles.textLight]}
                  >
                    {item.moq}
                  </Text>
                </View>
              )}
            </View>
          </View>
          {item.last_ordered_date && (
            <View style={styles.lastOrderedContainer}>
              <View style={styles.itemDetailLine}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Last Ordered
                </Text>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  {formatDate(new Date(item.last_ordered_date))}
                </Text>
              </View>
            </View>
          )}

          {item.avg_cases_per_week !== undefined &&
            item.avg_cases_per_week !== null && (
              <View style={styles.lastOrderedContainer}>
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.headlineMedium, globalStyles.text]}
                  >
                    {supplier_id == 31
                      ? "Average Cases Per Week"
                      : "Average Cases Per Order"}
                  </Text>
                  <Text
                    style={[globalStyles.headlineMedium, globalStyles.text]}
                  >
                    {item.avg_cases_per_week}
                  </Text>
                </View>
              </View>
            )}

          {item.quantity_on_hand > 0 && (
            <View style={{ marginTop: 10 }}>
              <View style={styles.itemDetailLine}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Stock Level
                </Text>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  {item.quantity_on_hand}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.pricingContainer}>
            {user.show_prices && user.doesnt_use_uom ? (
              <View>
                <View style={styles.itemDetailLine}>
                  <Text
                    style={[globalStyles.headlineMedium, globalStyles.text]}
                  >
                    Price
                  </Text>

                  {enablePriceEditing && isEditingPrice ? (
                    <Animated.View
                      entering={FadeIn.duration(300)}
                      style={{ alignItems: "flex-end" }}
                    >
                      <PriceInput
                        value={customPrice}
                        onValueChange={handlePriceChange}
                        theme={theme}
                        globalStyles={globalStyles}
                      />

                      {priceError && (
                        <Text
                          style={[
                            globalStyles.bodySmall,
                            {
                              color: theme.appColors.error || "#FF0000",
                              marginTop: 4,
                              fontWeight: "500",
                            },
                          ]}
                        >
                          {priceError}
                        </Text>
                      )}
                    </Animated.View>
                  ) : // Original price display when not editing
                  discountExists(item) ? (
                    <View
                      style={{
                        flexDirection: "row",
                        flexWrap: "wrap",
                        alignItems: "flex-start",
                      }}
                    >
                      <Text
                        style={[
                          globalStyles.headlineMedium,
                          globalStyles.text,
                          { color: theme.appColors.accent },
                        ]}
                      >
                        {user.approved
                          ? `$${
                              isEditingPrice
                                ? customPrice
                                : parseFloat(item_price).toFixed(2)
                            } `
                          : null}
                      </Text>
                      <Text
                        style={[
                          { textDecorationLine: "line-through" },
                          globalStyles.bodyLarge,
                          globalStyles.textLight,
                          styles.originalPriceText,
                        ]}
                      >
                        {user.approved
                          ? `$${parseFloat(item.price).toFixed(2)}`
                          : null}
                      </Text>
                    </View>
                  ) : (
                    <Text
                      style={[globalStyles.headlineMedium, globalStyles.text]}
                    >
                      {user.approved
                        ? `$${
                            isEditingPrice
                              ? customPrice
                              : parseFloat(item_price).toFixed(2)
                          }`
                        : null}
                    </Text>
                  )}
                </View>

                <OfferForFreeCheckbox
                  canSellForFree={canSellForFree}
                  isEditingPrice={isEditingPrice}
                  offerForFree={offerForFree}
                  onOfferForFreeChange={handleOfferForFreeChange}
                />

                <MinPrice
                  item={item}
                  enablePriceEditing={enablePriceEditing}
                  isEditingPrice={isEditingPrice}
                />
              </View>
            ) : null}
          </View>

          {/* UOM Selection */}
          {itemHasUoms && !user.doesnt_use_uom && (
            <View style={styles.uomSelectorContainer}>
              <TouchableOpacity
                style={styles.uomSelector}
                onPress={() => setUomDropdownOpen(!uomDropdownOpen)}
              >
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  UOM:{" "}
                  {uomOptions.find((opt) => opt.value === selectedUomId)
                    ?.label || "Unit"}
                </Text>
                <Ionicons
                  name={uomDropdownOpen ? "chevron-up" : "chevron-down"}
                  size={28}
                  color={theme.appColors.text}
                />
              </TouchableOpacity>

              {uomDropdownOpen && (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.uomOptionsContainer}
                  contentContainerStyle={styles.uomOptionsContent}
                >
                  {uomOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.uomOption,
                        selectedUomId === option.value &&
                          styles.selectedUomOption,
                      ]}
                      onPress={() => {
                        setSelectedUomId(option.value);
                        setUomDropdownOpen(false);
                      }}
                    >
                      <Text
                        style={[
                          globalStyles.bodyMedium,
                          globalStyles.text,
                          selectedUomId === option.value &&
                            styles.selectedUomText,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              )}
            </View>
          )}

          <View style={styles.quantityContainer}>
            <View
              style={{
                flexDirection: "column",
                justifyContent: "center",
                flexWrap: "wrap",
                verticalAlign: "middle",
              }}
            >
              <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                {user.doesnt_use_uom
                  ? "Quantity"
                  : `Quantity (${selectedUomName})`}
              </Text>
            </View>
            <NumericInput onEditQuantity={onEditQuantity} quantity={quantity} />
          </View>
          {/* MOQ warning logic - mutually exclusive conditions */}
          {user.is_user_account_selected_by_driver &&
          Number(item.moq) > 0 &&
          getItemQuantityInCart() < Number(item.moq) &&
          getItemQuantityInCart() > 0 ? (
            <View
              style={{
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="warning"
                size={16}
                color={theme.appColors.redText}
                style={{ marginRight: 5 }}
              />
              <Text
                style={[
                  globalStyles.bodyMedium,
                  globalStyles.text,
                  { color: theme.appColors.redText },
                ]}
              >
                {t("item.cart_is_less_than_moq")}
              </Text>
            </View>
          ) : user.is_user_account_selected_by_driver &&
            Number(item.moq) > 0 &&
            quantity < Number(item.moq) &&
            getItemQuantityInCart() === 0 ? (
            <View
              style={{
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="warning"
                size={16}
                color={theme.appColors.redText}
                style={{ marginRight: 5 }}
              />
              <Text
                style={[
                  globalStyles.bodyMedium,
                  globalStyles.text,
                  { color: theme.appColors.redText },
                ]}
              >
                {t("item.quantity_below_moq")}
              </Text>
            </View>
          ) : !user.is_user_account_selected_by_driver &&
            Number(item.moq) > 0 &&
            quantity < Number(item.moq) ? (
            <View
              style={{
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="warning"
                size={16}
                color={theme.appColors.redText}
                style={{ marginRight: 5 }}
              />
              <Text
                style={[
                  globalStyles.bodyMedium,
                  globalStyles.text,
                  { color: theme.appColors.redText },
                ]}
              >
                {t("item.quantity_must_be_at_least_moq")}
              </Text>
            </View>
          ) : null}

          {user.driver && isEditingPrice && (
            <Animated.View
              entering={FadeIn.duration(300)}
              style={{ marginBottom: 20, marginTop: -10 }}
            >
              <View style={styles.itemDetailLine}>
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  Notes
                </Text>
              </View>
              <TextInput
                style={{
                  borderWidth: 1,
                  borderColor: theme.appColors.textLight,
                  borderRadius: 8,
                  padding: 12,
                  marginTop: 8,
                  minHeight: 40,
                  textAlignVertical: "top",
                  ...globalStyles.bodyMedium,
                }}
                placeholder="Add notes for this item..."
                placeholderTextColor={theme.appColors.textLight}
                value={customNotes}
                onChangeText={setCustomNotes}
                multiline={true}
                numberOfLines={2}
              />
            </Animated.View>
          )}

          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              marginTop: 10,
            }}
          >
            {user.approved ? (
              <Button
                onPress={onAddToCartPressed}
                variant="light"
                size="lg"
                disabled={
                  (item.oos &&
                    !(
                      supplierObj?.id === "31" &&
                      user.is_user_account_selected_by_driver
                    )) ||
                  (item.moq &&
                    quantity < item.moq &&
                    !user.is_user_account_selected_by_driver) ||
                  (priceError && priceError.length > 0)
                }
              >
                {!item.oos ? (
                  <Text>{fromOrderEdit ? "Add To Order" : "Add To Cart"}</Text>
                ) : supplierObj?.id === "31" &&
                  user.is_user_account_selected_by_driver ? (
                  <Text>
                    {fromOrderEdit ? "Add To Order (OOS)" : "Add To Cart (OOS)"}
                  </Text>
                ) : (
                  <Text>Out of Stock</Text>
                )}
              </Button>
            ) : null}
            {!fromOrderEdit &&
              (() => {
                // Get all cart items for this item (different UOMs)
                const allCartItems = cart.cartItems.filter(
                  (cartItem) => cartItem.item_id == item.id
                );
                if (allCartItems.length === 0) return null;

                return (
                  <View style={{ alignItems: "center", marginTop: 10 }}>
                    <Text
                      style={[globalStyles.headlineSmall, globalStyles.text]}
                    >
                      Already In Cart:{" "}
                      {allCartItems
                        .map((cartItem, index) => {
                          const uomQuantity = cartItem?.item_uom_id
                            ? getUomQuantity(cartItem, cartItem.item_uom_id)
                            : 1;
                          const displayQuantity = Number(
                            (cartItem.quantity / uomQuantity).toFixed(2)
                          );
                          const uomName = cartItem?.item_uom_id
                            ? getUomName(item, cartItem.item_uom_id)
                            : "Unit";

                          return `${displayQuantity} ${uomName}${
                            index < allCartItems.length - 1 ? ", " : ""
                          }`;
                        })
                        .join("")}
                    </Text>
                  </View>
                );
              })()}
          </View>
        </View>
        <View style={{ marginTop: 20 }}>
          <ItemsSection
            variant="light"
            params={"asdasdasdasd"}
            title={"Similar Items We Recommend"}
            items={item.related_items}
            userApproved={user.approved}
            customPrices={user.custom_prices}
          />
        </View>
      </ScrollView>
      {showAddedToCartPopup && (
        <View
          style={{
            display: "flex",
            justifyContent: "space-between",
            paddingHorizontal: 35,
            flexDirection: "row",
            position: "absolute",
            bottom: 0,
            width: "100%",
            height: 60,
            backgroundColor: theme.appColors.backdropLight,
            borderTopLeftRadius: 10,
            borderTopRightRadius: 10,
          }}
        >
          <View
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            <Text style={styles.boldSecondaryText}>Item Added</Text>
          </View>
          <View
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            <TouchableOpacity onPress={viewCartButtonPressed}>
              <Text
                style={[
                  styles.boldSecondaryText,
                  { textDecorationLine: "underline" },
                ]}
              >
                View Cart
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}
