import { useTheme } from "@react-navigation/native";
import * as Application from "expo-application";
import * as Device from "expo-device";
import Constants from "expo-constants";
import { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  Platform,
  StatusBar,
} from "react-native";
import Button from "../../components/Button";
import useGlobalStyles from "../../lib/useGlobalStyles";
import AsyncStorage from "@react-native-async-storage/async-storage";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    buttonContainer: {
      alignItems: "center",
      marginBottom: 30,
      marginTop: 20,
    },
    container: {
      backgroundColor: theme.appColors.backdropNeutral,
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    header: {
      marginVertical: 20,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 10,
    },
    scrollView: {
      display: "flex",
      flexDirection: "column",
      height: "100%",
      marginHorizontal: 20,
    },
    section: {
      backgroundColor: theme.appColors.backdropLight,
      borderRadius: 8,
      marginBottom: 20,
      padding: 15,
    },
  });
  return styles;
};

export default function DebugScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();
  const [storedData, setStoredData] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getStorageData = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const result = {};
        for (const key of keys) {
          // Only include certain keys to avoid large data dumps
          if (!key.includes("token") && !key.includes("password")) {
            const value = await AsyncStorage.getItem(key);
            result[key] = value;
          }
        }
        setStoredData(result);
      } catch (error) {
        console.error("Error getting AsyncStorage data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getStorageData();
  }, []);

  const clearStorage = async () => {
    try {
      await AsyncStorage.clear();
      // Refresh data
      setIsLoading(true);
      const keys = await AsyncStorage.getAllKeys();
      const result = {};
      for (const key of keys) {
        if (!key.includes("token") && !key.includes("password")) {
          const value = await AsyncStorage.getItem(key);
          result[key] = value;
        }
      }
      setStoredData(result);
      setIsLoading(false);
    } catch (error) {
      console.error("Error clearing AsyncStorage:", error);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={[globalStyles.headlineLarge, globalStyles.text]}>
            Debug Information
          </Text>
        </View>

        <View style={styles.section}>
          <Text
            style={[
              globalStyles.headlineMedium,
              globalStyles.text,
              { marginBottom: 10 },
            ]}
          >
            App Information
          </Text>
          <View style={styles.row}>
            <Text style={globalStyles.text}>App Name:</Text>
            <Text style={globalStyles.text}>
              {Constants.expoConfig?.name || "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Version:</Text>
            <Text style={globalStyles.text}>
              {Constants.expoConfig?.version || "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Build Number:</Text>
            <Text style={globalStyles.text}>
              {Platform.OS === "ios"
                ? Constants.expoConfig?.ios?.buildNumber || "Unknown"
                : Constants.expoConfig?.android?.versionCode || "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Runtime Version:</Text>
            <Text style={globalStyles.text}>
              {Constants.expoConfig?.runtimeVersion ||
                Constants.manifest?.runtimeVersion ||
                "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>SDK Version:</Text>
            <Text style={globalStyles.text}>
              {Constants.expoConfig?.sdkVersion || "Unknown"}
            </Text>
          </View>
          {Application.applicationId && (
            <View style={styles.row}>
              <Text style={globalStyles.text}>Application ID:</Text>
              <Text style={globalStyles.text}>{Application.applicationId}</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text
            style={[
              globalStyles.headlineMedium,
              globalStyles.text,
              { marginBottom: 10 },
            ]}
          >
            Device Information
          </Text>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Device Name:</Text>
            <Text style={globalStyles.text}>
              {Device.deviceName || "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Device Type:</Text>
            <Text style={globalStyles.text}>
              {Device.deviceType === Device.DeviceType.PHONE
                ? "Phone"
                : Device.deviceType === Device.DeviceType.TABLET
                ? "Tablet"
                : "Unknown"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>OS:</Text>
            <Text style={globalStyles.text}>
              {Platform.OS === "ios" ? "iOS" : "Android"} {Platform.Version}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={globalStyles.text}>Memory:</Text>
            <Text style={globalStyles.text}>
              {Device.totalMemory
                ? `${Math.round(Device.totalMemory / (1024 * 1024))} MB`
                : "Unknown"}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text
            style={[
              globalStyles.headlineMedium,
              globalStyles.text,
              { marginBottom: 10 },
            ]}
          >
            Storage
          </Text>
          {isLoading ? (
            <Text style={globalStyles.text}>Loading storage data...</Text>
          ) : (
            Object.keys(storedData).map((key) => (
              <View key={key} style={styles.row}>
                <Text
                  style={[globalStyles.text, { flex: 1 }]}
                  numberOfLines={1}
                >
                  {key}:
                </Text>
                <Text
                  style={[globalStyles.text, { flex: 2 }]}
                  numberOfLines={2}
                >
                  {typeof storedData[key] === "string" &&
                  storedData[key].length > 30
                    ? storedData[key].substring(0, 30) + "..."
                    : storedData[key]}
                </Text>
              </View>
            ))
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            variant="light"
            size="lg"
            onPress={clearStorage}
            style={{
              backgroundColor: theme.appColors.redText,
              marginBottom: 10,
            }}
          >
            <Text style={{ color: theme.appColors.textNeutral }}>
              Clear AsyncStorage
            </Text>
          </Button>

          <Button variant="light" size="lg" onPress={() => navigation.goBack()}>
            <Text>Back to Settings</Text>
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}
