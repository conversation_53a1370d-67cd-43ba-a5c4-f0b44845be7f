import { StyleSheet, View, StatusBar } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import algoliasearch from "algoliasearch";
import {
  Configure,
  InstantSearch,
  connectStateResults,
} from "react-instantsearch-native";
import { useCustomerHiddenProductsQuery } from "../../generated/graphql";
import SearchBox from "../../components/SearchBox";
import InfiniteHits from "../../components/InfiniteHits";
import { useState, useContext, useMemo } from "react";

import { UserContext } from "../../context/userContext";

const createStyles = () => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    safeContainer: {
      flex: 1,
      marginHorizontal: 0,
    },
    /*     supplierBox: {
      width: "100%",
      backgroundColor: theme.appColors.primary,
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      padding: 15,
      paddingTop: 20,
      width: "100%",
      zIndex: 5,
    }, */
  });
  return styles;
};
const searchClient = algoliasearch(
  "65VCJQ2Y52",
  "********************************"
);

const Results = connectStateResults(({ searchState, children }) => {
  return searchState && searchState.query ? (
    children
  ) : (
    <View
      style={{
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "row",
      }}
    ></View>
  );
});

export default function SearchResultsScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const supplier = route && route.params && route.params.supplier;
  const order = route && route.params && route.params.order;
  const defaultQuery = route && route.params && route.params.defaultQuery;
  const index = "item-index";
  const theme = useTheme();
  const styles = createStyles(theme);

  const [searchQuery, setSearchQuery] = useState(defaultQuery || "");

  // Find the supplier ID if we have a supplier name
  const supplierObj = useMemo(() => {
    if (!supplier) return null;
    return user.suppliers.find((s) => s.name === supplier);
  }, [supplier, user.suppliers]);

  const addItemToOrder = async (item) => {
    if (
      order.orderItems.find(
        (orderItem) => orderItem.item_id === item.id.toString()
      ) ||
      route.params.invoiceItems.find(
        (invoiceItem) => invoiceItem.item_id === item.id
      )
    ) {
      // if the item is already in the order, navigate to the edit order screen
      navigation.navigate("EditOrderScreen", {
        order: order,
        invoiceItems: route.params.invoiceItems,
      });
      return;
    }

    // Navigate to item detail screen so user can specify UOM and quantity
    navigation.navigate("ItemDetail", {
      id: item.id,
      fromOrderEdit: true,
      order: order,
      invoiceItems: route.params.invoiceItems,
    });
  };
  const onPress = order ? addItemToOrder : null;

  const { data: hiddenProductsData } = useCustomerHiddenProductsQuery({
    fetchPolicy: "cache-and-network",
    skip: !user?.id || !supplierObj?.id,
    variables: {
      customerHiddenProductsInput: {
        customerId: user.id,
        supplierId: supplierObj?.id,
      },
    },
  });

  // Create a Set of hidden item IDs for efficient lookup
  const hiddenItemIds = useMemo(() => {
    if (!hiddenProductsData?.customerHiddenProducts?.items) return new Set();
    return new Set(
      hiddenProductsData.customerHiddenProducts.items.map((item) => item.itemId)
    );
  }, [hiddenProductsData]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeContainer}>
        <InstantSearch
          searchClient={searchClient}
          indexName={index}
          searchState={{ query: searchQuery }}
          onSearchStateChange={({ query }) => setSearchQuery(query)}
        >
          {supplier ? (
            <Configure
              filters={`supplier:"${supplier.toLowerCase()}"`}
              distinct={false}
            />
          ) : (
            <Configure
              filters={user.suppliers
                .map((supplier) => `supplier:"${supplier.name.toLowerCase()}"`)
                .join(" OR ")}
              distinct={5}
            />
          )}

          <SearchBox defaultRefinement={searchQuery} />
          <Results>
            <InfiniteHits
              supplier={supplier}
              searchQuery={searchQuery}
              user={user}
              onPress={onPress}
              showButton={route?.params?.showButton}
              hiddenItemIds={hiddenItemIds}
            />
          </Results>
        </InstantSearch>
      </View>
    </SafeAreaView>
  );
}
