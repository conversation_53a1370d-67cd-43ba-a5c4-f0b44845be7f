import { StyleSheet, View, ScrollView, StatusBar } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { format, sub, isAfter } from "date-fns";

import Text from "../../components/Text";
import { useGetOrderStatusesQuery } from "../../generated/graphql";
import DeliveryStatus from "../../components/DeliveryStatus";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";

const styles = StyleSheet.create({
  bodyText: {
    fontSize: 15,
  },
  container: {
    backgroundColor: "white",
    flex: 1,
    paddingTop: StatusBar.currentHeight,
  },
});

export default function OrderStatusScreen({ navigation, route }) {
  const { orderId } = route.params;

  const {
    loading: getOrderStatusesLoading,
    data: getOrderStatusesData,
    error: getOrderStatusesError,
  } = useGetOrderStatusesQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      orderId: orderId,
    },
  });

  if (getOrderStatusesLoading && !getOrderStatusesData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (getOrderStatusesError)
    return (
      <LoadingErrorStatus
        message={getOrderStatusesError.message}
        errorStatus={true}
      />
    );

  const orderStatuses = getOrderStatusesData.orderStatuses;

  const handleSeeDetails = (orderStatus) => {
    navigation.navigate("SupplierItems", {
      orderStatus: orderStatus,
      title: orderStatus.name,
    });
  };

  const statusParser = (orderStatus) => {
    const date = new Date();
    if (isAfter(sub(date, { days: 1 }), orderStatus.delivery_date)) {
      return "Delivered";
    }
    if (isAfter(sub(date, { days: 1 }), orderStatus.delivering_date)) {
      return "Delivering";
    }
    if (isAfter(date, orderStatus.submission_date)) {
      return "Submitted";
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View
          style={{
            alignItems: "center",
            justifyContent: "center",
            paddingTop: 20,
          }}
        >
          <Text style={styles.bodyText}>
            Click each supplier to see order breakdown.
          </Text>
        </View>
        {orderStatuses.map((orderStatus) => {
          return (
            <View
              key={orderStatus.id}
              id={orderStatus.id}
              style={{ marginVertical: 10 }}
            >
              <DeliveryStatus
                orderStatus={orderStatus}
                name={orderStatus.name}
                date={
                  orderStatus.delivery_date
                    ? format(new Date(orderStatus.delivery_date), "MM/dd/yyyy")
                    : "TBD"
                }
                status={statusParser(orderStatus)}
                handleSeeDetails={handleSeeDetails}
              />
            </View>
          );
        })}
      </ScrollView>
    </SafeAreaView>
  );
}
