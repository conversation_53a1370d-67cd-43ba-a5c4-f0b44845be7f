import { Text, ActivityIndicator, StyleSheet, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { MaterialIcons } from "@expo/vector-icons";
import useGlobalStyles from "../lib/useGlobalStyles";

const LoadingErrorStatus = ({ message, errorStatus, theme: passedTheme }) => {
  const theme = passedTheme || useTheme();
  const styles = StyleSheet.create({
    errorIconStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginTop: "50%",
      padding: 10,
      textAlign: "center",
    },
    errorMessageStyle: {
      alignItems: "center",
      flexDirection: "row",
      fontFamily: theme.fonts.medium.fontFamily,
      fontSize: 24,
      justifyContent: "center",
      textAlign: "center",
    },
    loadingMessageStyle: {
      alignItems: "center",
      flexDirection: "row",
      fontFamily: theme.fonts.medium.fontFamily,
      fontSize: 24,
      justifyContent: "center",
      marginTop: "50%",
      padding: 10,
      textAlign: "center",
    },
  });

  if (errorStatus) {
    return (
      <View>
        <MaterialIcons
          name="error"
          size={52}
          color={theme.appColors.textLight}
          style={styles.errorIconStyle}
        />
        <Text style={styles.errorMessageStyle}>{message}</Text>
      </View>
    );
  } else {
    return (
      <View>
        <Text style={styles.loadingMessageStyle}>{message}</Text>
        <ActivityIndicator size="large" color={theme.appColors.backdropDark} />
      </View>
    );
  }
};

LoadingErrorStatus.propTypes = {
  message: PropTypes.string.isRequired,
  errorStatus: PropTypes.bool.isRequired,
  theme: PropTypes.object,
};

export default LoadingErrorStatus;
