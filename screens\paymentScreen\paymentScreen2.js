import { useContext, useRef } from "react";
import { StyleSheet, StatusBar } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useGetBalanceLinkQuery } from "../../generated/graphql";

import { UserContext } from "../../context/userContext";
import { WebView } from "react-native-webview";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    flex: 1,
    paddingTop: StatusBar.currentHeight,
  },
});

export default function PaymentScreen2({ navigation }) {
  const { user } = useContext(UserContext);
  const webViewRef = useRef(null);
  const { loading, data, error } = useGetBalanceLinkQuery({
    fetchPolicy: "network-only",
    variables: {
      businessId: user.id,
    },
  });

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  const link = data.balanceLink.link || "";
  return (
    <SafeAreaView style={styles.container}>
      <WebView
        ref={webViewRef}
        setSupportMultipleWindows={false}
        onShouldStartLoadWithRequest={(event) => {
          if (
            (event.navigationType === "other" ||
              event.title === "Balance Checkout") &&
            event.canGoBack === true
          ) {
            navigation.navigate("AddPaymentMethodRedirect", {
              url: event.url,
            });
            return false;
          }
          return true;
        }}
        style={{ flex: 1 }}
        source={{
          uri: link ? link : "",
        }}
      />
    </SafeAreaView>
  );
}
