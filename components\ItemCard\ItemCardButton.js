import { StyleSheet, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@react-navigation/native";

import Text from "../Text";
import useGlobalStyles from "../../lib/useGlobalStyles";

const styles = StyleSheet.create({
  addToCartButton: {
    alignContent: "center",
    borderRadius: 15,
    height: 30,
    justifyContent: "center",
    paddingLeft: 1,
    shadowColor: "black",
    shadowOffset: { height: 1, width: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 0.5,
    width: 30,
  },
  content: {
    textAlign: "center",
  },
});

export const ItemCardButton = ({ onPress, quantity }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();

  const itemsInCart = quantity > 0;
  return (
    <TouchableOpacity
      style={[
        styles.addToCartButton,
        {
          backgroundColor: itemsInCart
            ? theme.appColors.primary
            : theme.appColors.textLight,
        },
      ]}
      onPress={onPress}
    >
      {itemsInCart ? (
        <Text
          style={[
            globalStyles.headlineLarge,
            globalStyles.textNeutral,
            styles.content,
          ]}
        >
          {quantity}
        </Text>
      ) : (
        <Ionicons
          style={styles.content}
          name="add"
          size={30}
          color={theme.colors.text}
        />
      )}
    </TouchableOpacity>
  );
};

ItemCardButton.propTypes = {
  onPress: PropTypes.func.isRequired,
  quantity: PropTypes.number,
};

export default ItemCardButton;
