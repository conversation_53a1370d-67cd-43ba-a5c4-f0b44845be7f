query GetItemsV2($itemsInputV2: ItemsInputV2!) {
  itemsV2(itemsInput: $itemsInputV2) {
        totalCount
        items {
            id
            name
            unit_size
            qoh
            price
            discounted_price
            upc1
            upc2
            nacs_category
            nacs_subcategory
            image
            supplier_code
            supplier
            last_ordered_date
            avg_cases_per_week
            size
            tags
            oos
            local_item
            outdated
            created_at
            archived
            qb_id
            qb_sync_token
            updated_at
            metadata
            crv
            qty_on_hand
            cog_price
            moq
            uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
        }
  }
}

