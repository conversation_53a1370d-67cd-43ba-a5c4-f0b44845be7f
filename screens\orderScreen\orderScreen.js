import { useEffect, useState, useContext, useRef } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  PixelRatio,
  AppState,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import { createStackNavigator } from "@react-navigation/stack";
import { formatDateWithoutZero } from "../../lib/formatDate";

import {
  useGetOrdersQuery,
  useGetSupplierConfigByIdQuery,
} from "../../generated/graphql";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import OrderDetailScreen from "../orderDetailScreen/orderDetailScreen";

import InvoiceScreen from "../invoiceScreen/invoiceScreen";
import InvoiceItemDetailScreen from "../invoiceItemDetailScreen/invoiceItemDetailScreen";
import RequestCreditsScreen from "../requestCreditsScreen/requestCreditsScreen";
import ActionItemsScreen from "../actionItemsScreen/actionItemsScreen";
import OrderCheckedInScreen from "../orderCheckedInScreen/orderCheckedInScreen";
import SelectItemsScreen from "../selectItemsScreen/selectItemsScreen";
import SearchResultsScreen from "../searchResultsScreen/searchResultsScreen";
import OrderStatusScreen from "../orderStatusScreen/orderStatusScreen";
import SupplierItemsScreen from "../supplierItemsScreen/supplierItemsScreen";
import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import TagList, { Tag } from "../../components/TagList";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import OrderSupplierIcon from "../../components/OrderSupplierIcon";
import NetTermsDisplay from "../../components/NetTermsDisplay";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { getTotal } from "../../lib/getTotal";
import CreditRequestsConfirmationScreen from "../requestCreditsScreen/creditRequestsConfirmationScreen";
import { posthog } from "../../src/posthog";
import { useTranslation } from "react-i18next";

const fontScale = PixelRatio.getFontScale();

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    content: {
      backgroundColor: theme.appColors.backdropLight,
      flex: 1,
      paddingTop: 15,
    },
    orderInfo: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    orderList: {
      paddingTop: 10,
    },
    orderStatus: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 5,
    },
    orderView: {
      backgroundColor: "white",
      borderRadius: 15,
      marginBottom: 5,
      marginHorizontal: 10,
      paddingHorizontal: 25,
      paddingVertical: 15,
      shadowColor: "black",
      shadowOffset: {
        height: 2,
        width: 2,
      },
      shadowOpacity: 0.15,
      shadowRadius: 3,
    },
    tags: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 10,
      paddingHorizontal: 15,
    },
  });
  return styles;
};

function OrderComponent({ navigation }) {
  const { user } = useContext(UserContext);

  const supplier = user.suppliers.length > 0 ? user.suppliers[0] : {};

  const { data } = useGetSupplierConfigByIdQuery({
    variables: { supplierId: supplier.id },
    skip: !supplier.id,
  });
  const enableBouncedCheckFeature =
    data?.supplierConfig?.enable_bounced_check_tracking || false;

  const [query, setQuery] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [orders, setOrders] = useState([]);

  const { t } = useTranslation();
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();

  const STATUSES = [
    t("order.submitted"),
    t("order.in_transit"),
    t("order.delivered"),
    t("order.canceled"),
  ];

  const COLORS = ["#BF8D2C", "#0E9BD8", "#3FAE65", "#B66745"];

  const tags = STATUSES.map((name, i) => ({ color: COLORS[i], name }));

  const normalizeStatus = ({ status }) =>
    status.charAt(0).toUpperCase() + status.slice(1);

  const getColor = (item) => COLORS[STATUSES.indexOf(normalizeStatus(item))];

  const getOrderDateText = (order, expanded = false) => {
    const status = normalizeStatus(order);

    let date, dateText;
    if (status === STATUSES[3] || !order.delivery_date) {
      date = order.date_submitted;
      dateText = "Submitted";
    } else {
      date = order.delivery_date;
      dateText =
        status === STATUSES[2]
          ? "Delivered"
          : expanded
          ? "Estimated delivery"
          : "Est. delivery";
    }

    return `${dateText}: ${formatDateWithoutZero(date)}`;
  };

  const {
    loading: getOrdersLoading,
    data: getOrdersData,
    error: getOrdersError,
    fetchMore: fetchMoreOrdersQuery,
    refetch: getOrdersRefetch,
  } = useGetOrdersQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getOrdersInput: {
        userId: user.id,
        pagination: {
          offset: 0,
          limit: 5,
        },
        query,
        status: selectedTag ? selectedTag.status : null,
      },
    },
  });

  useEffect(() => {
    if (!getOrdersLoading && getOrdersData) {
      setOrders(getOrdersData.orders);
    }
  }, [getOrdersLoading, getOrdersData]);

  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", async () => {
      await getOrdersRefetch();
    });
    // if (user.driver && user.suppliers.length && user.suppliers[0].id === "68") {
    //   setSelectedTag({
    //     status: tags[0].name,
    //     index: 0,
    //   });
    // }
    return unsubscribe;
  }, []);

  const orderPressed = (order) => {
    posthog.capture("order_clicked", {
      id: order.id,
    });
    navigation.navigate("OrderDetail", {
      orderId: order.order_number,
    });
  };

  const onSelectTag = (tag, index) => {
    if (selectedTag && index === selectedTag.index) {
      setSelectedTag(null);
    } else {
      setSelectedTag({
        status: tag.name,
        index: index,
      });
    }
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <Header
        title={t("order.orders")}
        rightContent={<NetTermsDisplay netTermsDays={user.net_terms_days} />}
      >
        <SearchBar
          onInput={setQuery}
          placeholder={t("search.search_by_order_number")}
        />
      </Header>
      <View style={styles.content}>
        {getOrdersLoading && !getOrdersData && !getOrdersError && (
          <LoadingErrorStatus message="Loading..." errorStatus={false} />
        )}
        {getOrdersError && (
          <LoadingErrorStatus
            message={getOrdersError.message}
            errorStatus={true}
          />
        )}
        {!getOrdersLoading && !getOrdersError && getOrdersData && (
          <>
            <View style={styles.tags}>
              <TagList
                bold
                expand
                onSelectTag={onSelectTag}
                selectedTag={selectedTag}
                tags={tags}
              />
            </View>
            <FlatList
              data={orders}
              style={styles.orderList}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.orderView}
                  key={item.id}
                  onPress={() => orderPressed(item)}
                >
                  <View style={styles.orderInfo}>
                    <View>
                      <Text
                        style={[globalStyles.headlineLarge, globalStyles.text]}
                      >
                        {`${
                          (item.invoice
                            ? item.invoice.subtotal
                            : item.subtotal) >= 0
                            ? "Order"
                            : "Credit"
                        } #${item.order_number}`}
                      </Text>
                      {item.invoice &&
                      user.suppliers[0].id != "68" &&
                      (item.invoice.payment_status === "bounced" ||
                        Math.abs(
                          (item.invoice.subtotal || 0) -
                            (item.invoice.paid || 0) -
                            (item.invoice.credit || 0)
                        ) > 1e-10) ? (
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginTop: 5,
                          }}
                        >
                          <Tag
                            bold
                            color={theme.appColors.redText}
                            highlighted
                            label={
                              enableBouncedCheckFeature &&
                              item.invoice.payment_status === "bounced"
                                ? `$${(item.invoice.subtotal || 0).toFixed(
                                    2
                                  )} (Bounced)`
                                : `$${(
                                    (item.invoice.subtotal || 0) -
                                    (item.invoice.paid || 0) -
                                    (item.invoice.credit || 0)
                                  ).toFixed(2)}`
                            }
                          />
                        </View>
                      ) : null}
                    </View>
                    <View>
                      {user.show_prices ? (
                        <Text
                          style={[
                            globalStyles.headlineLarge,
                            globalStyles.text,
                            { textAlign: "right", marginBottom: 5 },
                          ]}
                        >
                          $
                          {item.invoice?.subtotal?.toFixed(2) ||
                            item.invoice?.total?.toFixed(2) ||
                            getTotal(item.orderItems)}
                        </Text>
                      ) : (
                        <Text></Text>
                      )}
                      <Text
                        style={[
                          globalStyles.bodyLarge,
                          globalStyles.text,
                          { textAlign: "right" },
                        ]}
                      >
                        {item.orderItems.length} Items
                      </Text>
                    </View>
                  </View>
                  <View style={styles.orderStatus}>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Tag
                        bold
                        color={getColor(item)}
                        highlighted
                        label={normalizeStatus(item)}
                      />
                    </View>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      {getOrderDateText(item)}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              onEndReached={() => {
                fetchMoreOrdersQuery({
                  variables: {
                    getOrdersInput: {
                      userId: user.id,
                      pagination: {
                        offset: getOrdersData.orders.length,
                        limit: 5,
                      },
                      query,
                      status: selectedTag ? selectedTag.status : null,
                    },
                  },
                  updateQuery: (previousResult, { fetchMoreResult }) => {
                    // Don't do anything if there weren't any new items
                    if (
                      !fetchMoreResult ||
                      fetchMoreResult.orders.length === 0
                    ) {
                      return orders;
                    }
                    return {
                      // Append the new feed results to the old one, filtering out duplicates
                      orders: Array.from(
                        new Map(
                          orders
                            .concat(fetchMoreResult.orders)
                            .map((order) => [order.id, order])
                        ).values()
                      ),
                    };
                  },
                });
              }}
              onEndReachedThreshold={0.3}
            />
          </>
        )}
      </View>
    </SafeAreaView>
  );
}

const OrderStack = createStackNavigator();

export default function OrderScreen() {
  return (
    <OrderStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 25 / fontScale,
        },
      })}
    >
      <OrderStack.Screen
        name="OrdersScreen"
        component={OrderComponent}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{ title: "Item Details" }}
      />
      <OrderStack.Screen
        name="OrderDetail"
        component={OrderDetailScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="RequestCreditsScreen"
        component={RequestCreditsScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="CreditRequestsConfirmationScreen"
        component={CreditRequestsConfirmationScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="InvoiceScreen"
        component={InvoiceScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="InvoiceItemDetailScreen"
        component={InvoiceItemDetailScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="ActionItemsScreen"
        component={ActionItemsScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="OrderCheckedInScreen"
        component={OrderCheckedInScreen}
        options={{ headerShown: false }}
      />
      <OrderStack.Screen
        name="SelectItems"
        component={SelectItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
      <OrderStack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{ title: "Search Results" }}
      />
      <OrderStack.Screen
        name="OrderStatus"
        component={OrderStatusScreen}
        options={{ title: "Order Status" }}
      />
      <OrderStack.Screen
        name="SupplierItems"
        component={SupplierItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
    </OrderStack.Navigator>
  );
}
