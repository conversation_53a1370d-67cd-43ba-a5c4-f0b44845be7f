import { useRef } from "react";
import { StyleSheet, StatusBar } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { WebView } from "react-native-webview";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    flex: 1,
    paddingTop: StatusBar.currentHeight,
  },
});

export default function PaymentScreen3({ navigation, route }) {
  const webViewRef = useRef(null);
  const { url } = route.params;

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        ref={webViewRef}
        onNavigationStateChange={(event) => {
          if (event.title === "Redirecting…") {
            navigation.goBack();
          }
        }}
        style={{ flex: 1 }}
        source={{
          uri: url,
        }}
      />
    </SafeAreaView>
  );
}
