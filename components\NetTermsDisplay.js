import { View, StyleSheet } from "react-native";
import PropTypes from "prop-types";
import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    title: {
      fontSize: 16,
    },
  });
  return styles;
};

const termsMap = {
  0: "On Delivery",
};

const NetTermsDisplay = ({ netTermsDays }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);

  if (netTermsDays == null) {
    return null;
  }

  return (
    <View style={{ alignItems: "flex-end", color: "red" }}>
      <Text style={[globalStyles.bodySmall, globalStyles.textNeutral]}>
        Net Terms
      </Text>
      <Text
        style={[
          globalStyles.headlineLarge,
          globalStyles.textNeutral,
          styles.title,
        ]}
      >
        {termsMap[netTermsDays] ?? netTermsDays + " days"}
      </Text>
    </View>
  );
};

NetTermsDisplay.propTypes = {
  netTermsDays: PropTypes.number,
};

export default NetTermsDisplay;
