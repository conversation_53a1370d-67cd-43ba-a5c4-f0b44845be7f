PODS:
  - BEM<PERSON><PERSON>ckBox (1.4.1)
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EASClient (0.13.3):
    - ExpoModulesCore
  - EXApplication (6.0.2):
    - ExpoModulesCore
  - EXConstants (17.0.7):
    - ExpoModulesCore
  - EXJSONUtils (0.14.0)
  - EXManifests (0.15.6):
    - ExpoModulesCore
  - EXNotifications (0.29.13):
    - ExpoModulesCore
  - Expo (52.0.37):
    - ExpoModulesCore
  - expo-dev-client (5.0.12):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (5.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Main (= 5.0.29)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Main (5.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Unsafe (5.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu (6.0.19):
    - DoubleConversion
    - expo-dev-menu/Main (= 6.0.19)
    - expo-dev-menu/ReactNativeCompatibles (= 6.0.19)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu-interface (1.9.3)
  - expo-dev-menu/Main (6.0.19):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (6.0.19):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/SafeAreaView (6.0.19):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/Vendored (6.0.19):
    - DoubleConversion
    - expo-dev-menu/SafeAreaView
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAsset (11.0.4):
    - ExpoModulesCore
  - ExpoCamera (16.0.17):
    - ExpoModulesCore
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - ExpoDevice (7.0.2):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.11):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoLinking (7.0.5):
    - ExpoModulesCore
  - ExpoLocalization (16.0.1):
    - ExpoModulesCore
  - ExpoModulesCore (2.2.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoRandom (14.0.1):
    - ExpoModulesCore
  - ExpoSplashScreen (0.29.22):
    - ExpoModulesCore
  - ExpoWebBrowser (14.0.2):
    - ExpoModulesCore
  - ExpoZebraPrinter (1.0.0):
    - ExpoModulesCore
  - EXStructuredHeaders (4.0.0)
  - EXUpdates (0.27.1):
    - DoubleConversion
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - ReachabilitySwift
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - EXUpdatesInterface (1.0.0):
    - ExpoModulesCore
  - FBLazyVector (0.76.7)
  - fmt (9.1.0)
  - glog (0.3.5)
  - hermes-engine (0.76.7):
    - hermes-engine/Pre-built (= 0.76.7)
  - hermes-engine/Pre-built (0.76.7)
  - MultiplatformBleAdapter (0.2.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.76.7)
  - RCTRequired (0.76.7)
  - RCTTypeSafety (0.76.7):
    - FBLazyVector (= 0.76.7)
    - RCTRequired (= 0.76.7)
    - React-Core (= 0.76.7)
  - ReachabilitySwift (5.2.4)
  - React (0.76.7):
    - React-Core (= 0.76.7)
    - React-Core/DevSupport (= 0.76.7)
    - React-Core/RCTWebSocket (= 0.76.7)
    - React-RCTActionSheet (= 0.76.7)
    - React-RCTAnimation (= 0.76.7)
    - React-RCTBlob (= 0.76.7)
    - React-RCTImage (= 0.76.7)
    - React-RCTLinking (= 0.76.7)
    - React-RCTNetwork (= 0.76.7)
    - React-RCTSettings (= 0.76.7)
    - React-RCTText (= 0.76.7)
    - React-RCTVibration (= 0.76.7)
  - React-callinvoker (0.76.7)
  - React-Core (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-Core/RCTWebSocket (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.76.7)
    - React-Core/CoreModulesHeaders (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.76.7)
    - ReactCodegen
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.76.7):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-debug (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor (= 0.76.7)
    - React-timing (= 0.76.7)
  - React-debug (0.76.7)
  - React-defaultsnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.7)
    - React-Fabric/attributedstring (= 0.76.7)
    - React-Fabric/componentregistry (= 0.76.7)
    - React-Fabric/componentregistrynative (= 0.76.7)
    - React-Fabric/components (= 0.76.7)
    - React-Fabric/core (= 0.76.7)
    - React-Fabric/dom (= 0.76.7)
    - React-Fabric/imagemanager (= 0.76.7)
    - React-Fabric/leakchecker (= 0.76.7)
    - React-Fabric/mounting (= 0.76.7)
    - React-Fabric/observers (= 0.76.7)
    - React-Fabric/scheduler (= 0.76.7)
    - React-Fabric/telemetry (= 0.76.7)
    - React-Fabric/templateprocessor (= 0.76.7)
    - React-Fabric/uimanager (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.7)
    - React-Fabric/components/root (= 0.76.7)
    - React-Fabric/components/view (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.7)
    - React-FabricComponents/textlayoutmanager (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.7)
    - React-FabricComponents/components/iostextinput (= 0.76.7)
    - React-FabricComponents/components/modal (= 0.76.7)
    - React-FabricComponents/components/rncore (= 0.76.7)
    - React-FabricComponents/components/safeareaview (= 0.76.7)
    - React-FabricComponents/components/scrollview (= 0.76.7)
    - React-FabricComponents/components/text (= 0.76.7)
    - React-FabricComponents/components/textinput (= 0.76.7)
    - React-FabricComponents/components/unimplementedview (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.76.7)
    - RCTTypeSafety (= 0.76.7)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.76.7)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.7)
  - React-featureflagsnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.76.7)
    - React-jsi
    - React-jsiexecutor (= 0.76.7)
    - React-jsinspector
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.7):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.7):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-perflogger (= 0.76.7)
  - React-jsinspector (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor (= 0.76.7)
  - React-jsitracing (0.76.7):
    - React-jsi
  - React-logger (0.76.7):
    - glog
  - React-Mapbuffer (0.76.7):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-ble-plx (3.5.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - MultiplatformBleAdapter (= 0.2.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-safe-area-context (4.12.0):
    - React-Core
  - react-native-webview (13.12.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-zebra-bluetooth-printer (1.0.12):
    - React
  - React-nativeconfig (0.76.7)
  - React-NativeModulesApple (0.76.7):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.7):
    - DoubleConversion
    - RCT-Folly (= 2024.01.01.00)
  - React-performancetimeline (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.7):
    - React-Core/RCTActionSheetHeaders (= 0.76.7)
  - React-RCTAnimation (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.7):
    - React-Core/RCTLinkingHeaders (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.7)
  - React-RCTNetwork (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.7):
    - React-Core/RCTTextHeaders (= 0.76.7)
    - Yoga
  - React-RCTVibration (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.7)
  - React-rendererdebug (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.76.7)
  - React-RuntimeApple (0.76.7):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.7):
    - React-jsi (= 0.76.7)
  - React-RuntimeHermes (0.76.7):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.7)
  - React-utils (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.76.7)
  - ReactCodegen (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.7):
    - ReactCommon/turbomodule (= 0.76.7)
  - ReactCommon/turbomodule (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - ReactCommon/turbomodule/bridging (= 0.76.7)
    - ReactCommon/turbomodule/core (= 0.76.7)
  - ReactCommon/turbomodule/bridging (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
  - ReactCommon/turbomodule/core (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-debug (= 0.76.7)
    - React-featureflags (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - React-utils (= 0.76.7)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNGestureHandler (2.20.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNPermissions (4.1.5):
    - React-Core
  - RNReanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated/apple (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.8.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - scandit-datacapture-frameworks-barcode (6.28.4):
    - scandit-datacapture-frameworks-core (= 6.28.4)
    - ScanditBarcodeCapture (= 6.28.4)
  - scandit-datacapture-frameworks-core (6.28.4):
    - ScanditCaptureCore (= 6.28.4)
  - scandit-react-native-datacapture-barcode (6.28.4):
    - React
    - scandit-datacapture-frameworks-barcode (= 6.28.4)
    - scandit-react-native-datacapture-core (= 6.28.4)
  - scandit-react-native-datacapture-core (6.28.4):
    - React
    - scandit-datacapture-frameworks-core (= 6.28.4)
  - ScanditBarcodeCapture (6.28.4):
    - ScanditCaptureCore (= 6.28.4)
  - ScanditCaptureCore (6.28.4)
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)
  - ZXingObjC/Core (3.6.9)
  - ZXingObjC/OneD (3.6.9):
    - ZXingObjC/Core
  - ZXingObjC/PDF417 (3.6.9):
    - ZXingObjC/Core

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EASClient (from `../node_modules/expo-eas-client/ios`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../node_modules/expo-notifications/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoCamera (from `../node_modules/expo-camera/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoLocalization (from `../node_modules/expo-localization/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoRandom (from `../node_modules/expo-random/ios`)
  - ExpoSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - ExpoWebBrowser (from `../node_modules/expo-web-browser/ios`)
  - ExpoZebraPrinter (from `../modules/expo-zebra-printer/ios`)
  - EXStructuredHeaders (from `../node_modules/expo-structured-headers/ios`)
  - EXUpdates (from `../node_modules/expo-updates/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-ble-plx (from `../node_modules/react-native-ble-plx`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - react-native-zebra-bluetooth-printer (from `../node_modules/react-native-zebra-bluetooth-printer`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - scandit-react-native-datacapture-barcode (from `../node_modules/scandit-react-native-datacapture-barcode`)
  - scandit-react-native-datacapture-core (from `../node_modules/scandit-react-native-datacapture-core`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - BEMCheckBox
    - MultiplatformBleAdapter
    - ReachabilitySwift
    - scandit-datacapture-frameworks-barcode
    - scandit-datacapture-frameworks-core
    - ScanditBarcodeCapture
    - ScanditCaptureCore
    - SocketRocket
    - ZXingObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EASClient:
    :path: "../node_modules/expo-eas-client/ios"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../node_modules/expo-notifications/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoCamera:
    :path: "../node_modules/expo-camera/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoLocalization:
    :path: "../node_modules/expo-localization/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoRandom:
    :path: "../node_modules/expo-random/ios"
  ExpoSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  ExpoWebBrowser:
    :path: "../node_modules/expo-web-browser/ios"
  ExpoZebraPrinter:
    :path: "../modules/expo-zebra-printer/ios"
  EXStructuredHeaders:
    :path: "../node_modules/expo-structured-headers/ios"
  EXUpdates:
    :path: "../node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-ble-plx:
    :path: "../node_modules/react-native-ble-plx"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  react-native-zebra-bluetooth-printer:
    :path: "../node_modules/react-native-zebra-bluetooth-printer"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  scandit-react-native-datacapture-barcode:
    :path: "../node_modules/scandit-react-native-datacapture-barcode"
  scandit-react-native-datacapture-core:
    :path: "../node_modules/scandit-react-native-datacapture-core"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EASClient: 68ffef26f551980423751c44c870759842b3340f
  EXApplication: 27a524f5c3e671c6218220fba04752629466a1a9
  EXConstants: 0472d3c23d97943fe3505306daf9cde8024365c2
  EXJSONUtils: 01fc7492b66c234e395dcffdd5f53439c5c29c93
  EXManifests: b1586049a30ba7ec9ca7ec9afbd4cc233c4b2316
  EXNotifications: 08cc7c5a100c2edc3a01227cfdfcaf17746189d7
  Expo: f1d13d22815979ad85a0a59fcf6d23660c813ecd
  expo-dev-client: be653fa8a57337215e8557c07ddced0e8c576af6
  expo-dev-launcher: c2828ac93e56a5ae0278dc86296272c90376d63d
  expo-dev-menu: c05ba94a0e32c90493005178a26ecea66e941462
  expo-dev-menu-interface: 00dc42302a72722fdecec3fa048de84a9133bcc4
  ExpoAsset: a4cbc27a7cd24a6e87eb719603790b2cfa8dd326
  ExpoCamera: 66fd71366b165e97a1676f6a5d906f4a35c89315
  ExpoDevice: 82da2577196c2ddee18189da65f754ea83003d2e
  ExpoFileSystem: 4d2e7f77a41cfde8c3ae98580d872cd49437f993
  ExpoFont: 773955186469acc5108ff569712a2d243857475f
  ExpoKeepAwake: 2a5f15dd4964cba8002c9a36676319a3394c85c7
  ExpoLinking: 0381341519ca7180a3a057d20edb1cf6a908aaf4
  ExpoLocalization: e36b911e04d371c6c6624ef818e56229bf51c498
  ExpoModulesCore: f9976411ca33b45435bf26b58c1facd4648a5d50
  ExpoRandom: 099ddce39b39ffb984b45118d031e8713e962a50
  ExpoSplashScreen: 48a5b55b370aaf48e8be0c7846c487e583067164
  ExpoWebBrowser: 6890a769e6c9d83da938dceb9a03e764afc3ec9c
  ExpoZebraPrinter: 361ac1b1d2dec3e0f2533eb0d41135316c8cd45a
  EXStructuredHeaders: 09c70347b282e3d2507e25fb4c747b1b885f87f6
  EXUpdates: 22559e1e80eb152df3466752ccad7afbccc22350
  EXUpdatesInterface: 1dcebac98ac5dad4289e6ff2bd5616822e894397
  FBLazyVector: ca8044c9df513671c85167838b4188791b6f37e1
  fmt: 10c6e61f4be25dc963c36bd73fc7b1705fe975be
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  hermes-engine: eb4a80f6bf578536c58a44198ec93a30f6e69218
  MultiplatformBleAdapter: b1fddd0d499b96b607e00f0faa8e60648343dc1d
  RCT-Folly: bf5c0376ffe4dd2cf438dcf86db385df9fdce648
  RCTDeprecation: 7691283dd69fed46f6653d376de6fa83aaad774c
  RCTRequired: eac044a04629288f272ee6706e31f81f3a2b4bfe
  RCTTypeSafety: cfe499e127eda6dd46e5080e12d80d0bfe667228
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: 1f3737a983fdd26fb3d388ddbca41a26950fe929
  React-callinvoker: 5c15ac628eab5468fe0b4dc453495f4742761f00
  React-Core: 4b90a977a5b2777fd8f4a8db7325a83431ecd2d8
  React-CoreModules: 385bbacfa34ac9208aa24f239a5184fa7ab1cd28
  React-cxxreact: 3e09bcdf1f86b931b5e96bf5429d7c274a0ec168
  React-debug: 2086b55a5e55fb0abae58c42b8f280ebd708c956
  React-defaultsnativemodule: ac85ea3f584aa508402544ebbb96f8095dbbf9b2
  React-domnativemodule: 440afe80468b67462618dec536eaa83902ed547d
  React-Fabric: 5b8373d1bd34bf269b13529a0ebee0643165ccf8
  React-FabricComponents: 3f8528c3ed060464a120e161ffaef9307a88817b
  React-FabricImage: 8efa4e206b1e5cf2e8e1e48fd345619c5c0484f4
  React-featureflags: 4503c901bf16b267b689e8a1aed24e951e0b091b
  React-featureflagsnativemodule: a80f7cc87e369f6e43c4484c4133f1ab7f8181d8
  React-graphics: c619a6e974baf9a7dbae8442944c7b7408391d46
  React-hermes: 24bfc254f1ba83182d4936641898fe963af343fb
  React-idlecallbacksnativemodule: 51a6e112c0ae1667f578ad991104cdb3ce9cb572
  React-ImageManager: ba9c89729be310413c610444a658fac505253d2c
  React-jserrorhandler: bf16ea495377b22223bf93f3ef6d0711b9852613
  React-jsi: ede7e8c96f997f8772871c82688cea53c1ffb148
  React-jsiexecutor: fc9b287189ce800a92d5ab4e7291508eacaab451
  React-jsinspector: fa5e8b22102b599c2bb2aeafebbf957a1ab836da
  React-jsitracing: f38c15aeb910bafcf3ba2e24af8c92e6af4ce1d4
  React-logger: f9d104eace4ce03d7d5ab96802069d9905082225
  React-Mapbuffer: 23ffe602d0f5ca53b861ef8534cb8c63c4479671
  React-microtasksnativemodule: 258cde5410c578c4739e1d3a3bc902b7a28d4625
  react-native-ble-plx: 5d4c4f6b8e2fe21d53b772a867d9b58b0e1c2215
  react-native-date-picker: 312c387d2ff873c66c5e4cf78ff6827fa91644e2
  react-native-safe-area-context: 142fade490cbebbe428640b8cbdb09daf17e8191
  react-native-webview: b034adbe6dc6cc783c3270b6e47298bc1dfdb632
  react-native-zebra-bluetooth-printer: 05da2d540b1c35e9bc6d9fcbfe989242cb723950
  React-nativeconfig: 67fa7a63ea288cb5b1d0dd2deaf240405fec164f
  React-NativeModulesApple: cbf1a34443e1f67b56344547f4b0af69e1c685ba
  React-perflogger: f02ee21d98773121d77993b3c1a8be445840fae3
  React-performancetimeline: 7021d68884291b649b4c39ecb71e0fd3a2e53a59
  React-RCTActionSheet: ad84d5a0bd1ad1782f0b78b280c6f329ad79a53a
  React-RCTAnimation: 388460f7c124c76e337c6646738a83d6ea147095
  React-RCTAppDelegate: 0d66f6fc0a501b9e34a8a16e9099411b082904b2
  React-RCTBlob: 07cccbb74e22ce66745358799f6ab02a5bed2993
  React-RCTFabric: 0ea93fed54109b80f1bca063172120f07edb6516
  React-RCTImage: 8fbdae841ea1217c44f4c413bba2403134b83cd1
  React-RCTLinking: c59bf8286ba2cc327b01bb524fb9c16446dc18bc
  React-RCTNetwork: 2c137a0aaaed2cf4bb53aff82a2bb8c34f2fbeac
  React-RCTSettings: 9fcd32c5b38af6421a3dd20cdd9ebf09df0a9a6d
  React-RCTText: 5308618477fec454282809065bd121c2bd3dd5e1
  React-RCTVibration: 7b2a186756b5c8e586e3e7948eed4432a93299c0
  React-rendererconsistency: 65d4692825fda4d9516924b68c29d0f28da3158c
  React-rendererdebug: 0b97f49d44c91862e1576961faf6bde836ed4eb3
  React-rncore: 6aca111c05a48c58189a005cb10a7b52780738dc
  React-RuntimeApple: aa20633298595444bf2dfbc5246889b4f475b871
  React-RuntimeCore: 8ac56cc6d82a1090f1d15d48b487c9a5a1d7d915
  React-runtimeexecutor: 732038d7c356ba74132f1d16253a410621d3c2c1
  React-RuntimeHermes: a695d944686adc97f85a1b34c31840a0a39e356c
  React-runtimescheduler: 00666e100e35a13f28fb2fdab22817cf62bbd6a3
  React-timing: c2915214b94a62bdf77d2965c31f76bc25b362a5
  React-utils: 9f9a6a31d703b136eb1614d914c10a3c96b1e6dd
  ReactCodegen: 9a99ced51aab02909c7ab5056f33dff7054305bb
  ReactCommon: 04292c6f596181ebf755e7929d96d2148542b0e8
  RNCAsyncStorage: 826b603ae9c0f88b5ac4e956801f755109fa4d5c
  RNCCheckbox: a3ca9978cb0846b981d28da4e9914bd437403d77
  RNGestureHandler: 511250b190a284388f9dd0d2e56c1df76f14cfb8
  RNPermissions: ac5ac3a8f5f63166a4ea5473c73d7e28447e8178
  RNReanimated: 849c4bfc1a185b123ca9a89f2a98d679b3d79b4e
  RNScreens: e52d2e7e5fa29266d6f8f68e8229452e5b2f4236
  RNSVG: 8b1a777d54096b8c2a0fd38fc9d5a454332bbb4d
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  scandit-datacapture-frameworks-barcode: 3c7e3d76ba9a91efc18a84643544787d1b614f7c
  scandit-datacapture-frameworks-core: dd9ed604763f2fff4ddf97abb04393090e4cf24e
  scandit-react-native-datacapture-barcode: 9229064ec9a1aa1feffd9507932daafb0c4c0df5
  scandit-react-native-datacapture-core: f430d02bec9899ba2e5962cdee6c46097820f97f
  ScanditBarcodeCapture: 2db63ff944ec208b2aaa44209f1f9fea670e4d13
  ScanditCaptureCore: 42a87065ff47bae25e5266eaa4030e8900db8c87
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 90d80701b27946c4b23461c00a7207f300a6ff71
  ZXingObjC: 8898711ab495761b2dbbdec76d90164a6d7e14c5

PODFILE CHECKSUM: 3ffa71c38cebf8f20119f0cb69953abaad9296e6

COCOAPODS: 1.16.2
