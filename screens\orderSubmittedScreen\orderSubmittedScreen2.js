import React, { useState, useContext, useEffect } from "react";
import {
  StyleSheet,
  View,
  StatusBar,
  TouchableOpacity,
  Modal,
  TextInput,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import { useSubmitFeedbackMutation } from "../../generated/graphql";
import Text from "../../components/Text";
import { AntDesign } from "@expo/vector-icons";
import { UserContext } from "../../context/userContext";
import { posthog } from "../../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldMainText: {
      color: theme.appColors.textDark,
      fontSize: 16,
      fontWeight: "500",
    },
    buttonContainer: {
      marginBottom: 0,
      marginTop: 10,
    },
    centerView: {
      alignItems: "center",
      backgroundColor: "white",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      marginBottom: 50,
      marginTop: 30,
    },
    closeButton: {
      fontWeight: "600",
    },
    closeWrapper: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      marginTop: 10,
      width: "100%",
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    input: {
      alignSelf: "stretch",
      borderRadius: 20,
      borderWidth: 1,
      height: 40,
      margin: 12,
      padding: 10,
    },
    largeBoldMainText: {
      fontSize: 33,
      fontWeight: "600",
      marginBottom: 15,
      marginTop: 30,
    },
    mainButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 10,
      color: "white",
      height: 45,
      justifyContent: "center",
    },
    mainButtonText: {
      color: "white",
      fontSize: 20,
      fontWeight: "500",
    },
    modalPosition: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      marginTop: 22,
    },
    modalText: {
      fontWeight: "bold",
      marginBottom: 15,
      textAlign: "center",
    },
    modalView: {
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: 20,
      elevation: 5,
      margin: 20,
      padding: 35,
      shadowColor: "black",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    previousText: {
      color: theme.appColors.textDark,
      fontSize: 16,
      fontWeight: "600",
    },
    previousWrapper: {
      display: "flex",
      flexDirection: "row",
    },
    safeContainer: {
      backgroundColor: "white",
      marginHorizontal: 40,
      marginTop: 20,
    },
    submitButton: {
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 20,
      elevation: 2,
      marginTop: 10,
      padding: 10,
    },
    textStyle: {
      color: "white",
      fontWeight: "bold",
      textAlign: "center",
    },
    thumbContainer: {
      alignItems: "center",
      borderRadius: 25,
      height: 50,
      justifyContent: "center",
      marginHorizontal: 25,
      width: 50,
    },
    thumbsRow: {
      flexDirection: "row",
    },
  });
  return styles;
};

export default function OrderSubmittedScreen2({ navigation, route }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { user } = useContext(UserContext);
  const [thumbsModalVisible, setThumbsModalVisible] = useState(false);
  const [issuesModalVisible, setIssuesModalVisible] = useState(false);
  const [thumbSelected, setThumbSelected] = useState(false);
  const [text, onChangeText] = React.useState("");

  const [submitFeedback] = useSubmitFeedbackMutation();

  const onBackToHomePressed = () => {
    navigation.goBack();
    navigation.popToTop();
  };

  const onBackToOrdersPressed = () => {
    navigation.navigate("Orders");
  };
  const total = route.params.total;

  const onSelectThumb = (thumbsUp) => {
    posthog.capture("order_submitted_feedback", { thumbsUp });
    setThumbSelected(true);
    setThumbsModalVisible(false);

    if (thumbsUp) {
      submit(true);
    } else {
      setIssuesModalVisible(true);
    }
  };

  const onSubmitIssues = () => {
    setIssuesModalVisible(false);
    submit(false);
  };

  const submit = (thumbsUp) => {
    try {
      submitFeedback({
        variables: {
          submitFeedbackInput: {
            userId: user.id,
            issues: text,
            thumbsUp,
          },
        },
      });
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    if (!thumbSelected) {
      setTimeout(() => {
        setThumbsModalVisible(true);
      }, 1000);
    }
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeContainer}>
        <View style={styles.closeWrapper}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => {
              posthog.capture("order_submitted_close_clicked");
              onBackToHomePressed();
            }}
          >
            <AntDesign name="close" size={33} color="black" />
          </TouchableOpacity>
        </View>
        <View style={styles.modalPosition}>
          <Modal
            animationType="slide"
            transparent={true}
            visible={thumbsModalVisible}
            onRequestClose={() => {
              setThumbsModalVisible(!thumbsModalVisible);
            }}
          >
            <View style={styles.modalPosition}>
              <View style={styles.modalView}>
                <Text style={styles.modalText}>
                  How was your ordering experience today?
                </Text>
                <View style={styles.thumbsRow}>
                  <TouchableOpacity
                    style={[
                      styles.thumbContainer,
                      { backgroundColor: "#F43756" },
                    ]}
                    onPress={() => onSelectThumb(false)}
                  >
                    <AntDesign name="dislike1" size={30} color="white" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.thumbContainer,
                      { backgroundColor: "#1A6923" },
                    ]}
                    onPress={() => onSelectThumb(true)}
                  >
                    <AntDesign name="like1" size={30} color="white" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
          <Modal
            animationType="slide"
            transparent={true}
            visible={issuesModalVisible}
            onRequestClose={() => {
              setIssuesModalVisible(!issuesModalVisible);
            }}
          >
            <View style={styles.modalPosition}>
              <View style={styles.modalView}>
                <Text style={styles.modalText}>
                  Share feedback or any issues you had
                </Text>
                <TextInput
                  style={styles.input}
                  onChangeText={onChangeText}
                  value={text}
                />
                <TouchableOpacity
                  style={styles.submitButton}
                  onPress={() => onSubmitIssues()}
                >
                  <Text style={styles.textStyle}>Submit</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
        <View style={styles.centerView}>
          <AntDesign
            name="checkcircle"
            size={140}
            color={theme.appColors.textLight}
          />
          {user.show_prices ? (
            <Text style={styles.largeBoldMainText}>${total}</Text>
          ) : <Text style={styles.largeBoldMainText}></Text>}
          <Text style={styles.boldMainText}>Your Order Has Been Submitted</Text>
          <Text style={styles.boldMainText}>
            Please check the delivery status on
          </Text>
          <View style={styles.previousWrapper}>
            <TouchableOpacity onPress={onBackToOrdersPressed}>
              <Text style={styles.previousText}>Previous Order </Text>
            </TouchableOpacity>
            <Text style={styles.boldMainText}>page</Text>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.mainButton}
            onPress={() => {
              posthog.capture("order_submitted_continue_shopping_clicked");
              onBackToHomePressed();
            }}
          >
            <Text style={styles.mainButtonText}>Continue Shopping</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
