/**
 * UOM Helper functions for handling Unit of Measure functionality
 */

/**
 * Normalize UOMs to always return an array
 * @param {Object} item - The item object
 * @returns {Array} Normalized UOMs array
 */
const normalizeUoms = (item) => {
  if (!item?.uoms) return [];
  if (Array.isArray(item.uoms)) return item.uoms;
  return [item.uoms]; // Convert single object to array
};

/**
 * Get UOM dropdown options for an item
 * @param {Object} item - The item object
 * @param {Object} user - User object with custom prices (optional)
 * @returns {Array} Array of UOM options for dropdown
 */
export const getUomOptions = (item, user = null) => {
  const uoms = normalizeUoms(item);

  if (uoms.length === 0) {
    return [{ value: "default", label: "Unit" }];
  }

  const formatOptionLabel = (uomId, uomName) => {
    const quantity = getUomQuantity(item, uomId);

    if (user && user.show_prices && user.approved) {
      const price = getUomPrice(item, user, uomId);
      return `${uomName} (${quantity}): $${parseFloat(price).toFixed(2)}`;
    } else {
      return `${uomName} (${quantity})`;
    }
  };

  return [
    { value: "default", label: formatOptionLabel("default", "Unit") },
    ...uoms
      .filter((uom) => uom && !uom.archived)
      .map((uom) => ({
        value: uom.id,
        label: formatOptionLabel(uom.id, uom.name),
      })),
  ];
};

/**
 * Get custom UOM price for a specific item and UOM
 * @param {Object} user - User object with custom_uom_prices
 * @param {string} itemId - Item ID
 * @param {string} itemUomId - Item UOM ID
 * @returns {number|null} Custom UOM price or null if not found
 */
export const getCustomUomPrice = (user, itemId, itemUomId) => {
  if (
    !user?.custom_uom_prices ||
    !Array.isArray(user.custom_uom_prices) ||
    !itemUomId
  )
    return null;

  const itemUomPrices = user.custom_uom_prices.find(
    (cp) => cp?.item_id === itemId
  );

  if (!itemUomPrices || !Array.isArray(itemUomPrices.uom_prices)) return null;

  const customPrice = itemUomPrices.uom_prices.find(
    (up) => up.item_uom_id === itemUomId
  );

  return customPrice?.price || null;
};

/**
 * Get UOM price (custom or default)
 * @param {Object} item - Item object
 * @param {Object} user - User object with custom prices
 * @param {string|null} itemUomId - Selected UOM ID
 * @returns {number} The price for the UOM
 */
export const getUomPrice = (item, user, itemUomId) => {
  if (!itemUomId || itemUomId === "default") {
    // Return default item price (check custom price first)
    if (!item) return 0;

    const customPrice = user?.custom_prices?.find(
      (cp) => cp?.item_id === item.id
    );

    return customPrice?.price || item.discounted_price || item.price || 0;
  }

  // Check for custom UOM price first
  const customUomPrice = getCustomUomPrice(user, item.id, itemUomId);
  if (customUomPrice !== null) {
    return customUomPrice;
  }

  // Return default UOM price
  const uoms = normalizeUoms(item);
  if (uoms.length === 0) {
    return item?.price || 0;
  }

  const uom = uoms.find((u) => u?.id === itemUomId);
  return uom?.price || 0;
};

/**
 * Get UOM quantity multiplier
 * @param {Object} item - Item object
 * @param {string|null} itemUomId - Selected UOM ID
 * @returns {number} The quantity multiplier for the UOM
 */
export const getUomQuantity = (item, itemUomId) => {
  if (!itemUomId || itemUomId === "default") {
    return 1;
  }

  const uoms = normalizeUoms(item);
  if (uoms.length === 0) {
    return 1;
  }

  const uom = uoms.find((u) => u?.id === itemUomId);
  return uom?.quantity || 1;
};

/**
 * Get UOM-specific UPC code
 * @param {Object} item - Item object
 * @param {string|null} itemUomId - Selected UOM ID
 * @returns {string} UPC code for the UOM
 */
export const getUomUpc = (item, itemUomId) => {
  if (!itemUomId || itemUomId === "default") {
    return item?.upc1 || "-";
  }

  const uoms = normalizeUoms(item);
  if (uoms.length === 0) {
    return item?.upc1 || "-";
  }

  const uom = uoms.find((u) => u?.id === itemUomId);
  return uom?.upc || item?.upc1 || "-";
};

/**
 * Get UOM name for display
 * @param {Object} item - Item object
 * @param {string|null} itemUomId - Selected UOM ID
 * @returns {string} UOM name for display
 */
export const getUomName = (item, itemUomId) => {
  if (!itemUomId || itemUomId === "default") {
    return "Unit";
  }

  const uoms = normalizeUoms(item);
  if (uoms.length === 0) {
    return "Unit";
  }

  const uom = uoms.find((u) => u?.id === itemUomId);
  return uom?.name || "Unit";
};

/**
 * Check if item has UOMs available
 * @param {Object} item - Item object
 * @returns {boolean} Whether item has UOMs
 */
export const hasUoms = (item) => {
  const uoms = normalizeUoms(item);
  return uoms.length > 0;
};
