import { useTheme } from "@react-navigation/native";
import { useContext, useMemo, useState, useEffect } from "react";
import { ScrollView, StyleSheet, TouchableOpacity, View } from "react-native";
import CartItem from "../../components/CartItem";
import FooterButton from "../../components/FooterButton";
import Header from "../../components/Header";
import SafeAreaView from "../../components/SafeAreaView";
import StoreSummary from "../../components/StoreSummary";
import { UserContext } from "../../context/userContext";
import {
  useUpdateInvoiceMutation,
  useUpdateOrderMutation,
  useGetItemsBySupplierQuery,
} from "../../generated/graphql";
import { getPrice } from "../../lib/getPrice";
import { getTotal } from "../../lib/getTotal";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Text from "../../components/Text";
import useGlobalStyles from "../../lib/useGlobalStyles";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    addItemButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropLight,
      borderRadius: 10,
      flexDirection: "row",
      gap: 5,
      justifyContent: "center",
      padding: 15,
      width: 150,
    },
    container: {
      backgroundColor: theme.appColors.primary,
      flex: 1,
    },
    itemsContainer: {
      alignItems: "center",
      gap: 10,
      marginBottom: 30,
      marginTop: 20,
    },
    scrollView: {
      backgroundColor: "white",
      paddingHorizontal: 20,
    },
  });
  return styles;
};

const updateCRV = (items, orderItems, isInvoice) => {
  const priceKey = isInvoice ? "price" : "price_purchased_at";
  const crv5ItemId = isInvoice ? "391457" : "391457";
  const crv10ItemId = isInvoice ? "391456" : "391456";

  // Create a new array instead of modifying the input array
  let updatedItems = [...items];

  // Invoice items don't have crv, so we need to add it
  if (orderItems && Array.isArray(orderItems)) {
    updatedItems = updatedItems.map((item) => {
      if (!("crv" in item)) {
        const orderItem = orderItems.find(
          (orderItem) => orderItem.item_id === item.item_id
        );
        return { ...item, crv: orderItem?.crv };
      }
      return item;
    });
  }

  const { crv5, crv10 } = updatedItems.reduce(
    (acc, item) => {
      if (item.crv === "CA CRV $0.05") {
        return { ...acc, crv5: acc.crv5 + item.quantity };
      }
      if (item.crv === "CA CRV $0.10") {
        return { ...acc, crv10: acc.crv10 + item.quantity };
      }
      return acc;
    },
    { crv5: 0, crv10: 0 }
  );

  // Filter out existing CRV items first
  updatedItems = updatedItems.filter(
    (item) => item.item_id !== crv5ItemId && item.item_id !== crv10ItemId
  );

  // Add CRV5 item if needed
  if (crv5 > 0) {
    updatedItems = updatedItems.filter((item) => item.item_id !== crv5ItemId);
    updatedItems = [
      {
        item_id: crv5ItemId,
        quantity: crv5,
        name: "CA CRV $0.05",
        [priceKey]: 0.05,
      },
      ...updatedItems,
    ];
  }

  // Add CRV10 item if needed
  if (crv10 > 0) {
    updatedItems = updatedItems.filter((item) => item.item_id !== crv10ItemId);
    updatedItems = [
      {
        item_id: crv10ItemId,
        quantity: crv10,
        name: "CA CRV $0.10",
        [priceKey]: 0.1,
      },
      ...updatedItems,
    ];
  }

  return updatedItems;
};

export default function EditOrderScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const { order, invoiceItems } = route.params;
  const [updateOrder] = useUpdateOrderMutation();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const [cancelPressed, setCancelPressed] = useState(false);
  const [isCredit, setIsCredit] = useState(order.subtotal < 0);
  const [orderItemsTemp, setOrderItemsTemp] = useState(order.orderItems);
  const [invoiceItemsTemp, setInvoiceItemsTemp] = useState(invoiceItems);
  const globalStyles = useGlobalStyles();

  const orderHasInvoice = invoiceItems && invoiceItems?.length !== 0;
  const supplierObj = user.suppliers.find(
    (element) => element.name === order.supplier
  );

  useEffect(() => {
    setOrderItemsTemp(updateCRV(order.orderItems, undefined, false));
  }, [order]);

  useEffect(() => {
    setInvoiceItemsTemp(updateCRV(invoiceItems, order.orderItems, true));
  }, [invoiceItems]);

  const onEditQuantity = async (cartId, itemId, quantity) => {
    setOrderItemsTemp((prevOrderItems) => {
      const tempitems = prevOrderItems.reduce((acc, item) => {
        if (item.item_id === itemId) {
          if (quantity > 0) {
            return [...acc, { ...item, quantity }];
          }
        } else {
          return [...acc, item];
        }
        return acc;
      }, []);

      return updateCRV(tempitems, undefined, false);
    });
    if (orderHasInvoice) {
      setInvoiceItemsTemp((prevInvoiceItems) => {
        const tempitems = prevInvoiceItems.reduce((acc, item) => {
          if (item.item_id === itemId) {
            if (quantity > 0) {
              return [...acc, { ...item, quantity }];
            }
          } else {
            return [...acc, item];
          }
          return acc;
        }, []);
        return updateCRV(tempitems, orderItemsTemp, true);
      });
    }
  };

  const onCancelPressed = async () => {
    if (cancelPressed) {
      await updateOrder({
        variables: {
          updateOrderInput: {
            orderId: order.id,
            userId: user.id,
            status: "Canceled",
            supplierId: user.suppliers[0].id,
          },
        },
      });
      // TODO: Remove this, we auto update the invoice when the order is updated
      // await updateInvoice({
      //   variables: {
      //     updateInvoiceInput: {
      //       id: order.invoice.id,
      //       archived: true,
      //     },
      //   },
      // });
      navigation.goBack();
    }
    setCancelPressed(!cancelPressed);
    setTimeout(() => setCancelPressed(false), 2000);
  };

  const saveChanges = async () => {
    if (orderItemsTemp.length === 0) {
      navigation.goBack();
      return;
    }
    try {
      await updateOrder({
        variables: {
          updateOrderInput: {
            orderId: order.id,
            userId: user.id,
            supplierId: user.suppliers[0].id,
            orderItems: orderItemsTemp.map((item) => ({
              id: item.item_id,
              price_purchased_at: item.price_purchased_at,
              quantity: item.quantity,
            })),
          },
        },
      });
      // TODO: Remove this, we auto update the invoice when the order is updated
      // if (orderHasInvoice) {
      //   const itemMap = itemsData?.itemsBySupplier.reduce((acc, item) => {
      //     acc[item.id] = item;
      //     return acc;
      //   }, {});

      //   await updateInvoice({
      //     variables: {
      //       updateInvoiceInput: {
      //         id: order.invoice.id,
      //         invoice_items: invoiceItemsTemp.map((item) => {
      //           const { image, crv, ...invoiceItem } = item;
      //           return {
      //             ...invoiceItem,
      //             name: createInvoiceItemName(invoiceItem, itemMap),
      //             quantity: invoiceItem.quantity,
      //           };
      //         }),
      //       },
      //     },
      //   });
      // }
      navigation.goBack();
    } catch (error) {
      console.error("Failed to update order:", error);
    }
  };

  const addItemPressed = () => {
    navigation.navigate("SearchResults", {
      supplier: order.supplier,
      order: { ...order, orderItems: orderItemsTemp },
      invoiceItems: invoiceItemsTemp,
      showButton: false,
    });
  };

  const getItemImage = (item) => {
    return (
      item.image ||
      order.orderItems.find(
        (order_item) => order_item.item_id.toString() === item.item_id
      )?.image ||
      supplierObj?.config?.defaultItemImage ||
      "https://fakeimg.pl/100x100?text=no+image"
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        goBack={navigation.goBack}
        title={`Edit ${!isCredit ? "Order" : "Credit"}`}
      />
      <StoreSummary
        imageUri={supplierObj?.config?.squareLogo ?? supplierObj.logo ?? null}
        store={order.supplier}
        onPress={onCancelPressed}
        buttonPressed={cancelPressed}
        buttonText={"Cancel Order"}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.itemsContainer}>
          {(orderHasInvoice ? invoiceItemsTemp : orderItemsTemp).map((item) => {
            return (
              <CartItem
                key={item.item_id}
                imageUri={getItemImage(item)}
                itemName={item.name}
                unitSize={item.unit_size}
                quantity={item.quantity}
                amount={(
                  Math.round(getPrice(item) * item.quantity * 100).toFixed(2) /
                  100
                ).toFixed(2)}
                onEditQuantity={onEditQuantity}
                cartId={order.id}
                itemId={item.item_id}
                item={item}
              />
            );
          })}
          <TouchableOpacity
            style={styles.addItemButton}
            onPress={addItemPressed}
          >
            <MaterialCommunityIcons
              name="plus-circle"
              size={24}
              color={theme.appColors.text}
            />
            <Text style={globalStyles.text}>Add Item</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      <FooterButton
        mainText={
          user.show_prices
            ? `Subtotal: ${isCredit ? "-" : ""}$` +
              getTotal(orderHasInvoice ? invoiceItemsTemp : orderItemsTemp)
            : null
        }
        buttonText="Save Changes"
        onPress={saveChanges}
        isCredit={isCredit}
        onChangeIsCredit={setIsCredit}
      />
    </SafeAreaView>
  );
}
