import { useContext } from "react";
import { View, Image, StyleSheet, Text } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import { getPrice } from "../lib/getPrice";
import { UserContext } from "../context/userContext";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    itemImage: {
      height: 55,
      width: 55,
    },
    itemTextContainer: {
      marginLeft: 8,
      width: 180,
    },

    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      marginLeft: 20,
      width: 30,
    },
  });
  return styles;
};

const OrderItemWithoutPrice = ({ item, quantity }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const styles = createStyles(theme);
  const { user } = useContext(UserContext);

  const supplier = user.suppliers.find(
    (supplier) => supplier.name === item.supplier
  );

  return (
    <View style={{ flexDirection: "row" }}>
      <Image
        style={styles.itemImage}
        source={{
          uri:
            item.image ||
            supplier?.config?.defaultItemImage ||
            "https://fakeimg.pl/100x100?text=no+image",
        }}
      />

      <View style={styles.itemTextContainer}>
        <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
          {item.name}
        </Text>
        <Text style={[globalStyles.bodySmall, globalStyles.textLight]}>
          ${getPrice(item) ?? item.price_purchased_at} /{" "}
          {item.unit_size > 1 ? `${item.unit_size}ct` : "each"}
        </Text>
      </View>

      {quantity && (
        <View
          style={{
            flexDirection: "column",
            justifyContent: "center",
            alignItem: "center",
          }}
        >
          <View style={styles.quantity}>
            <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
              {item.quantity}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

OrderItemWithoutPrice.propTypes = {
  item: PropTypes.object.isRequired,
  quantity: PropTypes.number.isRequired,
};

export default OrderItemWithoutPrice;
