{"name": "attain", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "codegen": "graphql-codegen --config codegen.yml", "lint": "eslint .", "auto-lint": "eslint --fix .", "format": "prettier --check .", "auto-format": "prettier --write ."}, "dependencies": {"@apollo/client": "^3.5.10", "@apollo/react-hooks": "^4.0.0", "@expo-google-fonts/lato": "^0.2.2", "@expo-google-fonts/sen": "^0.2.2", "@expo/config-plugins": "~9.0.0", "@expo/prebuild-config": "~8.0.0", "@mui/icons-material": "^5.5.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/checkbox": "^0.5.12", "@react-native/gradle-plugin": "^0.74.83", "@react-navigation/bottom-tabs": "^6.2.0", "@react-navigation/native": "^6.0.8", "@react-navigation/native-stack": "^6.5.0", "@react-navigation/stack": "^6.1.1", "algoliasearch": "^4.13.0", "apollo-cache-inmemory": "^1.6.6", "apollo-link-http": "^1.5.17", "axios": "^0.26.1", "date-fns": "^2.28.0", "dayjs": "^1.11.13", "expo": "^52.0.0", "expo-application": "~6.0.2", "expo-build-properties": "~0.13.2", "expo-camera": "~16.0.17", "expo-checkbox": "~4.0.1", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.12", "expo-device": "~7.0.2", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-modules-core": "~2.2.2", "expo-notifications": "~0.29.13", "expo-random": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-updates": "~0.27.1", "expo-web-browser": "~14.0.2", "firebase": "^10.12.1", "graphql": "^16.3.0", "i18next": "^24.2.2", "posthog-react-native": "^3.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.49.1", "react-i18next": "^15.4.1", "react-instantsearch-dom": "^6.23.0", "react-instantsearch-native": "^6.23.0", "react-native": "0.76.7", "react-native-ble-plx": "^3.2.1", "react-native-date-picker": "^4.2.14", "react-native-dropdown-picker": "^5.4.6", "react-native-elements": "^3.4.2", "react-native-gesture-handler": "~2.20.2", "react-native-modal": "^13.0.1", "react-native-numeric-input": "BlueRacoon/react-native-numeric-input", "react-native-permissions": "^4.1.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "15.8.0", "react-native-swiper": "^1.6.0", "react-native-vector-icons": "^9.1.0", "react-native-web": "~0.19.10", "react-native-webview": "13.12.5", "react-native-zebra-bluetooth-printer": "^1.0.12", "scandit-react-native-datacapture-barcode": "^6.17.0-beta.1", "scandit-react-native-datacapture-core": "^6.17.0-beta.1"}, "devDependencies": {"@babel/core": "^7.19.3", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/introspection": "2.1.1", "@graphql-codegen/typescript": "2.4.7", "@graphql-codegen/typescript-operations": "2.3.4", "@graphql-codegen/typescript-react-apollo": "3.2.10", "@types/react": "~18.3.12", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-native": "^4.0.0", "prettier": "2.7.1", "typescript": "~5.3.3"}, "private": true}