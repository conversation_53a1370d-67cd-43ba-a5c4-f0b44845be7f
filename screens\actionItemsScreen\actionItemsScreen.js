import { useState, useContext, useRef } from "react";
import { StyleSheet, View, TouchableOpacity, FlatList } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import Text from "../../components/Text";
import {
  useGetActionItemsQuery,
  useReconcileInvoiceWithItemMutation,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";

import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import Header from "../../components/Header";
import Button from "../../components/Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    clicked: {
      borderColor: "#DEDEDE",
      borderWidth: 1,
      padding: 4,
    },
    deliveryGraphic: {
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 37,
    },
    deliveryGraphicLine: {
      borderColor: "red",
      borderRadius: 1,
      borderStyle: "dashed",
      borderWidth: 2,
      flex: 1,
    },
    deliveryGraphicLineClip: {
      flex: 1,
      height: 2,
      marginHorizontal: -2,
      overflow: "hidden",
    },
    deliveryGraphicPoint: {
      borderRadius: 5,
      height: 10,
      width: 10,
      zIndex: 2,
    },
    headerContainer: {
      alignItems: "center",
      paddingBottom: 20,
      paddingHorizontal: 20,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 55,
      width: 55,
    },
    itemInfoContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    itemPrice: {
      marginLeft: 8,
    },
    itemTextContainer: {
      marginLeft: 8,
      width: 180,
    },
    list: {
      backgroundColor: "white",
      height: "100%",
    },
    listHeader: {
      margin: 20,
    },
    oosText: {
      color: "gray",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
    },
    orderItemContainer: {
      marginBottom: 15,
      paddingHorizontal: 20,
    },
    orderNameContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: 6,
    },
    orderSummaryView: {
      alignSelf: "stretch",
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    pencilIconStyle: {
      paddingRight: 5,
    },
    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.textNeutral,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      width: 30,
    },
    tag: {
      marginBottom: 10,
      marginHorizontal: -3,
      marginTop: 20,
    },
    truck: {
      marginLeft: 28,
      marginRight: 25,
      marginVertical: 5,
    },
  });
  return styles;
};

export default function ActionItemsScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();

  const { invoiceItemId, invoiceId } = route.params;
  const [reconcileInvoiceWithItem] = useReconcileInvoiceWithItemMutation();
  const {
    loading: getActionItemsLoading,
    data: getActionItemsData,
    error: getActionItemsError,
  } = useGetActionItemsQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getActionItemsInput: {
        invoice_id: invoiceId,
        invoice_item_id: invoiceItemId,
      },
    },
  });

  if (getActionItemsLoading && !getActionItemsData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getActionItemsError)
    return (
      <LoadingErrorStatus
        message={getActionItemsError.message}
        errorStatus={true}
      />
    );

  const actionItems = getActionItemsData.actionItems;
  const checkInItem = async () => {
    const result = await reconcileInvoiceWithItem({
      variables: {
        reconcileInvoiceWithItemInput: {
          invoice_id: invoiceId,
          invoice_item_id: actionItems[0].invoiceItem.id,
          quantity: actionItems[0].invoiceItem.checked_in_quantity,
          is_mispick: actionItems[0].invoiceItem.is_mispick,
          checked_in: true,
        },
      },
    });
    navigation.pop(2);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: 30, marginVertical: 10 }}>
        <View
          style={{
            paddingHorizontal: 0,
            paddingVertical: 0,
            justifyContent: "space-between",
            flexDirection: "row",
            borderRadius: 10,
            marginBottom: 10,
          }}
          onPress={() =>
            navigation.navigate("ActionItemsScreen", { invoiceItemId: 1 })
          }
        >
          <View
            style={{
              justifyContent: "center",
            }}
          >
            <Text style={[globalStyles.text, globalStyles.headlineSmall]}>
              {item.invoiceItem.name}
            </Text>
          </View>
          <View
            style={{
              borderWidth: 1,
              borderColor: theme.appColors.backdropLight,
              borderRadius: 5,
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              paddingHorizontal: 10,
              paddingVertical: 6,
            }}
          >
            <Text style={[globalStyles.text, globalStyles.headlineSmall]}>
              {item.invoiceItem.checked_in_quantity}/{item.invoiceItem.quantity}
            </Text>
          </View>
        </View>
        <Text style={[globalStyles.text, globalStyles.bodyLarge]}>
          {item.description}
        </Text>
      </View>
    );
  };
  return (
    <SafeAreaView style={globalStyles.container}>
      <Header goBack={navigation.goBack} title="Action Items" />
      <View>
        <FlatList
          ListHeaderComponent={
            <>
              {actionItems.length === 0 ? (
                <View
                  style={{
                    paddingHorizontal: 30,
                    paddingTop: 30,
                    paddingBottom: 0,
                  }}
                >
                  <View
                    style={{
                      justifyContent: "flex-start",
                      alignItems: "center",
                      flexDirection: "row",
                      marginBottom: 10,
                    }}
                  >
                    <Ionicons
                      name="checkmark"
                      size={24}
                      color={theme.appColors.greenText}
                    />
                    <Text
                      style={[
                        globalStyles.headlineMedium,
                        { color: theme.appColors.greenText, marginLeft: 5 },
                      ]}
                    >
                      Matching Complete
                    </Text>
                  </View>

                  <View style={{ justifyContent: "center" }}>
                    <Text style={[globalStyles.text, globalStyles.bodyLarge]}>
                      Order has been checked in successfully, there are no
                      further actions needed.
                    </Text>
                  </View>
                </View>
              ) : (
                <View
                  style={{
                    justifyContent: "flex-start",
                    alignItems: "center",
                    flexDirection: "row",
                    paddingHorizontal: 30,
                    paddingTop: 30,
                    paddingBottom: 0,
                  }}
                >
                  <Ionicons
                    name="warning"
                    size={24}
                    color={theme.appColors.orangeText}
                  />
                  <Text
                    style={[
                      globalStyles.headlineMedium,
                      { color: theme.appColors.orangeText, marginLeft: 5 },
                    ]}
                  >
                    Action Items
                  </Text>
                </View>
              )}
            </>
          }
          data={actionItems}
          numColumns={1}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          style={styles.list}
        />
      </View>
      <View
        style={{
          position: "absolute",
          bottom: 50,
          marginHorizontal: 30,
        }}
      >
        {actionItems.length > 0 && (
          <View
            style={{
              justifyContent: "center",
              flexDirection: "row",
              marginBottom: 10,
            }}
          >
            <Button variant="light" size="lg" onPress={checkInItem}>
              <Text>Continue Check In</Text>
            </Button>
          </View>
        )}
        <View
          style={{
            justifyContent: "center",
            flexDirection: "row",
          }}
        >
          <Button variant="dark" size="lg" onPress={() => navigation.pop(2)}>
            <Text>{actionItems.length > 0 ? "Set Aside" : "Done"}</Text>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}
