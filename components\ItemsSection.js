import {
  View,
  Image,
  ScrollView,
  Text,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import React, { useContext, useMemo, useState } from "react";
import { useTheme } from "@react-navigation/native";
import Modal from "react-native-modal";
import ItemListCard from "./ItemCard/ItemListCard";
import { ViewAllButton } from "./ViewAllButton";
import { Tag } from "./TagList";
import { posthog } from "../src/posthog";
import { UserContext } from "../context/userContext";
import { useTranslation } from "react-i18next";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      paddingTop: 20,
    },
    dark: {
      backgroundColor: theme.appColors.backdropLight,
    },
    header: {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      marginRight: 20,
    },
    light: {
      backgroundColor: theme.appColors.backdropNeutral,
    },
  });
  return styles;
};

const ItemsSection = (props) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const [modalVisible, setModalVisible] = useState(false);
  const { user } = useContext(UserContext);
  const onSignupPress = () => {
    posthog.capture("sign_up_clicked", { supplier: props.title });
    setModalVisible(true);
    setTimeout(() => setModalVisible(false), 2000);
  };

  let title = props.titleContent || props.title;
  switch (title) {
    case "New Items":
      title = t("title.new_items");
      break;
    case "Top Sellers":
      title = t("title.top_sellers");
      break;
    case "Order Again":
      title = t("title.order_again");
      break;
    case "Recommended For You":
      title = t("title.recommended_for_you");
      break;
  }

  return (
    <View
      style={[
        styles.container,
        { paddingLeft: props.padding ? props.padding : 0 },
        props.variant == "light" ? styles.light : styles.dark,
        props.addBottomPadding ? { paddingBottom: "20%" } : {},
      ]}
      key={props.index}
    >
      <Modal
        animationIn="slideInUp"
        isVisible={modalVisible}
        hasBackdrop
        onBackdropPress={() => setModalVisible(false)}
        onRequestClose={() => setModalVisible(!modalVisible)}
        style={{ justifyContent: "flex-end", margin: 0 }}
      >
        <View
          style={{
            alignItems: "center",
            flex: 1,
            justifyContent: "center",
            marginTop: 22,
          }}
        >
          <View
            style={{
              alignItems: "center",
              backgroundColor: "white",
              borderRadius: 20,
              elevation: 5,
              margin: 20,
              padding: 35,
              shadowColor: "black",
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 4,
            }}
          >
            <Text style={globalStyles.labelLarge}>
              Thanks for your interest! We're processing your request.
            </Text>
          </View>
        </View>
      </Modal>

      {props.needSignup && (
        <Text
          maxFontSizeMultiplier={1.4}
          style={[
            globalStyles.headlineMedium,
            globalStyles.text,
            { marginBottom: 20 },
          ]}
        >
          Other Suppliers
        </Text>
      )}

      <View style={styles.header}>
        <Text
          maxFontSizeMultiplier={1.4}
          style={[
            globalStyles.headlineMedium,
            globalStyles.text,
            { paddingLeft: 30 },
          ]}
        >
          {title}
        </Text>
        {props.to && props.params && (
          <ViewAllButton
            to={props.to}
            params={props.params}
            variant={props.variant === "light" ? "dark" : "light"}
          />
        )}
        {props.needSignup && (
          <Tag
            bold
            color={theme.appColors.accent}
            highlighted
            label={"Sign up"}
            onPress={onSignupPress}
          />
        )}
      </View>
      <View style={{ paddingTop: 20 }}>
        <ScrollView
          contentContainerStyle={{ paddingLeft: 30 }}
          horizontal={true}
        >
          {props.items.map((item) => {
            return (
              <View style={{ marginRight: 15 }} key={item.id}>
                <ItemListCard
                  item={item}
                  section={props.title}
                  showSupplier={props.showSuppliers}
                  disabled={
                    props.needSignup || (user.driver && !user.is_user_account)
                  }
                  userApproved={props.userApproved}
                  customPrices={props.customPrices || []}
                />
              </View>
            );
          })}
        </ScrollView>
      </View>
    </View>
  );
};

ItemsSection.propTypes = {
  children: PropTypes.node,
  showSuppliers: PropTypes.bool,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  title: PropTypes.string,
  titleContent: PropTypes.node,
  userApproved: PropTypes.bool,
  variant: PropTypes.oneOf(["light", "dark"]),
  addBottomPadding: PropTypes.bool,
  customPrices: PropTypes.array,
};

export default ItemsSection;
