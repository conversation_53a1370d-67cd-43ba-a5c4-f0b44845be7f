import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, Image } from "react-native";
import DatePicker from "react-native-date-picker";
import Modal from "react-native-modal";
import useGlobalStyles from "../../lib/useGlobalStyles";
import DayOfWeekSelector from "../../components/DayOfWeekSelector";
import { format, parse } from "date-fns";
import Button from "../../components/Button";
import { useUpdateUserSuppliersMutation } from "../../generated/graphql";

const styles = StyleSheet.create({
  centeredView: {
    alignItems: "center",
    backgroundColor: "white",
    justifyContent: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  logo: { height: 30, marginRight: 10, width: 30 },
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContent: {
    backgroundColor: "white",
    padding: 20,
  },
  modalView: {
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 20,
    elevation: 5,
    shadowColor: "black",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  supplierHeader: {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    marginBottom: 10,
  },
  timeSection: { marginVertical: 20 },
});

const formattedDate = (date) => {
  return format(date, "HH:mm");
};

const parseDate = (dateString) => {
  const date = dateString
    ? parse(dateString, "HH:mm:ss", new Date())
    : new Date();
  date.setMinutes(Math.ceil(date.getMinutes() / 30) * 30);
  return date;
};
const EditCutoffTimeModal = ({ open, onClose, supplier }) => {
  useEffect(() => {
    if (supplier) {
      setCutoffTime(parseDate(supplier?.cutoffTime));
      setDeliveryTime(parseDate(supplier?.deliveryTIme));
      setCutoffDay(supplier?.cutoffDay || "Monday");
      setDeliveryDay(supplier?.deliveryDay || "Monday");
    }
  }, [supplier]);

  const [cutoffTime, setCutoffTime] = useState(new Date());
  const [deliveryTime, setDeliveryTime] = useState(new Date());
  const [cutoffDay, setCutoffDay] = useState(null);
  const [deliveryDay, setDeliveryDay] = useState(null);
  const globalStyles = useGlobalStyles();
  const [updateUserSuppliers] = useUpdateUserSuppliersMutation();

  const updateCutoffTimes = async () => {
    const supplierInput = {
      cutoffDay,
      cutoffTime: formattedDate(cutoffTime),
      deliveryDay,
      deliveryTime: formattedDate(deliveryTime),
      daysToDelivery: supplier.daysToDelivery,
      id: supplier.id,
    };
    await updateUserSuppliers({
      variables: {
        updateUserSuppliersInput: {
          suppliers: [supplierInput],
        },
      },
    });
    onClose();
  };
  return (
    <Modal
      animationIn="slideInUp"
      isVisible={open}
      hasBackdrop
      onBackdropPress={onClose}
      onRequestClose={onClose}
      style={styles.modal}
    >
      <View style={styles.modalContent}>
        <View style={styles.header}>
          <View style={styles.supplierHeader}>
            <Image
              style={styles.logo}
              source={{ uri: supplier?.supplierInfo.logo }}
            />
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {supplier?.supplier}
            </Text>
          </View>
          <Button size="sm" onPress={updateCutoffTimes}>
            <Text>Save</Text>
          </Button>
        </View>
        <View style={styles.timeSection}>
          <Text
            style={[
              globalStyles.headlineMedium,
              globalStyles.text,
              { marginBottom: 10 },
            ]}
          >
            Cutoff Time
          </Text>
          <DayOfWeekSelector selectedDay={cutoffDay} onSelect={setCutoffDay} />
          <View style={styles.centeredView}>
            <DatePicker
              style={{ height: 120 }}
              date={cutoffTime}
              mode={"time"}
              minuteInterval={30}
              onDateChange={setCutoffTime}
            />
          </View>
        </View>
        <View style={styles.timeSection}>
          <Text
            style={[
              globalStyles.headlineMedium,
              globalStyles.text,
              { marginBottom: 10 },
            ]}
          >
            Delivery Time
          </Text>
          <DayOfWeekSelector
            selectedDay={deliveryDay}
            onSelect={setDeliveryDay}
          />
          <View style={styles.centeredView}>
            <DatePicker
              style={{ height: 120 }}
              date={deliveryTime}
              mode={"time"}
              minuteInterval={30}
              onDateChange={setDeliveryTime}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default EditCutoffTimeModal;
