query GetSections($getSectionsInput: GetSectionsInput) {
  sections(getSectionsInput: $getSectionsInput) {
    value
    name
    items {
      id
      name
      price
      discounted_price
      unit_size
      qoh
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      last_ordered_date
      oos
      created_at
      metadata
      avg_cases_per_week
      moq
      uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    }
  }
}