import { useContext, useState } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  StatusBar,
  Image,
  TouchableOpacity,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import { useGetCutoffTimesQuery } from "../../generated/graphql";
import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import convertTime from "../../lib/convertTime";
import useGlobalStyles from "../../lib/useGlobalStyles";
import EditCutoffTimeModal from "./editCutoffTimeModal";
import { posthog } from "../../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    list: {
      backgroundColor: "white",
      paddingBottom: 20,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    logo: { height: 30, marginRight: 10, width: 30 },
    supplierContainer: {
      backgroundColor: theme.appColors.backdropNeutral,
      borderColor: theme.appColors.backdropDark,
      borderRadius: 20,
      borderWidth: 0.5,
      marginBottom: 15,
      padding: 20,
      shadowColor: "black",
      shadowOffset: {
        width: 1,
        height: 2,
      },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    supplierHeader: {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      marginBottom: 10,
    },
  });
  return styles;
};

export default function CutoffTimesScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);

  const { user } = useContext(UserContext);
  const { loading, data, error } = useGetCutoffTimesQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getCutoffTimesInput: {
        businessId: user.id,
      },
    },
    pollInterval: 500,
  });
  const globalStyles = useGlobalStyles();

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  const [open, setOpen] = useState(false);
  const [supplier, setSupplier] = useState(null);
  const cutoffTimeText = ({ cutoffDay, cutoffTime }) => {
    if (!cutoffTime) {
      return (
        <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
          Cutoff day: {cutoffDay || "Any"}
        </Text>
      );
    }

    return (
      <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
        Cutoff time: {cutoffDay}, {convertTime(cutoffTime)}
      </Text>
    );
  };

  const deliveryTimeText = ({ daysToDelivery, deliveryDay, deliveryTime }) => {
    if (deliveryDay && deliveryTime)
      return (
        <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
          Delivery time: {deliveryDay}, {convertTime(deliveryTime)}
        </Text>
      );

    if (typeof daysToDelivery === "number") {
      return (
        <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
          Days to delivery: {daysToDelivery}
        </Text>
      );
    }
  };

  const openModal = (supplier) => {
    setSupplier(supplier);
    setOpen(true);
  };

  const closeModal = () => {
    setOpen(false);
    setSupplier(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <EditCutoffTimeModal
        open={open}
        onClose={closeModal}
        supplier={supplier}
      />

      <FlatList
        contentContainerStyle={styles.list}
        data={data.cutoffTimes}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.supplierContainer}
            onPress={() => {
              posthog.capture("edit_supplier_cutoff_times_clicked");
              openModal(item);
            }}
          >
            <View style={styles.supplierHeader}>
              <Image
                style={styles.logo}
                source={{ uri: item.supplierInfo.logo }}
              />
              <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                {item.supplier}
              </Text>
            </View>
            <View style={{ marginLeft: 40 }}>
              {cutoffTimeText(item)}
              {deliveryTimeText(item)}
            </View>
          </TouchableOpacity>
        )}
        keyExtractor={(_, i) => i.toString()}
        ListHeaderComponent={
          <View style={{ marginBottom: 10, alignItems: "center" }}>
            <Text style={[globalStyles.text, globalStyles.bodyLarge]}>
              Click on a supplier to edit cutoff and delivery times
            </Text>
          </View>
        }
        ListEmptyComponent={
          <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
            No supplier cutoff times available
          </Text>
        }
      />
    </SafeAreaView>
  );
}
