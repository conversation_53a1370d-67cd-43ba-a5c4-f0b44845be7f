import { useContext, useState, useRef, useEffect, memo } from "react";
import { StyleSheet, View, FlatList } from "react-native";
import {
  useGetItemsV2Query,
  useGetItemsV2LazyQuery,
  useGetCategoriesBySupplierQuery,
  Ordering,
} from "../../generated/graphql";
import ItemGridCard from "../../components/ItemCard/ItemGridCard";
import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
  },
});

const MemoizedItemGridCard = memo(ItemGridCard);

export default function SelectItemsScreen({ route }) {
  const { user } = useContext(UserContext);
  const [allItems, setAllItems] = useState([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const currentCategoryIndex = useRef(0);

  const { category, brand, section } = route.params;

  const { data: categoriesData } = useGetCategoriesBySupplierQuery({
    skip: !user.suppliers[0].id === "31",
    fetchPolicy: "cache-and-network",
    variables: {
      getCategoriesBySupplierInput: {
        supplierId: user.suppliers[0].id,
        fillItemsData: false,
      },
    },
  });

  const {
    loading: getItemsLoading,
    error: getItemsError,
    data: getItemsData,
    fetchMore: fetchMoreItemsQuery,
  } = useGetItemsV2Query({
    skip: user.suppliers[0].id === "31" && !category?.value,
    fetchPolicy: "cache-and-network",
    variables: {
      itemsInputV2: {
        supplierId: user.suppliers[0].id,
        userId: user.id,
        filters: {
          category: category && !section ? category.value : null,
          brand: brand,
          section: section,
        },
        pagination: {
          offset: 0,
          limit: 20,
        },
        sortBy: {
          field: "name",
          ordering: Ordering.Asc,
        },
      },
    },
  });

  const [getItemsByCategory] = useGetItemsV2LazyQuery();

  // const {
  //   loading: getTagsLoading,
  //   error: getTagsError,
  //   data: getTagsData,
  // } = useGetTagsQuery({
  //   fetchPolicy: "cache-and-network",
  //   variables: {
  //     getTagsInput: {
  //       category: category.value,
  //       pagination: {
  //         offset: 0,
  //         limit: 5,
  //       },
  //     },
  //   },
  // });

  const getOrderedCategories = () => {
    if (!categoriesData?.categoriesBySupplier) return [];

    return [...categoriesData.categoriesBySupplier].sort((a, b) => {
      if (a.ordering && b.ordering) {
        return a.ordering - b.ordering;
      }
      return a.name.localeCompare(b.name);
    });
  };

  const fetchNextBatch = async () => {
    if (!user.suppliers[0].id === "31" || category?.value || isLoadingMore)
      return;

    setIsLoadingMore(true);
    const orderedCategories = getOrderedCategories();
    if (!orderedCategories.length) {
      setIsLoadingMore(false);
      return;
    }

    try {
      const categoriesToFetch = [];
      for (let i = 0; i < 3; i++) {
        const categoryIndex =
          (currentCategoryIndex.current + i) % orderedCategories.length;
        categoriesToFetch.push(orderedCategories[categoryIndex].name);
      }

      const results = await Promise.all(
        categoriesToFetch.map((categoryName) =>
          getItemsByCategory({
            variables: {
              itemsInputV2: {
                supplierId: user.suppliers[0].id,
                userId: user.id,
                filters: {
                  category: !section ? categoryName : null,
                  brand: brand,
                  section: section,
                },
                pagination: {
                  offset: 0,
                  limit: 10000,
                },
                sortBy: {
                  field: "name",
                  ordering: Ordering.Asc,
                },
              },
            },
          })
        )
      );

      const newItems = results.flatMap(
        (result) => result.data?.itemsV2?.items || []
      );

      setAllItems((prev) => [...prev, ...newItems]);
      currentCategoryIndex.current =
        (currentCategoryIndex.current + 3) % orderedCategories.length;
    } catch (error) {
      console.error("Error fetching items:", error);
    }

    setIsLoadingMore(false);
  };

  useEffect(() => {
    if (user.suppliers[0].id === "31" && !category?.value) {
      currentCategoryIndex.current = 0;
      setAllItems([]);
      fetchNextBatch();
    }
  }, [category?.value, user.suppliers[0].id === "31"]);

  if (
    getItemsLoading &&
    !getItemsData &&
    (category?.value || !user.suppliers[0].id === "31")
  ) {
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  }

  if (getItemsError) {
    return (
      <LoadingErrorStatus message={getItemsError.message} errorStatus={true} />
    );
  }

  // if (getTagsLoading && !getTagsData)
  //   return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  // if (getTagsError)
  //   return (
  //     <LoadingErrorStatus message={getTagsError.message} errorStatus={true} />
  //   );

  // const onSelectTag = (tag, index) => {
  //   if (selectedTag && index === selectedTag.index) {
  //     setSelectedTag(null);
  //   } else {
  //     setSelectedTag({
  //       value: tag.value,
  //       index: index,
  //     });
  //   }
  // };

  const renderItem = ({ item, index }) => (
    <MemoizedItemGridCard
      item={item}
      index={index}
      screenName="categories"
      userApproved={user.approved}
      customPrices={user.custom_prices}
    />
  );

  const displayItems =
    user.suppliers[0].id === "31"
      ? category?.value
        ? getItemsData?.itemsV2?.items
        : allItems
      : getItemsData?.itemsV2?.items;

  return (
    <View style={styles.container}>
      <FlatList
        // ListHeaderComponent={
        //   <ScrollView
        //     horizontal
        //     showsHorizontalScrollIndicator={false}
        //     style={styles.tags}
        //   >
        //     <TagList
        //       onSelectTag={onSelectTag}
        //       selectedTag={selectedTag}
        //       tags={getTagsData.tags}
        //     />
        //   </ScrollView>
        // }
        columnWrapperStyle={{
          flex: 1,
          justifyContent: "space-between",
          padding: 0,
        }}
        numColumns={2}
        data={displayItems}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        onEndReached={() => {
          if (category?.value || !user.suppliers[0].id === "31") {
            fetchMoreItemsQuery({
              variables: {
                itemsInputV2: {
                  supplierId: user.suppliers[0].id,
                  userId: user.id,
                  filters: {
                    category: category ? category.value : null,
                    brand: brand,
                    section: section,
                  },
                  pagination: {
                    offset: getItemsData.itemsV2.items.length,
                    limit: 20,
                  },
                  sortBy: {
                    field: "name",
                    ordering: Ordering.Asc,
                  },
                },
              },
              updateQuery: (prev, { fetchMoreResult }) => {
                if (!fetchMoreResult?.itemsV2?.items?.length) {
                  return prev;
                }
                return {
                  itemsV2: {
                    ...prev.itemsV2,
                    items: [
                      ...prev.itemsV2.items,
                      ...fetchMoreResult.itemsV2.items,
                    ],
                  },
                };
              },
            });
          } else {
            fetchNextBatch();
          }
        }}
        onEndReachedThreshold={0.3}
        maxToRenderPerBatch={10}
        windowSize={5}
        removeClippedSubviews={true}
      />
    </View>
  );
}
