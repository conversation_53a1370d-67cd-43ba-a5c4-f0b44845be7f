# Mobile Application React Native - Developer Guide

## Prerequisites
- Node.js and Yarn
- Xcode (for iOS development)
- iOS Simulator
- Expo CLI
- Git

## Account Access Required
- Expo Account
  - Used for builds and updates
  - Required for EAS (Expo Application Services)
- GitHub Account
  - Repository access
  - Branch permissions
- Client Test Accounts
  - C&G Account credentials
  - Whitestone Foods Account credentials

## Local Development Setup

### 1. Initial Setup
```bash
# Install dependencies
yarn

# Generate native builds
npx expo prebuild
```

### 2. iOS Setup
1. Open Project in Xcode
   ```bash
   open ios/AttainWholesale.xcworkspace
   ```
2. Configure Bundle Identifier
   - Change to: `com.[name].attain`
   - Ensure it's unique and valid
   - Update associated provisioning profiles

3. Project Configuration
   - Remove notifications from project settings
   - Verify signing certificates
   - Check deployment targets

4. Build and Run
   - Select appropriate simulator
   - Build project in Xcode
   - Start development server:
     ```bash
     yarn start --lan
     ```

## Development Workflow

### 1. Branch Management
- Main branch: Production code
- `expo-zebra-printer-native-module`: TestFlight builds
- Feature branches: New development

### 2. Code Organization
- Follow React Native best practices
- Maintain consistent styling
- Document component props
- Handle platform-specific code appropriately

### 3. Testing
- Run local tests before commits
- Test on both iOS and Android
- Verify printer functionality
- Test with different account types

## Deployment Process

### 1. Branch Management
```bash
# For production fixes
git checkout main
git merge feature-branch

# For TestFlight builds
git checkout expo-zebra-printer-native-module
git merge feature-branch
```

### 2. EAS Updates
```bash
# Production update
eas update --branch production --message "[Patch] Description"

# Staging update
eas update --branch staging --message "[Patch] Description"
```


# Backend Apollo GraphQL Server - Developer Onboarding Guide

## Prerequisites
- Node.js
- Yarn package manager
- Git

## Local Development Setup

### 1. Repository Setup
```bash
# Clone the repository
git clone https://github.com/josephycyeh/attain-server.git
cd attain-server
```

### 2. Environment Configuration
- Copy the example environment file
  ```bash
  cp .env.example .env
  ```
- Obtain environment variables from Heroku dashboard:
  1. Navigate to Heroku Dashboard > Your App > Settings
  2. Find "Config Vars" section
  3. Copy all required variables to your local `.env` file

### 3. Dependencies & Development
```bash
# Install dependencies
yarn install

# Start development server
yarn run start
```

## Environment Variables
Key environment variables include:
- `DATABASE_URL`: PostgreSQL connection string
- Twilio environment variables (not included in the example env)

## Account Access & Management

### Heroku
- Access Level: Team member or above
- Important Locations:
  - Settings > Config Vars: Environment secrets
  - Resources: Add-ons and services
  - Activity: Deployment logs
  - Metrics: Performance monitoring

# Repository Structure Documentation

## 1. Core Application Structure
### 1.1 Source Code (`src/`)
- Configuration & Environment
  - Environment setup (`config/environment/`)
  - Constants management (`constants/`)
    - Formatting utilities (`formats.ts`)
    - Supplier configurations (`suppliers.ts`)

### 1.2 Database Layer (`db/`)
- Core database configuration (`index.ts`)
- Migration management (`knex/migrations/`)
  - Initial schema setup (2023-06)
  - Invoice tables creation (2023-07)
  - Supplier invoice updates (2023-07)
  - User account updates (2023-08)
  - Recommendation system integration (2023-08)
- Test database setup
  - Creation script (`create_testdb.sql`)
  - Cleanup script (`delete_testdb.sql`)

### 1.3 GraphQL Implementation
- Schema definition (`graphql/`)
  - Mutations directory
  - Queries directory
  - Main GraphQL configuration (`index.ts`)

## 2. API Layer (`apis/`)
### 2.1 REST Endpoints
- Base configuration (`_restEndpoint.ts`)
- Account Management
  - Account creation (`createAccount.ts`, `createAccount.test.ts`)
  - Health monitoring (`healthCheck.ts`, `healthCheck.test.ts`)

### 2.2 Invoice Management
- Core invoice operations
  - Creation (`addInvoice.ts`, `addInvoice.test.ts`)
  - Sending (`sendInvoice.ts`)
  - Item management (`addInvoiceItems.ts`, `deleteInvoiceItems.ts`)
  - ZPL generation (`generateInvoiceZPL.ts`)

### 2.3 Integration Services
- QuickBooks integration
  - Authentication (`authQuickBooks.ts`)
  - Connection management (`disconnectQuickBooks.ts`)
  - Data synchronization (`updateQuickBooksData.ts`)
  - Desktop specific handling (`quickBooksDesktop.ts`)

## 3. Services Layer (`services/`)
### 3.1 Scheduled Jobs
- Order Management
  - Status creation (`createOrderStatus.ts`)
  - Status updates (`updateOrderStatus.ts`)
  - Last ordered date tracking (`addLastOrderedDate.ts`)
  - Order retrieval (`getOrder.ts`)
  - Supplier-specific ordering (`getOrderItemsBySupplier.ts`)

### 3.2 Notification Systems
- Communication services
  - Email handling (`sendEmail.ts`)
  - Push notifications (`expoPushNotification.ts`)
  - SMS services (`sendTextMessages.ts`)
  - Slack integration (`slackWebhook.ts`)
  - Order reminders (`sendOrderReminder.ts`)

### 3.3 Product Management
- Inventory and Catalog
  - Cart operations (`getCart.ts`)
  - Item tagging (`addItemTags.ts`)
  - Section management (`getItemsBySection.ts`)
  - Pricing
    - Custom pricing (`modifyToCustomPricing.ts`)
    - Discount management (`getDiscounts.ts`)
    - User-specific pricing (`getUsersCustomPrices.ts`)

### 3.4 Recommendation Engine
- Store recommendations (`getRecommendedItemsForStore.ts`)
- Python implementation (`getRecommendedItemsForStore.py`)
- Trending items tracking (`getTrendingItems.ts`)

### 3.5 Third-party Integrations
- Algolia search operations (`algoliaOperations.ts`)
- Apollo HTTP client (`apolloHttp.ts`)
- UPCS management (`upcs.ts`)

