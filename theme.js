export const theme = {
  //for react navigation
  colors: {
    primary: "#FCFCFD",
    background: "#BDDAE0",
    card: "#356B82",
    text: "#FCFCFD",
  },
  appColors: {
    primary: "#356B82",
    accent: "#47CD85",
    surface: "#FCFCFD",
    text: "#356B82",
    textNeutral: "#FCFCFD",
    textLight: "#61A5C2",
    textDark: "#313233",
    redText: "#EB4034",
    orangeText: "#B66745",
    greenText: "#3FAE65",
    disabled: "#E3E3E3",
    placeholder: "#E3E3E3",
    backdropDark: "#61A5C2",
    backdropNeutral: "#FCFCFD",
    backdropLight: "#BDDAE0",
  },
  fonts: {
    regular: {
      fontFamily: "Sen_700Bold",
    },
    medium: {
      fontFamily: "Sen_400Regular",
    },
    light: {
      fontFamily: "Lato_400Regular",
    },
  },
  assets: {
    barcode: require("./assets/barcode.png"),
    back: require("./assets/back.png"),
    allVendors: require("./assets/all-vendors.png"),
    seeAllRightLight: require("./assets/see-all-right-light.png"),
    seeAllRightDark: require("./assets/see-all-right-dark.png"),
  },
};

export const empireSnacksTheme = {
  colors: {
    primary: "#FCFCFD",
    background: "#CCC",
    card: "#2B2B2B",
    text: "#FCFCFD",
  },
  appColors: {
    primary: "#2B2B2B",
    accent: "#47CD85",
    surface: "#FCFCFD",
    text: "#2B2B2B",
    textNeutral: "#FCFCFD",
    textLight: "#FF9A00",
    textDark: "#2B2B2B",
    redText: "#EB4034",
    orangeText: "#B66745",
    disabled: "#E3E3E3",
    placeholder: "#E3E3E3",
    backdropDark: "#AAA",
    backdropNeutral: "#FCFCFD",
    backdropLight: "#CCC",
  },
  assets: {
    barcode: require("./assets/barcode-dark.png"),
    back: require("./assets/back-light.png"),
    allVendors: require("./assets/all-vendors-dark.png"),
    seeAllRightLight: require("./assets/see-all-right-light-mono.png"),
    seeAllRightDark: require("./assets/see-all-right-dark-mono.png"),
  },
};

export const cgSnacksTheme = {
  colors: {
    primary: "#FCFCFD",
    background: "#CAD5E7",
    card: "#46659D",
    text: "#FCFCFD",
  },
  appColors: {
    primary: "#46659D",
    accent: "#47CD85",
    surface: "#FCFCFD",
    text: "#46659D",
    textNeutral: "#FCFCFD",
    textLight: "#46659D",
    textDark: "#313233",
    redText: "#EB4034",
    orangeText: "#B66745",
    greenText: "#3FAE65",
    disabled: "#E3E3E3",
    placeholder: "#E3E3E3",
    backdropDark: "#849CC8",
    backdropNeutral: "#FCFCFD",
    backdropLight: "#CAD5E7",
  },
  assets: {
    barcode: require("./assets/barcode-dark.png"),
    back: require("./assets/back-light.png"),
    allVendors: require("./assets/all-vendors-dark.png"),
    seeAllRightLight: require("./assets/see-all-right-light-mono.png"),
    seeAllRightDark: require("./assets/see-all-right-dark-mono.png"),
  },
};
