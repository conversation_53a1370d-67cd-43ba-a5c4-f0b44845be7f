import formatDate from "./formatDate";

const STATUSES = ["Submitted", "In Transit", "Delivered", "Canceled"];
const COLORS = ["#BF8D2C", "#0E9BD8", "#3FAE65", "#B66745"];
const ORDERED_STATUSES = ["Canceled", "Submitted", "In Transit", "Delivered"];

export const tags = STATUSES?.map((name, i) => ({ color: COLORS[i], name }));

export const normalizeStatus = ({ status } = {}) =>
  status?.charAt(0)?.toUpperCase() + status?.slice(1);

export const getColor = (item) =>
  COLORS[STATUSES.indexOf(normalizeStatus(item))];

export const getStatusLevel = (normalizedStatus) =>
  ORDERED_STATUSES.indexOf(normalizedStatus);

export const getOrderDateText = (order, expanded = false) => {
  const status = normalizeStatus(order);

  let date, dateText;
  if (status === STATUSES[3] || !order?.delivery_date) {
    date = order?.date_submitted;
    dateText = "Submitted";
  } else {
    date = order?.delivery_date;
    dateText =
      status === STATUSES[2]
        ? "Delivered"
        : expanded
        ? "Estimated delivery"
        : "Est. delivery";
  }

  return `${dateText}: ${formatDate(date)}`;
};
