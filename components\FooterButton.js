import { StyleSheet, Text, View } from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import { useContext, useMemo } from "react";
import Button from "./Button";
import { UserContext } from "../context/userContext";
import CheckBox from "@react-native-community/checkbox";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      backgroundColor: theme.appColors.primary,
      borderRadius: 10,
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    buttonText: {
      color: theme.appColors.surface,
    },
    checkboxContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 4,
      marginTop: 4,
    },
    container: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropLight,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    leftColumn: {
      flex: 1,
      flexDirection: "column",
      justifyContent: "center",
      marginRight: 16,
    },
    rightColumn: {
      flexShrink: 0,
    },
  });
  return styles;
};

export default function FooterButton({
  mainText,
  buttonText,
  onPress,
  minimum,
  disabled,
  isCredit,
  onChangeIsCredit,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const { user } = useContext(UserContext);

  const hasLeftContent =
    minimum || (user.driver && user.suppliers[0].id === "68") || mainText;

  return (
    <View
      style={[
        styles.container,
        !hasLeftContent && { justifyContent: "center" },
      ]}
    >
      {hasLeftContent && (
        <View style={styles.leftColumn}>
          {minimum && (
            <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
              {`Order Minimum: $${minimum}`}
            </Text>
          )}
          {user.driver && user.suppliers[0].id === "68" && (
            <View style={styles.checkboxContainer}>
              <CheckBox
                boxType="square"
                onFillColor={theme.appColors.text}
                onCheckColor={theme.appColors.surface}
                tintColor={theme.appColors.text}
                onTintColor={theme.appColors.text}
                tintColors={{
                  true: theme.appColors.text,
                  false: theme.appColors.text,
                }}
                animationDuration={0}
                style={{ width: 20, height: 20, marginRight: 10 }}
                onValueChange={onChangeIsCredit}
                value={isCredit}
              />
              <Text
                style={[globalStyles.headlineSmall, globalStyles.text]}
                onPress={() => onChangeIsCredit(!isCredit)}
              >
                Is Credit?
              </Text>
            </View>
          )}
          {mainText && (
            <Text
              style={[
                globalStyles.headlineLarge,
                globalStyles.text,
                {
                  marginTop:
                    minimum || (user.driver && user.suppliers[0].id === "68")
                      ? 5
                      : 0,
                },
              ]}
            >
              {mainText}
            </Text>
          )}
        </View>
      )}

      <View style={styles.rightColumn}>
        <Button
          style={styles.button}
          size="md"
          onPress={onPress}
          disabled={disabled}
        >
          <Text style={[globalStyles.headlineMedium, styles.buttonText]}>
            {buttonText}
          </Text>
        </Button>
      </View>
    </View>
  );
}

FooterButton.propTypes = {
  mainText: PropTypes.string,
  buttonText: PropTypes.string.isRequired,
  onPress: PropTypes.func.isRequired,
  minimum: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disabled: PropTypes.bool,
  isCredit: PropTypes.bool,
  onChangeIsCredit: PropTypes.func,
};
