/** Converts string from military time to display time with AM/PM */
const convertTime = (militaryTime) => {
  const [rawHours, minutes] = militaryTime.split(":");
  let hours = parseInt(rawHours);
  if (isNaN(hours) || hours < 0 || hours > 23) {
    return militaryTime;
  }
  if (hours === 0) {
    return `12:${minutes} AM`;
  }
  if (hours === 12) {
    return `12:${minutes} PM`;
  }
  if (hours > 12) {
    return `${hours - 12}:${minutes} PM`;
  }

  return `${hours}:${minutes} AM`;
};

export default convertTime;
