query GetOrderBySupplier($orderId: ID, $supplierId: ID) {
  orderBySupplier(orderId: $orderId, supplierId: $supplierId) {
    orderItems {
      id
      name
      supplier
      unit_size
      price
      discounted_price
      price_purchased_at
      upc1
      upc2
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
      nacs_subcategory
      nacs_category
      quantity
      item_id
      image
      supplier_code
      qoh
      metadata
      crv
    }
  }
}