import { Text as ReactNativeText } from "react-native";
import PropTypes from "prop-types";

const Text = (props) => {
  return (
    <ReactNativeText
      maxFontSizeMultiplier={1.2}
      style={props.style}
      numberOfLines={props.numberOfLines}
    >
      {props.children}
    </ReactNativeText>
  );
};

Text.propTypes = {
  children: PropTypes.node,
  numberOfLines: PropTypes.number,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

export default Text;
