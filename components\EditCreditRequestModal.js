import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo, useState, useRef } from "react";
import Modal from "react-native-modal";
import OrderItemWithoutPrice from "./OrderItemWithoutPrice";
import CheckBox from "@react-native-community/checkbox";
import Button from "./Button";
import Swiper from "react-native-swiper";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    buttonText: {
      color: theme.appColors.surface,
      fontWeight: "bold",
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
    itemCountContainer: {
      alignItems: "center",
      borderColor: theme.appColors.textLight,
      borderRadius: 5,
      borderWidth: 0.5,
      flexDirection: "row",
      justifyContent: "center",
      paddingHorizontal: 10,
      paddingVertical: 5,
    },
    minusButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.textLight,
      borderColor: theme.appColors.backdropLight,
      borderRadius: 5,
      borderWidth: 1,
      display: "flex",
      height: 25,
      justifyContent: "center",
      width: 25,
    },
    modalContainer: {
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: 20,
      elevation: 5,
      height: "80%",
      margin: 0,
      paddingVertical: 15,
      shadowColor: "black",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    modalHeader: {
      alignItems: "center",
      flexDirection: "row",
      height: 50,
      justifyContent: "space-between",
      marginBottom: 10,
      paddingHorizontal: 20,
      width: "100%",
    },
    plusButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.textLight,
      borderColor: theme.appColors.backdropLight,
      borderRadius: 5,
      borderWidth: 1,
      display: "flex",
      height: 25,
      justifyContent: "center",
      width: 25,
    },
    quantityText: {
      color: theme.appColors.textLight,
      fontSize: 15,
      ...theme.fonts.regular,
      marginHorizontal: 5,
      textAlign: "center",
    },
  });
  return styles;
};

export default function EditCreditRequestModal({
  modalOpen,
  toggleModal,
  items,
  submitRequests,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const swiperRef = useRef(null);

  const [claimItems, setClaimItems] = useState(
    items.map((item) => ({
      ...item,
      quantityAffected: item.quantity,
      isMispick: false,
      isDamaged: false,
      isExpired: false,
    }))
  );

  const onChange = (itemId, key, value) => {
    if (
      (key === "quantityAffected" && value < 1) ||
      value > claimItems.find((item) => item.id === itemId).quantity
    ) {
      return;
    }
    setClaimItems(
      claimItems.map((item) =>
        item.id === itemId ? { ...item, [key]: value } : item
      )
    );
  };

  const scrollToNext = (index) => {
    if (index === claimItems.length - 1) {
      submitRequests(claimItems);
    } else {
      swiperRef.current.scrollTo(index + 1);
    }
  };
  const claimItem = (item, index) => {
    return (
      <View
        style={{
          alignItems: "center",
          flex: 1,
        }}
      >
        <View style={styles.modalHeader}>
          <View>
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {`New Claim (${index + 1} of ${items.length})`}
            </Text>
          </View>
          <TouchableOpacity onPress={toggleModal} style={styles.shopButton}>
            <Ionicons name="close" size={25} color={theme.appColors.text} />
          </TouchableOpacity>
        </View>
        <OrderItemWithoutPrice item={item} quantity={true} />
        <View
          style={{
            marginTop: 30,
            width: "100%",
            paddingHorizontal: 20,
          }}
        >
          <View style={{ flexDirection: "row", marginBottom: 20 }}>
            <CheckBox
              boxType="square"
              onFillColor={theme.appColors.orangeText}
              onCheckColor={theme.appColors.surface}
              tintColor={theme.appColors.orangeText}
              onTintColor={theme.appColors.orangeText}
              animationDuration={0}
              style={{ width: 20, height: 20, marginRight: 10 }}
              onValueChange={(value) => {
                onChange(item.id, "isMispick", value);
              }}
              value={item.isMispick}
            />
            <Text
              style={[
                globalStyles.headlineSmall,
                { color: theme.appColors.orangeText },
              ]}
            >
              Mispick
            </Text>
          </View>
          <View style={{ flexDirection: "row", marginBottom: 20 }}>
            <CheckBox
              boxType="square"
              onFillColor={theme.appColors.orangeText}
              onCheckColor={theme.appColors.surface}
              tintColor={theme.appColors.orangeText}
              onTintColor={theme.appColors.orangeText}
              animationDuration={0}
              style={{ width: 20, height: 20, marginRight: 10 }}
              onValueChange={(value) => {
                onChange(item.id, "isExpired", value);
              }}
              value={item.isExpired}
            />
            <Text
              style={[
                globalStyles.headlineSmall,
                { color: theme.appColors.orangeText },
              ]}
            >
              Expired
            </Text>
          </View>
          <View style={{ flexDirection: "row", marginBottom: 20 }}>
            <CheckBox
              boxType="square"
              onFillColor={theme.appColors.orangeText}
              onCheckColor={theme.appColors.surface}
              tintColor={theme.appColors.orangeText}
              onTintColor={theme.appColors.orangeText}
              animationDuration={0}
              style={{ width: 20, height: 20, marginRight: 10 }}
              onValueChange={(value) => {
                onChange(item.id, "isDamaged", value);
              }}
              value={item.isDamaged}
            />
            <Text
              style={[
                globalStyles.headlineSmall,
                { color: theme.appColors.orangeText },
              ]}
            >
              Damaged
            </Text>
          </View>
        </View>
        <View
          style={{
            marginTop: 10,
            width: "100%",
            paddingHorizontal: 20,
          }}
        >
          <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
            Quantity Affected
          </Text>
          <View style={{ flexDirection: "row", marginTop: 10 }}>
            <View style={styles.itemCountContainer}>
              <TouchableOpacity
                style={styles.minusButton}
                onPress={() =>
                  onChange(
                    item.id,
                    "quantityAffected",
                    item.quantityAffected - 1
                  )
                }
              >
                <Text style={styles.buttonText}>-</Text>
              </TouchableOpacity>

              <Text style={styles.quantityText}>
                {item.quantityAffected}/{item.quantity}
              </Text>

              <TouchableOpacity
                style={styles.plusButton}
                onPress={() =>
                  onChange(
                    item.id,
                    "quantityAffected",
                    item.quantityAffected + 1
                  )
                }
              >
                <Text style={styles.buttonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <Button
          style={{ position: "absolute", bottom: 60 }}
          size="lg"
          onPress={() => scrollToNext(index)}
        >
          {index === items.length - 1 ? "Submit" : "Next"}
        </Button>
      </View>
    );
  };
  return (
    <Modal
      animationIn="slideInUp"
      isVisible={modalOpen}
      hasBackdrop
      onBackdropPress={toggleModal}
      onRequestClose={toggleModal}
    >
      <View style={styles.modalContainer}>
        <Swiper
          ref={swiperRef}
          loop={false}
          showsButtons={false} // Set to true if you want next and prev buttons
          horizontal={true}
          showsPagination={true}
          loadMinimal={true}
          paginationStyle={{ bottom: 10 }}
        >
          {claimItems.map((item, index) => {
            return claimItem(item, index);
          })}
        </Swiper>
      </View>
    </Modal>
  );
}
