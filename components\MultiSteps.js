// Adopted from https://github.com/samassango/react-native-multi-steps
import { useHeaderHeight } from "@react-navigation/elements";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import SafeAreaView from "./SafeAreaView";

function isNextItem(step, len) {
  return !(step === len);
}
function isPreviousValidItem(step) {
  return !(step === 0);
}
function MultiSteps(props) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const headerHeight = useHeaderHeight();
  const [stepIndex, setStepIndex] = useState(0);
  const [Component, setComponent] = useState(null);
  const [canMove, setCanMove] = useState({
    canMovePrevious: false,
    canMoveNext: true,
  });
  const [isSubmit, setIsSubmit] = useState(false);

  useEffect(
    () => setComponent(props.children[stepIndex]),
    [stepIndex, props.children]
  );

  useEffect(() => setIsSubmit(isSubmit), [props.onSubmit, isSubmit]);

  function onNext() {
    let step = stepIndex + 1;
    const len = props.children.length - 1;
    let isNextValid = isNextItem(step, len);
    let isPrevious = step > 0 ? true : false;
    let child = props.children[step];
    if (child) {
      setCanMove({
        canMoveNext: isNextValid,
        canMovePrevious: isPrevious,
      });
      setStepIndex(step);
    } else {
      step = step + 1;
      isNextValid = isNextItem(step, len);
      setCanMove({
        canMoveNext: isNextValid,
        canMovePrevious: isPrevious,
      });
      setStepIndex(step);
    }
    props.onMoveNext(canMove);
  }

  function onPrevious() {
    if (stepIndex !== 0) {
      let step = stepIndex - 1;
      let isPreviousValid = isPreviousValidItem(step); //step === 0 ? false : true;
      let isNext = isNextItem(step, props.children.length - 1);
      let child = props.children[step];
      if (child) {
        setCanMove({
          canMoveNext: isNext,
          canMovePrevious: isPreviousValid,
        });
        setStepIndex(step);
      } else {
        step = step - 1;
        isPreviousValid = isPreviousValidItem(step);
        isNext = isNextItem(step, props.children.length - 1);
        setCanMove({
          canMoveNext: isNext,
          canMovePrevious: isPreviousValid,
        });
        setStepIndex(step);
      }
    }
    props.onMovePrevious(canMove);
  }
  function onSubmit() {
    props.onSubmit();
  }

  return (
    <SafeAreaView
      style={props.containerStyle ? props.containerStyle : styles.container}
      edges={["bottom", "left", "right"]}
    >
      {props.children.length > 1 ? (
        <View style={styles.stepContainer}>
          <Text style={styles.stepText}>
            Step {stepIndex + 1} of {props.children.length}
          </Text>
        </View>
      ) : null}

      <View style={styles.contentContainer}>
        {Component ? (
          Component
        ) : (
          <View>
            <ActivityIndicator size="large" />
            <Text>Loading...</Text>
          </View>
        )}
      </View>

      <View
        style={[
          props.containerButtonStyle
            ? props.containerButtonStyle
            : styles.fixToText,
          Platform.OS === "ios" && styles.iosButtonContainer,
        ]}
      >
        {canMove.canMovePrevious && (
          <TouchableOpacity
            style={
              props.prevButtonStyle
                ? props.prevButtonStyle
                : styles.prevStyleBtn
            }
            onPress={onPrevious}
          >
            <Text
              style={
                props.buttonLabelStyle
                  ? props.buttonLabelStyle
                  : styles.styleBtnLabel
              }
            >
              {props?.config?.previousButtonLabel
                ? props.config.previousButtonLabel
                : "Previous"}
            </Text>
          </TouchableOpacity>
        )}

        {canMove.canMoveNext && (
          <TouchableOpacity
            style={{
              ...(props.nextButtonStyle
                ? props.nextButtonStyle
                : styles.nextStyleBtn),
              opacity: props.blockNext ? 0.5 : 1,
            }}
            onPress={onNext}
            disabled={props.blockNext ?? false}
          >
            <Text
              style={
                props.buttonLabelStyle
                  ? props.buttonLabelStyle
                  : styles.styleBtnLabel
              }
            >
              {props?.config?.nextButtonLabel
                ? props.config.nextButtonLabel
                : "Next"}
            </Text>
          </TouchableOpacity>
        )}
        {!canMove.canMoveNext && typeof props.onSubmit === "function" && (
          <TouchableOpacity
            style={{
              ...(props.submitButtonStyle
                ? props.submitButtonStyle
                : styles.submitStyleBtn),
              opacity: props.blockNext ? 0.5 : 1,
            }}
            onPress={props.onSubmit}
            disabled={props.blockNext}
          >
            <Text
              style={
                props.buttonLabelStyle
                  ? props.buttonLabelStyle
                  : styles.styleBtnLabel
              }
            >
              {props?.config?.submitButtonLabel
                ? props.config.submitButtonLabel
                : "Submit"}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}
const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "space-between",
    },
    contentContainer: {
      flex: 1,
    },
    counter: {
      alignItems: "center",
      backgroundColor: theme.appColors.primary,
      borderRadius: 100,
      color: "white",
      height: 50,
      justifyContent: "center",
      width: 50,
    },
    counterContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    counterText: {
      color: "white",
      fontSize: 30,
      fontWeight: "600",
    },
    fixToText: {
      backgroundColor: "white",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 30,
      paddingVertical: 10,
    },
    iosButtonContainer: {
      bottom: 0,
      left: 0,
      paddingBottom: 20,
      position: "absolute",
      right: 0,
    },
    nextStyleBtn: {
      alignItems: "center",
      backgroundColor: theme.appColors.primary,
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      marginLeft: "auto",
      paddingHorizontal: 4,
      width: "40%",
    },
    prevStyleBtn: {
      alignItems: "center",
      backgroundColor: theme.appColors.primary,
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      paddingHorizontal: 4,
      width: "40%",
    },
    progressBar: {
      backgroundColor: theme.appColors.backdropLight,
      height: 6,
    },
    progressBarContainer: {
      backgroundColor: theme.appColors.primary,
      flexDirection: "row",
      height: 6,
      marginTop: 22,
      position: "absolute",
      width: "100%",
    },
    stepContainer: {
      flexDirection: "row",
      justifyContent: "center",
    },
    stepText: {
      fontSize: 24,
      fontWeight: "600",
      textAlign: "center",
    },
    styleBtnLabel: {
      color: theme.appColors.textNeutral,
      fontWeight: "bold",
    },
    submitStyleBtn: {
      alignItems: "center",
      backgroundColor: theme.appColors.accent,
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      paddingHorizontal: 4,
      width: "40%",
    },
  });
  return styles;
};

MultiSteps.propTypes = {
  children: PropTypes.any,
  containerStyle: PropTypes.object,
  containerButtonStyle: PropTypes.object,
  prevButtonStyle: PropTypes.object,
  nextButtonStyle: PropTypes.object,
  submitButtonStyle: PropTypes.object,
  buttonLabelStyle: PropTypes.object,
  onMoveNext: PropTypes.func,
  onMovePrevious: PropTypes.func,
  onSubmit: PropTypes.func,
  config: PropTypes.shape({
    nextButtonLabel: PropTypes.string,
    previousButtonLabel: PropTypes.string,
    submitButtonLabel: PropTypes.string,
  }),
  blockNext: PropTypes.bool,
};
export default MultiSteps;
