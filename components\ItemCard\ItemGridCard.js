import { useContext, useMemo } from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import PropTypes from "prop-types";
import { useTheme, useRoute, useNavigation } from "@react-navigation/native";
import ItemCardContainer from "./ItemCardContainer";
import ItemCardText from "./ItemCardText";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { UserContext } from "../../context/userContext";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;
const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      bottom: 0,
      right: 9,
    },
    itemView: {
      alignItems: "center",
      borderBottomWidth: 0.5,
      borderColor: theme.appColors.backdropLight,
      // height: 270, // keep sizes consistent
      height: screenHeight > 800 ? screenHeight * 0.3 : 270,
      justifyContent: "center",
      overflow: "hidden",
      padding: 8,
      paddingTop: 15,
      width: "50%",
    },
    leftItemView: {
      borderRightWidth: 0.5,
    },
    textBox: {
      paddingHorizontal: 12,
      width: "100%",
    },
  });
  return styles;
};

const ItemGridCard = ({
  highlighted,
  index,
  item,
  userApproved,
  customPrices,
  onPress,
  showButton,
}) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  const { user } = useContext(UserContext);

  return (
    <ItemCardContainer
      buttonPosition={styles.button}
      item={item}
      style={[styles.itemView].concat(
        index % 2 === 0 ? [styles.leftItemView] : []
      )}
      userApproved={userApproved}
      disabled={user.driver && !user.is_user_account}
      onPress={onPress}
      showButton={showButton}
    >
      <View style={styles.textBox}>
        <ItemCardText
          highlighted={highlighted}
          item={item}
          largeStyle={globalStyles.headlineSmall}
          smallStyle={globalStyles.bodyMedium}
          userApproved={userApproved}
          customPrices={customPrices}
        />
      </View>
    </ItemCardContainer>
  );
};

ItemGridCard.propTypes = {
  highlighted: PropTypes.bool,
  index: PropTypes.number.isRequired,
  item: PropTypes.object.isRequired,
  screenName: PropTypes.string,
  userApproved: PropTypes.bool,
  customPrices: PropTypes.array,
  onPress: PropTypes.func,
  showButton: PropTypes.bool,
};

export default ItemGridCard;
