import React from "react";
import Text from "../../../components/Text";
import useGlobalStyles from "../../../lib/useGlobalStyles";

interface MinPriceProps {
  item: {
    min_sale_price?: number;
  };
  enablePriceEditing: boolean;
  isEditingPrice: boolean;
}

export default function MinPrice({
  item,
  enablePriceEditing,
  isEditingPrice,
}: MinPriceProps) {
  const globalStyles = useGlobalStyles();

  const hasMinPrice = item?.min_sale_price && item.min_sale_price > 0;
  const showMinPrice = hasMinPrice && enablePriceEditing && isEditingPrice;

  if (!showMinPrice) {
    return null;
  }

  return (
    <Text
      style={[
        globalStyles.bodySmall,
        globalStyles.textLight,
        {
          fontSize: globalStyles.bodySmall.fontSize,
          fontWeight: "600",
          marginTop: -12,
        },
      ]}
    >
      Min: ${item?.min_sale_price?.toFixed(2)}
    </Text>
  );
}
