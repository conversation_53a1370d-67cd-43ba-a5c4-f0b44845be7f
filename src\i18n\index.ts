import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import * as Localization from "expo-localization";

// Import translations
import en from "../translations/en.json";
import es from "../translations/es.json";

const resources = {
  en: {
    translation: en,
  },
  es: {
    translation: es,
  },
};

i18next.use(initReactI18next).init({
  resources,
  lng: Localization.locale.split("-")[0], // Use device language by default
  fallbackLng: "en", // Fallback to English if translation is missing
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18next;
