import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";

import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";

const styles = StyleSheet.create({
  icon: {
    height: 30,
    width: 30,
  },
  rightContent: {
    alignItems: "flex-end",
    minWidth: 30,
  },
  title: {
    lineHeight: 30,
  },
  topRow: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
});

const Header = ({ children, goBack, iconSource, title, rightContent }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();

  return (
    <View style={globalStyles.navBar}>
      <View style={styles.topRow}>
        {goBack && (
          <TouchableOpacity onPress={goBack}>
            <Image source={theme.assets.back} style={styles.icon} />
          </TouchableOpacity>
        )}
        <Text
          style={[
            globalStyles.headlineLarge,
            globalStyles.textNeutral,
            styles.title,
          ]}
        >
          {title}
        </Text>
        <View style={styles.rightContent}>
          {rightContent}
          {iconSource && !rightContent && (
            <Image source={iconSource} style={styles.icon} />
          )}
        </View>
      </View>
      {children}
    </View>
  );
};

Header.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
  goBack: PropTypes.func,
  iconSource: PropTypes.object,
  title: PropTypes.string.isRequired,
  rightContent: PropTypes.node,
};

export default Header;
