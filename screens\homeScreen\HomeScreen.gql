query HomeScreen($userId: ID, $getCategoriesInput: GetCategoriesInput, $getSectionsInput: GetSectionsInput, $getRecommendationsInput: GetRecommendationsInput) {

  categories(getCategoriesInput: $getCategoriesInput) {
    image
    name
    value
  }

  brandSpotlights(userId: $userId) {
    id
    name
    spotlight_image
  }

  spotlights(userId: $userId) {
    id
    logo
    name
    spotlight_image
  }
    sections(getSectionsInput: $getSectionsInput) {
    value
    name
    items {
      id
      name
      price
      discounted_price
      unit_size
      qoh
      qty_on_hand
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      oos
      back_in_stock_date    
      created_at
      metadata
      crv
      avg_cases_per_week
      moq
      item_uom_id
      isFavorited
      uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    }
    
  }

  
  brandSections(getSectionsInput: $getSectionsInput) {
    itemsPreview {
      id
      image
      supplier_code
      name
      price
      discounted_price
      nacs_category
      unit_size
      qoh
      qty_on_hand
      upc1
      last_ordered_date
      oos
      back_in_stock_date    
      created_at
      avg_cases_per_week
      moq
      item_uom_id
      uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    }

    id
    logo
    name
    spotlight_image

  }

  recommendations(getRecommendationsInput: $getRecommendationsInput) {
    item {
      id
      name
      price
      discounted_price
      unit_size
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      last_ordered_date
      oos
      back_in_stock_date    
      created_at
      moq
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
    num_store
    quantity
  }
}

