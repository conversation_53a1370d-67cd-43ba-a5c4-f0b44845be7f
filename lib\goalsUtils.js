export const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount || 0);
};

export const formatPercentage = (value) => {
  return `${(value || 0).toFixed(1)}%`;
};

export const getProgressColor = (percentage) => {
  if (percentage >= 90) return "#10B981";
  if (percentage >= 70) return "#F59E0B";
  if (percentage >= 50) return "#F97316";
  return "#EF4444";
};

export const formatGoalType = (type) => {
  switch (type) {
    case "SALES_AMOUNT":
      return "Sales";
    case "STORES_SOLD_TO":
      return "Stores Sold To";
    default:
      return type;
  }
};

export const formatPeriod = (period) => {
  return period.charAt(0) + period.slice(1).toLowerCase();
};

export const getBadgeColor = (percentage) => {
  if (percentage >= 90) return { backgroundColor: "#D1FAE5", color: "#065F46" };
  if (percentage >= 70) return { backgroundColor: "#FEF3C7", color: "#92400E" };
  if (percentage >= 50) return { backgroundColor: "#FED7AA", color: "#9A3412" };
  return { backgroundColor: "#FEE2E2", color: "#991B1B" };
};

export const getCurrentPeriodDisplay = (period) => {
  switch (period) {
    case "DAILY":
      return "Today";
    case "WEEKLY":
      return "This Week";
    case "MONTHLY":
      return "This Month";
    default:
      return "Current Period";
  }
};

export const getUserAssignment = (goal, employeeId) => {
  return goal.assignments.find(
    (assignment) =>
      assignment.employee_id === employeeId || !assignment.employee_id
  );
};
