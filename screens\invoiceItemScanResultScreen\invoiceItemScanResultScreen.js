import { useState, useContext, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  FlatList,
  TextInput,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import {
  useGetInvoiceItemMatchQuery,
  MatchStatus,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import Header from "../../components/Header";
import Button from "../../components/Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    clicked: {
      borderColor: "#DEDEDE",
      borderWidth: 1,
      padding: 4,
    },
    deliveryGraphic: {
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 37,
    },
    deliveryGraphicLine: {
      borderColor: "red",
      borderRadius: 1,
      borderStyle: "dashed",
      borderWidth: 2,
      flex: 1,
    },
    deliveryGraphicLineClip: {
      flex: 1,
      height: 2,
      marginHorizontal: -2,
      overflow: "hidden",
    },
    deliveryGraphicPoint: {
      borderRadius: 5,
      height: 10,
      width: 10,
      zIndex: 2,
    },
    headerContainer: {
      alignItems: "center",
      paddingBottom: 20,
      paddingHorizontal: 20,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 55,
      width: 55,
    },
    itemInfoContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    itemPrice: {
      marginLeft: 8,
    },
    itemTextContainer: {
      marginLeft: 8,
      width: 180,
    },
    list: {
      backgroundColor: "white",
      height: "100%",
    },
    listHeader: {
      margin: 20,
    },
    oosText: {
      color: "gray",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
    },
    orderItemContainer: {
      marginBottom: 15,
      paddingHorizontal: 20,
    },
    orderNameContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: 6,
    },
    orderSummaryView: {
      alignSelf: "stretch",
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    pencilIconStyle: {
      paddingRight: 5,
    },
    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      width: 30,
    },
    tag: {
      marginBottom: 10,
      marginHorizontal: -3,
      marginTop: 20,
    },
    truck: {
      marginLeft: 28,
      marginRight: 25,
      marginVertical: 5,
    },
  });
  return styles;
};

export default function InvoiceItemScanResultScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();
  const { upcCode, invoiceId } = route.params;
  const {
    loading: getInvoiceItemMatchLoading,
    data: getInvoiceItemMatchData,
    error: getInvoiceItemItemMatchError,
  } = useGetInvoiceItemMatchQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getInvoiceItemMatchInput: {
        upc: upcCode,
        invoiceId,
      },
    },
    pollInterval: 500,
  });
  if (getInvoiceItemMatchLoading && !getInvoiceItemMatchData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getInvoiceItemItemMatchError)
    return (
      <LoadingErrorStatus
        message={getInvoiceItemItemMatchError.message}
        errorStatus={true}
      />
    );

  const { itemMatches: invoiceLineItems, matchStatus: invoiceItemMatchStatus } =
    getInvoiceItemMatchData.invoiceItemMatch;

  const InvoiceItemScanResultText = () => {
    var text = "";
    if (invoiceItemMatchStatus === MatchStatus.Single) {
      text = "We found an exact match. Click to check in item.";
    } else if (invoiceItemMatchStatus === MatchStatus.Multiple) {
      text =
        "We found multiple matches within your order. Please select the correct item to check in.";
    } else if (invoiceItemMatchStatus === MatchStatus.Suggested) {
      text =
        "We couldn't find an exact match for the item. Please select from the options or manually select from the invoice screen.";
    } else {
      text =
        "Cannot find item. Please scan again or enter the quantity maually from the invoice screen.";
    }
    return (
      <Text style={[globalStyles.text, globalStyles.headline]}>{text}</Text>
    );
  };
  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={{
          marginHorizontal: 30,
          marginVertical: 10,
          paddingHorizontal: 15,
          paddingVertical: 8,
          justifyContent: "space-between",
          flexDirection: "row",
          borderWidth: 1,
          borderColor: theme.appColors.backdropDark,
          borderRadius: 10,
        }}
        onPress={() =>
          // navigation.navigate("ActionItemsScreen", { invoiceItemId: 1 })

          navigation.navigate("InvoiceItemDetailScreen", {
            invoiceItem: item,
            invoiceId: invoiceId,
          })
        }
      >
        <View style={{ justifyContent: "center", width: "80%" }}>
          <Text
            numberOfLines={1}
            style={[globalStyles.text, globalStyles.headlineSmall]}
          >
            {item.name}
          </Text>
        </View>
        <View
          style={{
            borderWidth: 1,
            borderColor: theme.appColors.backdropLight,
            borderRadius: 5,
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 8,
            paddingVertical: 6,
            width: "17%",
          }}
        >
          <Text style={[globalStyles.text, globalStyles.headlineSmall]}>
            {item.checked_in_quantity || 0}/{item.quantity}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <SafeAreaView
      style={[
        globalStyles.container,
        { backgroundColor: theme.appColors.surface },
      ]}
    >
      <View style={globalStyles.lightContainer}>
        <FlatList
          ListHeaderComponent={
            <View
              style={{
                marginHorizontal: 30,
                marginTop: 30,
                marginBottom: 20,
              }}
            >
              <InvoiceItemScanResultText />
            </View>
          }
          data={invoiceLineItems}
          numColumns={1}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          style={styles.list}
        />
      </View>
    </SafeAreaView>
  );
}
