import { StyleSheet } from "react-native";
import { useTheme } from "@react-navigation/native";
import React from "react";
const getGlobalStyles = (props) =>
  StyleSheet.create({
    bodyLarge: {
      ...props.fonts.light,
      fontSize: 14,
    },
    bodyMedium: {
      ...props.fonts.light,
      fontSize: 12,
    },
    bodySmall: {
      ...props.fonts.light,
      fontSize: 11,
    },
    card: {
      backgroundColor: props.appColors.surface,
      borderRadius: 10,
      padding: 20,
    },
    container: {
      backgroundColor: props.appColors.primary,
      flex: 1,
    },
    headlineLarge: {
      ...props.fonts.regular,
      fontSize: 20,
    },
    headlineMedium: {
      ...props.fonts.regular,
      fontSize: 16,
    },
    headlineSmall: {
      ...props.fonts.regular,
      fontSize: 14,
    },
    labelLarge: {
      ...props.fonts.medium,
      fontSize: 16,
    },
    labelMedium: {
      ...props.fonts.medium,
      fontSize: 13,
    },
    lightContainer: {
      backgroundColor: props.appColors.surface,
      flex: 1,
    },
    navBar: {
      backgroundColor: props.appColors.primary,
      paddingHorizontal: 25,
      paddingVertical: 20,
    },
    scrollView: {
      flex: 1,
      padding: 20,
    },
    scrollViewHorizontal: {},
    statusBar: {
      backgroundColor: props.appColors.primary,
      height: 50,
      marginTop: -50,
    },
    text: {
      color: props.appColors.text,
    },
    textDisabled: {
      color: props.appColors.disabled,
    },
    textLight: {
      color: props.appColors.textLight,
    },
    textNeutral: {
      color: props.appColors.textNeutral,
    },
    titleLarge: {
      ...props.fonts.regular,
      fontSize: 12,
    },
    titleMedium: {
      ...props.fonts.regular,
      fontSize: 10,
    },
  });

function useGlobalStyles() {
  const { colors, fonts, appColors } = useTheme();

  // We only want to recompute the stylesheet on changes in color.
  const styles = React.useMemo(
    () => getGlobalStyles({ colors, fonts, appColors }),
    [colors]
  );

  return styles;
}

export default useGlobalStyles;
