query GetOrdersBySupplier($getOrdersBySupplierInput: GetOrdersBySupplierInput) {
  ordersBySupplier(getOrdersBySupplierInput: $getOrdersBySupplierInput) {
    orders {
        customerDetails {
            id
            name
        }
        id
        order_number
        status
        subtotal
        date_submitted
        orderName
        config
        orderItems {
          id
          name
          unit_size
          price
          discounted_price
          price_purchased_at
          upc1
          upc2
          nacs_category
          nacs_subcategory
          quantity
          item_id
          image
          oos
          qoh
          metadata
          supplier
          archived
          crv
        }
        delivery_date
        supplier
        supplier_logo
        invoice {
          id
          discount
          signature
          signature_name
          paid
          credit
          payment_method
          notes
          subtotal
          total
          config
        }
      }
    }
  }
