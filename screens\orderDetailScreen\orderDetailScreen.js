import { Feather } from "@expo/vector-icons";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import { useContext, useEffect, useRef, useState } from "react";
import {
  Alert,
  AppState,
  FlatList,
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";

import Header from "../../components/Header";
import Text from "../../components/Text";
import {
  useGetInvoiceItemsQuery,
  useGetOrdersBySupplierQuery,
  useUpdateInvoiceMutation,
  useUpdateOrderMutation,
  useCreateInvoiceMutation,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";

import { MaterialCommunityIcons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as WebBrowser from "expo-web-browser";
import Modal from "react-native-modal";
import RNZebraBluetoothPrinter from "react-native-zebra-bluetooth-printer";
import Button from "../../components/Button";
import InvoiceInfoModal from "../../components/InvoiceInfoModal";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import OrderSupplierIcon from "../../components/OrderSupplierIcon";
import SignatureModal from "../../components/SignatureModal";
import { Tag } from "../../components/TagList";
import { UserContext } from "../../context/userContext";
import {
  getColor,
  getOrderDateText,
  getStatusLevel,
  normalizeStatus,
} from "../../lib/getOrderStatusInfo";
import { getPrice } from "../../lib/getPrice";
import { getTotal } from "../../lib/getTotal";
import { posthog } from "../../src/posthog";
import { getUomQuantity, getUomName } from "../../lib/uomHelpers";
import ExpoZebraPrinter from "../../modules/expo-zebra-printer";
import { createStackNavigator } from "@react-navigation/stack";
import EditOrderScreen from "./EditOrderScreen";
import SearchResultsScreen from "../searchResultsScreen/searchResultsScreen";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    deliveryGraphic: {
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 37,
    },
    deliveryGraphicLine: {
      borderColor: "red",
      borderRadius: 1,
      borderStyle: "dashed",
      borderWidth: 2,
      flex: 1,
    },
    deliveryGraphicLineClip: {
      flex: 1,
      height: 2,
      marginHorizontal: -2,
      overflow: "hidden",
    },
    deliveryGraphicPoint: {
      borderRadius: 5,
      height: 10,
      width: 10,
      zIndex: 2,
    },
    disabledButtonText: {
      color: "grey",
    },
    headerContainer: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropLight,
      paddingBottom: 20,
      paddingHorizontal: 20,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemHeaderContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 55,
      width: 55,
    },
    itemInfoContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    itemPrice: {
      marginLeft: 8,
    },
    itemTextContainer: {
      marginLeft: 8,
      width: 180,
    },
    itemTotalsText: {
      margin: 20,
    },
    list: {
      backgroundColor: "white",
      height: "100%",
    },
    listHeader: {
      margin: 20,
    },
    oosText: {
      color: "gray",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
    },
    orderActionButtons: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 20,
      justifyContent: "center",
      marginTop: 20,
      paddingHorizontal: 20,
    },
    orderItemContainer: {
      marginBottom: 15,
      paddingHorizontal: 20,
    },
    orderNameContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginLeft: 15,
      paddingBottom: 6,
    },
    orderSummaryView: {
      alignSelf: "stretch",
      backgroundColor: theme.appColors.backdropDark,
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 10,
    },
    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      width: 30,
    },
    signatureModal: {
      justifyContent: "flex-end",
      margin: 0,
    },
    tag: {
      marginBottom: 10,
      marginHorizontal: -3,
      marginTop: 20,
    },
    truck: {
      marginLeft: 28,
      marginRight: 25,
      marginVertical: 5,
    },
  });
  return styles;
};

const OrderDetailStack = createStackNavigator();

const OrderDetailScreenComponent = ({ navigation, route }) => {
  const { user, frontendUrl, backendUrl } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();

  const { orderId } = route.params;
  const showModal =
    user.driver &&
    (user.suppliers[0].id === "31" || user.suppliers[0].id === "47") &&
    route.params.showModal;

  const {
    loading: getOrdersLoading,
    data: getOrdersData,
    error: getOrdersError,
    refetch: getOrdersRefetch,
  } = useGetOrdersBySupplierQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getOrdersBySupplierInput: {
        supplierId: user.suppliers[0].id,
        filters: { userId: user.id, ids: [orderId] },
      },
    },
  });

  const order = getOrdersData?.ordersBySupplier?.orders?.[0];

  const {
    loading: getInvoiceItemsLoading,
    data: getInvoiceItemsData,
    error: getInvoiceItemsError,
    refetch: getInvoiceItemsRefetch,
  } = useGetInvoiceItemsQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      orderId: order?.id,
    },
    skip: !order?.id,
  });

  const [updateOrder] = useUpdateOrderMutation();
  const [updateInvoice] = useUpdateInvoiceMutation();
  const [createInvoice] = useCreateInvoiceMutation();

  const [selectedPrinter, setSelectedPrinter] = useState(null);
  const [modalVisible, setModalVisible] = useState(showModal);
  const [invoicePrinting, setInvoicePrinting] = useState(false);
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      "change",
      async (nextAppState) => {
        if (
          appState.current.match(/inactive|background/) &&
          nextAppState === "active"
        ) {
          // console.log("App has come to the foreground!");
          await getOrdersRefetch();
          await getInvoiceItemsRefetch();
        }

        appState.current = nextAppState;
        // console.log("AppState", appState.current);
      }
    );

    fetchSelectedPrinterFromStorage();

    return () => {
      subscription.remove();
    };
  }, []);

  const fetchSelectedPrinterFromStorage = async () => {
    const selectedPrinter = await AsyncStorage.getItem("selected_printer");
    setSelectedPrinter(JSON.parse(selectedPrinter));
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", async () => {
      fetchSelectedPrinterFromStorage();
      await getOrdersRefetch();
      await getInvoiceItemsRefetch();
    });
    return unsubscribe;
  }, [navigation]);

  if (
    (getOrdersLoading || getInvoiceItemsLoading) &&
    (!getOrdersData || !getInvoiceItemsData)
  )
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getOrdersError)
    return (
      <LoadingErrorStatus message={getOrdersError.message} errorStatus={true} />
    );

  if (getInvoiceItemsError) console.error(getInvoiceItemsError.message);

  const orderHasInvoice = order?.invoice && order?.invoice.id;
  const replaceOrderWithInvoice = orderHasInvoice && user.driver;

  console.log(order?.orderItems);

  var discount = 0;
  if (order?.orderItems) {
    for (var cartItem of order?.orderItems) {
      discount =
        discount +
        Math.round(cartItem.price * cartItem.quantity * 100) / 100 -
        Math.round(getPrice(cartItem) * cartItem.quantity * 100) / 100;
    }
  }

  const orderItemPressed = (orderItem) => {
    navigation.navigate("ItemDetail", {
      id: orderItem.item_id,
    });
  };

  const invoiceItemPressed = (invoiceItem) => {
    navigation.navigate("SearchResults", {
      supplier: user.suppliers[0].name,
      defaultQuery: invoiceItem.name,
    });
  };

  const status = normalizeStatus(order);
  const statusLevel = getStatusLevel(status);

  const alignment = {
    alignSelf: ["flex-start", "flex-start", "center", "flex-end"][statusLevel],
  };

  const DeliveryGraphicLine = ({ level }) => (
    <View style={styles.deliveryGraphicLineClip}>
      <View
        style={[
          styles.deliveryGraphicLine,
          {
            borderColor:
              statusLevel >= level
                ? theme.appColors.text
                : theme.appColors.backdropDark,
          },
        ]}
      />
    </View>
  );

  DeliveryGraphicLine.propTypes = { level: PropTypes.number.isRequired };

  const DeliveryGraphicPoint = ({ level }) => (
    <View
      style={[
        styles.deliveryGraphicPoint,
        {
          backgroundColor:
            statusLevel >= level
              ? theme.appColors.text
              : theme.appColors.backdropDark,
        },
      ]}
    />
  );

  DeliveryGraphicPoint.propTypes = { level: PropTypes.number.isRequired };

  const renderOrderButton = (onPress, text, image, props) => (
    <Button
      variant="light"
      size="md"
      style={{
        flex: 1,
        minHeight: 70,
        padding: 10,
      }}
      onPress={onPress}
      {...props}
    >
      <View
        style={{
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          gap: 5,
          height: "100%",
        }}
      >
        {image}
        <Text
          style={[
            globalStyles.headlineMedium,
            !props?.disabled ? globalStyles.text : styles.disabledButtonText,
          ]}
        >
          {text}
        </Text>
      </View>
    </Button>
  );

  const renderOrderButtons = () => {
    switch (order?.status?.toLowerCase()) {
      case "submitted":
        return (
          <>
            {renderOrderButton(
              () => {
                navigation.navigate("EditOrderScreen", {
                  order,
                  invoiceItems: getInvoiceItemsData.invoiceItems,
                });
              },
              `Edit`,
              <MaterialCommunityIcons
                name="file-document-edit"
                size={24}
                color={theme.appColors.textLight}
              />
            )}
            {renderOrderButton(
              async () => {
                try {
                  await updateOrder({
                    variables: {
                      updateOrderInput: {
                        orderId: order?.id,
                        userId: user.id,
                        supplierId: user.suppliers[0].id,
                        status: "In Transit",
                      },
                    },
                  });
                  await getOrdersRefetch();
                  await getInvoiceItemsRefetch();
                } catch (err) {
                  console.error(err);
                  Alert.alert(
                    "Error",
                    "Failed to confirm order. Please try again."
                  );
                }
              },
              `Confirm ${order?.subtotal >= 0 ? "Order" : "Credit"}`,
              <MaterialCommunityIcons
                name="file-plus"
                size={24}
                color={theme.appColors.textLight}
              />
            )}
          </>
        );
      case "in transit":
        return (
          <>
            {renderOrderButton(
              async () => {
                await updateOrder({
                  variables: {
                    updateOrderInput: {
                      status: "Delivered",
                      orderId: order?.id,
                      userId: user.id,
                      supplierId: user.suppliers[0].id,
                    },
                  },
                });
                await getOrdersRefetch();
                await getInvoiceItemsRefetch();
              },
              `Deliver`,
              <MaterialCommunityIcons
                name="truck-delivery"
                size={24}
                color={theme.appColors.textLight}
              />
            )}
            {renderOrderButton(
              () => {
                navigation.navigate("EditOrderScreen", {
                  order,
                  invoiceItems: getInvoiceItemsData.invoiceItems,
                });
              },
              `Edit`,
              <MaterialCommunityIcons
                name="file-document-edit"
                size={24}
                color={theme.appColors.textLight}
              />
            )}
            {order?.invoice && order?.invoice.id
              ? renderOrderButton(
                  () => handlePrintInvoice(),
                  `Print`,
                  <MaterialCommunityIcons
                    name="printer"
                    size={24}
                    color={theme.appColors.textLight}
                  />,
                  {
                    disabled: !selectedPrinter,
                    loading: invoicePrinting,
                  }
                )
              : null}
          </>
        );
      case "delivered":
        return (
          <>
            {order?.invoice && order?.invoice.id
              ? renderOrderButton(
                  () => {
                    setModalVisible(true);
                  },
                  String(user.suppliers[0].id) === "31" ||
                    String(user.suppliers[0].id) === "47"
                    ? "Add Info"
                    : order?.invoice && order?.invoice.signature
                    ? `View Signature`
                    : `Add Signature`,
                  <MaterialCommunityIcons
                    name="signature-text"
                    size={24}
                    color={theme.appColors.textLight}
                  />
                )
              : null}
            {order?.invoice && order?.invoice.id
              ? renderOrderButton(
                  () => handlePrintInvoice(),
                  `Print Invoice`,
                  <MaterialCommunityIcons
                    name="printer"
                    size={24}
                    color={
                      !selectedPrinter
                        ? styles.disabledButtonText
                        : theme.appColors.textLight
                    }
                  />,
                  {
                    disabled: !selectedPrinter,
                    loading: invoicePrinting,
                  }
                )
              : null}
          </>
        );
      default:
        <></>;
    }
  };

  const orderButtons = renderOrderButtons();

  const handlePrintInvoice = async () => {
    console.log("Printing invoice...");
    setInvoicePrinting(true);
    try {
      const { zpl } = await fetch(
        `${backendUrl}/api/generateInvoiceZPL?invoiceId=${order?.invoice.id}`
      ).then((response) => response.json());
      if (Platform.OS === "android") {
        await RNZebraBluetoothPrinter.print(selectedPrinter.address, zpl);
      } else {
        await ExpoZebraPrinter.connectAccessory(selectedPrinter.address);
        await ExpoZebraPrinter.sendZPLToAccessory(zpl);
      }
      Alert.alert("Invoice Print Successful", "Printing invoice...");
    } catch (err) {
      console.error(err);
      Alert.alert(
        "Invoice Print Failed",
        `Error occurred while printing invoice. Please try again. (${err})`
      );
    }
    setInvoicePrinting(false);
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <Header
        goBack={navigation.goBack}
        title={`${order?.subtotal >= 0 ? "Order" : "Credit"} Details`}
      />
      <FlatList
        ListHeaderComponent={
          <>
            <View style={styles.headerContainer}>
              <View style={styles.orderSummaryView}>
                <View>
                  <View style={styles.orderNameContainer}>
                    <Text
                      style={[
                        globalStyles.headlineMedium,
                        globalStyles.textNeutral,
                      ]}
                    >
                      {`${order?.subtotal >= 0 ? "Order" : "Credit"} #${
                        order?.order_number
                      }`}
                    </Text>
                  </View>
                </View>
                <View style={{ justifyContent: "center" }}>
                  {user.id === "11" && order?.supplier === "Coremark" ? (
                    <Button
                      variant="light"
                      size="sm"
                      onPress={() => {
                        posthog.capture("order_checkin_clicked", {
                          id: order?.id,
                        });
                        navigation.navigate("InvoiceScreen", {
                          order,
                        });
                      }}
                    >
                      <Text>Check In</Text>
                    </Button>
                  ) : order?.subtotal > 0 &&
                    order?.supplier !== "Whitestone Foods" &&
                    order?.supplier !== "C&G Snacks" ? (
                    <Button
                      variant="light"
                      size="md"
                      onPress={() =>
                        navigation.navigate("RequestCreditsScreen", {
                          order,
                        })
                      }
                    >
                      <Text>Request Credit</Text>
                    </Button>
                  ) : null}
                </View>
              </View>
              <View style={[styles.tag, alignment]}>
                <Tag
                  bold
                  color={getColor(order)}
                  highlighted
                  label={status}
                  onPress={
                    user.driver
                      ? async () => {
                          await WebBrowser.openBrowserAsync(
                            orderHasInvoice
                              ? `${frontendUrl}/payments/${order?.invoice.id}`
                              : `${frontendUrl}/orders/${order?.id}`
                          );
                        }
                      : null
                  }
                />
              </View>
              <View style={styles.deliveryGraphic}>
                <DeliveryGraphicPoint level={1} />
                <DeliveryGraphicLine level={2} />
                <DeliveryGraphicPoint level={2} />
                <DeliveryGraphicLine level={3} />
                <DeliveryGraphicPoint level={3} />
              </View>
              <Feather
                name="truck"
                color={
                  statusLevel > 0
                    ? theme.appColors.text
                    : theme.appColors.backdropDark
                }
                size={30}
                style={[styles.truck, alignment]}
              />
              <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
                {getOrderDateText(order, true)}
              </Text>
            </View>
            {user.driver ? (
              <View
                style={[
                  styles.orderActionButtons,
                  orderButtons && orderButtons.props.children.length > 2
                    ? { gap: 10 }
                    : {},
                ]}
              >
                {orderButtons?.props?.children?.length
                  ? orderButtons.props.children
                  : null}
              </View>
            ) : null}
            <View style={styles.itemHeaderContainer}>
              <Text
                style={[
                  globalStyles.headlineLarge,
                  globalStyles.text,
                  styles.listHeader,
                ]}
              >
                Items ({order?.orderItems.length})
              </Text>
              <View style={styles.itemTotalsText}>
                {user.show_prices ? (
                  <Text
                    style={[
                      globalStyles.headlineLarge,
                      globalStyles.text,
                      { textAlign: "right" },
                    ]}
                  >
                    Total: $
                    {replaceOrderWithInvoice && getInvoiceItemsData
                      ? getTotal(getInvoiceItemsData.invoiceItems)
                      : getTotal(order?.orderItems)}
                  </Text>
                ) : null}
                {user.supplier_beta && discount ? (
                  <Text
                    style={[
                      globalStyles.bodyLarge,
                      globalStyles.text,
                      { textAlign: "right" },
                    ]}
                  >
                    Discounts: ${discount.toFixed(2)}
                  </Text>
                ) : null}
              </View>
            </View>
          </>
        }
        data={
          replaceOrderWithInvoice && getInvoiceItemsData
            ? getInvoiceItemsData.invoiceItems
            : order?.orderItems
        }
        numColumns={1}
        renderItem={({ item }) => {
          const supplier = user.suppliers.find(
            (supplier) => supplier.name === item.supplier
          );
          const isDisabled = item.archived;

          // Calculate UOM-adjusted display quantity
          const uomQuantity = item?.item_uom_id
            ? getUomQuantity(item, item.item_uom_id)
            : 1;
          const displayQuantity = Number(
            (item.quantity / uomQuantity).toFixed(2)
          );
          const selectedUomName = item?.item_uom_id
            ? getUomName(item, item.item_uom_id)
            : "Unit";

          return (
            <TouchableOpacity
              onPress={() => {
                if (!isDisabled) {
                  replaceOrderWithInvoice
                    ? invoiceItemPressed(item)
                    : orderItemPressed(item);
                }
              }}
              key={item.id}
              style={[
                styles.orderItemContainer,
                isDisabled && { opacity: 0.5 },
              ]}
            >
              <View style={styles.itemContainer}>
                <View style={styles.itemInfoContainer}>
                  {replaceOrderWithInvoice && getInvoiceItemsData ? null : (
                    <Image
                      style={styles.itemImage}
                      source={{
                        uri:
                          item.image ||
                          supplier?.config?.defaultItemImage ||
                          "https://fakeimg.pl/100x100?text=no+image",
                      }}
                    />
                  )}
                  <View style={styles.itemTextContainer}>
                    <Text
                      style={[
                        globalStyles.headlineSmall,
                        globalStyles.text,
                        item.oos && styles.oosText,
                        isDisabled && { color: theme.appColors.textLight },
                      ]}
                      numberOfLines={2}
                    >
                      {item.name} {item.oos ? "(Out Of Stock)" : ""}{" "}
                      {isDisabled ? "(Discontinued)" : ""}
                    </Text>
                    {item?.item_uom_id && selectedUomName !== "Unit" && (
                      <Text
                        style={[
                          {
                            color: theme.appColors.text,
                            fontStyle: "italic",
                            fontSize: 11,
                          },
                          item.oos && styles.oosText,
                        ]}
                      >
                        UOM: {selectedUomName} (
                        {getUomQuantity(item, item.item_uom_id)})
                      </Text>
                    )}
                    {/* {user.show_prices ? (
                      <Text
                        style={[
                          globalStyles.bodySmall,
                          globalStyles.textLight,
                          item.oos && styles.oosText,
                        ]}
                      >
                        ${getPrice(item) ?? item.price_purchased_at.toFixed(2)}{" "}
                        / {item.unit_size > 1 ? `${item.unit_size}ct` : "each"}
                      </Text>
                    ) : null} */}
                  </View>
                  <View style={styles.quantity}>
                    <Text style={[globalStyles.bodyLarge, globalStyles.text]}>
                      {displayQuantity}
                    </Text>
                  </View>
                  {user.show_prices ? (
                    <Text
                      style={[
                        globalStyles.bodyLarge,
                        globalStyles.text,
                        styles.itemPrice,
                        item.oos && styles.oosText,
                      ]}
                    >
                      $
                      {(
                        (item.price_purchased_at || item.price) *
                        parseFloat(item.quantity)
                      ).toFixed(2)}
                    </Text>
                  ) : null}
                </View>
              </View>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item) => item.id}
        style={styles.list}
      />
      <Modal
        animationIn="slideInUp"
        animationOut="slideOutDown"
        isVisible={modalVisible}
        hasBackdrop
        onBackdropPress={() => setModalVisible(false)}
        onRequestClose={() => setModalVisible(!modalVisible)}
        style={styles.signatureModal}
      >
        {user.suppliers[0].id === "31" || user.suppliers[0].id === "47" ? (
          <InvoiceInfoModal
            onClose={async (invoiceFields) => {
              if (invoiceFields && Object.keys(invoiceFields).length > 0) {
                const updateOrderInput = {
                  orderId: order?.id,
                  userId: user.id,
                  supplierId: user.suppliers[0].id,
                  // Update order-level fields
                  config: invoiceFields.config,
                  notes: invoiceFields.notes,
                  // Update invoice through nested invoice field
                  invoice: {
                    id: order?.invoice.id,
                    paid: invoiceFields.paid,
                    credit: invoiceFields.credit,
                    total: invoiceFields.total,
                    payment_method: invoiceFields.payment_method,
                    notes: invoiceFields.notes,
                    config: invoiceFields.config,
                  },
                };
                try {
                  await updateOrder({
                    variables: {
                      updateOrderInput,
                    },
                  });
                  await getOrdersRefetch();
                } catch (err) {
                  console.error(err);
                }
              }
              setModalVisible(false);
            }}
            subtotal={order?.invoice && order?.invoice.subtotal}
            paid={order?.invoice && order?.invoice.paid}
            credit={order?.invoice && order?.invoice.credit}
            paymentMethod={order?.invoice && order?.invoice.payment_method}
            notes={order?.invoice && order?.invoice.notes}
            config={order?.invoice && order?.invoice.config}
          />
        ) : (
          <SignatureModal
            onClose={async (signatureImageString, signatureName) => {
              if (signatureImageString && signatureName) {
                // console.log(signatureImageString);
                const updateOrderInput = {
                  orderId: order?.id,
                  userId: user.id,
                  supplierId: user.suppliers[0].id,
                  // Update invoice through nested invoice field
                  invoice: {
                    id: order?.invoice.id,
                    signature: signatureImageString,
                    signature_name: signatureName,
                  },
                };
                await updateOrder({
                  variables: {
                    updateOrderInput,
                  },
                });
                await getOrdersRefetch();
              }
              setModalVisible(false);
            }}
            signature={order?.invoice && order?.invoice.signature}
            signatureName={order?.invoice && order?.invoice.signature_name}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};

export default function OrderDetailScreen({ route }) {
  return (
    <OrderDetailStack.Navigator>
      <OrderDetailStack.Screen
        name="OrderDetailScreen"
        component={OrderDetailScreenComponent}
        options={{ headerShown: false }}
        initialParams={route.params}
      />
      <OrderDetailStack.Screen
        name="EditOrderScreen"
        component={EditOrderScreen}
        options={{ headerShown: false }}
      />
      <OrderDetailStack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{ title: "Search Results" }}
      />
    </OrderDetailStack.Navigator>
  );
}
