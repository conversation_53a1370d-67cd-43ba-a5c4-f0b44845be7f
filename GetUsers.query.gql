query GetUsers($getUsersInput: GetUsersInput) {
  users(getUsersInput: $getUsersInput) {
    id
    name
    suppliers {
      id
      logo
      name
      minimum
      phone_number
      address
      config
      orderCount
    }
    user_name
    supplier_beta
    approved
    driver
    address
    delivery_window {
      days_of_week
      start_time
      end_time
    }
    custom_prices {
      item_id
      price
    }
    custom_uom_prices {
      item_id
      uom_prices {
        item_uom_id
        user_id
        price
        uom_name
        uom_id
      }
    }
    store_group
    route_id
    config
    net_terms_days
  }
}
