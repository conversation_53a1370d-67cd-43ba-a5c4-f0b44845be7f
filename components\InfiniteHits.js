import { View, FlatList } from "react-native";
import PropTypes from "prop-types";
import { connectInfiniteHits } from "react-instantsearch-native";

import ItemGridCard from "./ItemCard/ItemGridCard";
import ItemsSection from "./ItemsSection";

const InfiniteHits = ({
  hits,
  hasMore,
  refineNext,
  supplier,
  searchQuery,
  user,
  onPress,
  showButton,
  hiddenItemIds,
}) => {
  const filteredHits = hiddenItemIds
    ? hits.filter((hit) => !hiddenItemIds.has(hit.id.toString()))
    : hits;

  hits = filteredHits.filter((hit) => hit.archived !== true);
  hits.forEach((hit) => {
    const customPrice = user.custom_prices.find(
      (i) => i.item_id === hit.objectID
    );
    if (customPrice) {
      if (hit.discounted_price) {
        hit.discounted_price = customPrice.price;
      }
      hit.price = customPrice.price;
    }
  });

  function distinctResults(results, attributeForDistinct) {
    const categories = results.reduce((acc, curr) => {
      if (curr._distinctSeqID === 0) {
        acc.push({
          distinctAttribute: curr[attributeForDistinct],
          results: [curr],
        });
      } else {
        acc[acc.length - 1].results.push(curr);
      }
      return acc;
    }, []);

    return categories;
  }

  const sections = supplier ? [] : distinctResults(hits, "supplier");

  const renderItem = ({ item, index }) => {
    return (
      <ItemsSection
        variant="light"
        title={item.distinctAttribute}
        items={item.results || item.itemsPreview}
        index={index}
        showSuppliers
        to={"SearchResults"}
        params={{ defaultQuery: searchQuery, supplier: item.distinctAttribute }}
        userApproved={user.approved}
      />
    );
  };

  return (
    <View style={{ flex: 1 }}>
      {supplier ? (
        <FlatList
          data={hits}
          numColumns={2}
          columnWrapperStyle={{
            flex: 1,
            justifyContent: "space-between",
            padding: 0,
            marginHorizontal: 20,
          }}
          keyExtractor={(item) => item.objectID}
          onEndReached={() => hasMore && refineNext()}
          renderItem={({ item, index }) => (
            <ItemGridCard
              item={item}
              index={index}
              highlighted
              screenName="search"
              userApproved={user.approved}
              customPrices={user.custom_prices}
              onPress={onPress}
              showButton={showButton}
            />
          )}
        />
      ) : (
        <FlatList
          data={sections}
          keyExtractor={(item) => item.objectID}
          onEndReached={() => hasMore && refineNext()}
          renderItem={renderItem}
        />
      )}
    </View>
  );
};

InfiniteHits.propTypes = {
  hits: PropTypes.arrayOf(PropTypes.object).isRequired,
  hasMore: PropTypes.bool.isRequired,
  refineNext: PropTypes.func.isRequired,
  user: PropTypes.object.isRequired,
  onPress: PropTypes.func,
  showButton: PropTypes.bool,
  searchQuery: PropTypes.string,
  supplier: PropTypes.string,
  hiddenItemIds: PropTypes.arrayOf(PropTypes.string),
};

export default connectInfiniteHits(InfiniteHits);
