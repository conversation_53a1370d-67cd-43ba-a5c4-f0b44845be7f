import { useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import {
  useGetAccountsQuery,
  useUpdateDefaultInAccountMutation,
} from "../../generated/graphql";
import { UserContext } from "../../context/userContext";
import { Feather } from "@expo/vector-icons";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import { posthog } from "../../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
    },
    boldSecondaryText: {
      fontSize: 11,
      fontWeight: "600",
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    itemsContainer: {
      marginBottom: 10,
      marginHorizontal: 20,
      marginTop: 20,
    },
    mainButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 8,
      color: "white",
      height: 48,
      justifyContent: "center",
      marginTop: 90,
      width: 296,
    },
    mainButtonText: {
      color: theme.appColors.textDark,
      fontSize: 20,
    },
    orderView: {
      borderColor: "gray",
      borderRadius: 15,
      borderStyle: "solid",
      borderWidth: 1,
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 15,
      paddingHorizontal: 10,
      paddingVertical: 15,
    },
    scrollView: {
      backgroundColor: "white",
    },
    test: {
      alignContent: "center",
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
    },
    thirdTitleText: {
      color: theme.appColors.textDark,
      fontSize: 17,
    },
  });
  return styles;
};

export default function PaymentScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { user } = useContext(UserContext);
  const { loading, data, error } = useGetAccountsQuery({
    fetchPolicy: "network-only",
    variables: {
      businessId: user.id,
    },
    pollInterval: 500,
  });
  const [updateDefaultInAccount] = useUpdateDefaultInAccountMutation();

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  const onAddPaymentPressed = () => {
    posthog.capture("add_payment_method_clicked");
    navigation.navigate("AddPaymentMethod");
  };
  const selectDefault = (account) => {
    var setDefault = true;
    updateDefaultInAccount({
      variables: {
        updateDefaultInAccountInput: {
          accountId: account.id,
          isDefault: setDefault,
          userId: user.id,
        },
      },
    });
  };

  const bankAccounts = data.accounts.filter(
    (account) => account.type === "bank"
  );
  const creditCards = data.accounts.filter((account) => account.type === "cc");
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.itemsContainer}>
          {data.accounts.length >= 1 ? (
            <View>
              <View
                style={{
                  justifyContent: "space-between",
                  flexDirection: "row",
                }}
              >
                <Text style={styles.boldMainText}>Payment Methods</Text>
                <TouchableOpacity
                  style={{
                    backgroundColor: "gray",
                    paddingHorizontal: 10,
                    borderRadius: 10,
                  }}
                  onPress={onAddPaymentPressed}
                >
                  <View
                    style={{
                      justifyContent: "center",
                      flexDirection: "column",
                      flex: 1,
                    }}
                  >
                    <Text style={styles.boldSecondaryText}>Add More</Text>
                  </View>
                </TouchableOpacity>
              </View>
              {bankAccounts.length > 0 && (
                <View style={{ paddingVertical: 20 }}>
                  {bankAccounts.map((account) => (
                    <View key={account.id}>
                      <TouchableOpacity
                        style={styles.orderView}
                        key={account.id}
                        onPress={() => {
                          selectDefault(account);
                        }}
                      >
                        <View>
                          <Text>{account.balanceBankAccount.accountName}</Text>
                          <Text>
                            {account.balanceBankAccount.institutionName}
                          </Text>
                          <Text>
                            {account.balanceBankAccount.accountNumberMask}
                          </Text>
                        </View>
                        {account.is_default && (
                          <View>
                            <Feather name="check" size={24} color="black" />
                          </View>
                        )}
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
              {creditCards.length > 0 && (
                <View style={{ paddingVertical: 20 }}>
                  {creditCards.map((account) => (
                    <View key={account.id}>
                      <TouchableOpacity
                        style={styles.orderView}
                        key={account.id}
                        onPress={() => {
                          selectDefault(account);
                        }}
                      >
                        <View>
                          <Text>{account.balanceCreditCard.brand}</Text>
                          <Text>*{account.balanceCreditCard.last4}</Text>
                          <Text>
                            {account.balanceCreditCard.expiredMonth}/
                            {account.balanceCreditCard.expiredYear}
                          </Text>
                        </View>
                        {account.is_default && (
                          <View>
                            <Feather name="check" size={24} color="black" />
                          </View>
                        )}
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ) : (
            <View style={{ marginTop: "40%" }}>
              <Text
                style={[
                  styles.thirdTitleText,
                  { marginTop: "5%", textAlign: "center" },
                ]}
              >
                You have no payment method saved
              </Text>
              <View style={styles.test}>
                <TouchableOpacity
                  style={styles.mainButton}
                  onPress={onAddPaymentPressed}
                >
                  <Text style={styles.mainButtonText}>Add payment method</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
