Pod::Spec.new do |s|
  s.name           = 'ExpoZebraPrinter'
  s.version        = '1.0.0'
  s.summary        = 'Zebra LinkOS for Expo'
  s.description    = 'Implementation of Zebra LinkOS Native SDK for Expo'
  s.author         = '<PERSON>'
  s.homepage       = 'https://docs.expo.dev/modules/'
  s.platforms      = { :ios => '13.4' }
  s.source         = { git: '' }
  s.static_framework = true

  s.dependency 'ExpoModulesCore'

  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'SWIFT_COMPILATION_MODE' => 'wholemodule'
  }

  s.source_files = "**/*.{h,m,mm,swift,hpp,cpp}"
end
