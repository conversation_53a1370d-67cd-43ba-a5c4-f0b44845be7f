import {
  TouchableOpacity,
  View,
  Image,
  ScrollView,
  Text,
  StyleSheet,
} from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import React, { useMemo } from "react";
import { useTheme, useNavigation } from "@react-navigation/native";

const createStyles = (theme) => StyleSheet.create({});

const Categories = (props) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();

  return (
    <View
      style={{
        paddingTop: 16,
        paddingBottom: 16,
        paddingLeft: 20,
        backgroundColor: "white",
      }}
    >
      <ScrollView horizontal={true}>
        {props.categories.map((category, index) => {
          return (
            <TouchableOpacity
              style={{ marginRight: 20 }}
              key={index}
              onPress={() =>
                navigation.navigate("SelectItems", {
                  category: category,
                  title: category.name,
                })
              }
            >
              <View
                style={{
                  flexDirection: "column",
                  alignItems: "center",
                  flex: 1,
                }}
              >
                <Image
                  style={{
                    width: 45,
                    height: 45,
                    borderRadius: 22.5,
                    overflow: "hidden",
                  }}
                  source={{ uri: category.image }}
                ></Image>
                <Text
                  style={[
                    globalStyles.labelMedium,
                    globalStyles.text,
                    { textAlign: "center", flex: 1 },
                  ]}
                >
                  {category.name}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

Categories.propTypes = {
  children: PropTypes.node,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

export default Categories;
