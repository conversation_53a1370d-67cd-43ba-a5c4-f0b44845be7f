mutation UpdateItemInCart($updateItemInCartInput: UpdateItemInCartInput!) {
  updateItemInCart(updateItemInCartInput: $updateItemInCartInput) {
    id
    cartItems {
    id
    image
    item_id
    item_uom_id
    nacs_category
    nacs_subcategory
    name
    oos
    price
    discounted_price
    price_purchased_at
    custom_price
    quantity
    supplier
    supplier_code
    unit_size
    qoh
    upc1
    upc2
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
  }
    subCarts {
      cartItems {
        id
        image
        item_id
        item_uom_id
        nacs_category
        nacs_subcategory
        name
        oos
        price
        discounted_price
        custom_price
        quantity
        supplier
        supplier_code
        unit_size
        qoh
        upc1
        upc2
        uoms {
          id
          name
          supplier_id
          uom_id
          quantity
          item_id
          price
          upc
          archived
        }
      }
      supplier
    }
  }
}
