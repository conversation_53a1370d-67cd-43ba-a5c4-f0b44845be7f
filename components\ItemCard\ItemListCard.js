import { useMemo } from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import PropTypes from "prop-types";
import { useTheme } from "@react-navigation/native";

import Text from "../Text.js";
import ItemCardContainer from "./ItemCardContainer";
import ItemCardText from "./ItemCardText";
import useGlobalStyles from "../../lib/useGlobalStyles";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;
const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      bottom: -7,
      right: -3,
    },
    itemView: {
      alignItems: "center",
      backgroundColor: "white",
      borderColor: theme.appColors.backdropLight,
      borderRadius: 10,
      borderStyle: "solid",
      borderWidth: 0.5,
      // height: 205, // added this bc the items werent the same height
      height: screenHeight > 800 ? screenHeight * 0.25 : 205,
      // justifyContent: "center",
      marginBottom: 10,
      overflow: "hidden",
      padding: 8,
      width: screenWidth > 400 ? screenWidth * 0.4 : 140,
    },
    supplierText: {
      textDecorationLine: "underline",
    },
    textBox: {
      marginTop: 0,
      paddingHorizontal: 5,
      width: "100%",
    },
  });
  return styles;
};

const ItemListCard = ({
  highlighted,
  item,
  section,
  showSupplier,
  disabled,
  userApproved,
  customPrices,
}) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();

  return (
    <ItemCardContainer
      buttonPosition={styles.button}
      item={item}
      style={styles.itemView}
      disabled={disabled}
      userApproved={userApproved}
      section={section}
    >
      <View style={styles.textBox}>
        {/* {showSupplier && item.supplier && (
          <Text
            style={[
              globalStyles.bodySmall,
              globalStyles.textLight,
              styles.supplierText,
            ]}
          >
            {item.supplier}
          </Text>
        )} */}

        <ItemCardText
          highlighted={highlighted}
          item={item}
          largeStyle={globalStyles.titleLarge}
          smallStyle={globalStyles.bodySmall}
          userApproved={userApproved}
          customPrices={customPrices}
        />
      </View>
    </ItemCardContainer>
  );
};

ItemListCard.propTypes = {
  highlighted: PropTypes.bool,
  item: PropTypes.object.isRequired,
  section: PropTypes.string,
  showSupplier: PropTypes.bool,
  userApproved: PropTypes.bool,
  customPrices: PropTypes.array,
  disabled: PropTypes.bool,
};

export default ItemListCard;
