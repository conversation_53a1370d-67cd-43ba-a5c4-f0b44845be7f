import { StyleSheet, View, StatusBar, TouchableOpacity } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 15,
    },
    buttonContainer: {
      marginBottom: 0,
      marginTop: 10,
    },
    centerView: {
      marginBottom: 50,
      marginTop: 100,
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    largeBoldMainText: {
      fontSize: 30,
      fontWeight: "600",
      marginBottom: 15,
    },
    mainButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 20,
      color: "white",
      height: 45,
      justifyContent: "center",
    },
    mainButtonText: {
      color: "white",
      fontSize: 20,
    },
    safeContainer: {
      backgroundColor: "white",
      marginHorizontal: 20,
      marginTop: 20,
    },
  });
  return styles;
};

export default function OrderSubmittedScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const onBackToCartPressed = () => {
    navigation.goBack();
    navigation.popToTop();
  };
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeContainer}>
        <View style={styles.centerView}>
          <Text style={styles.largeBoldMainText}>Order Submitted!</Text>
          <Text style={styles.boldMainText}>
            Keep track of your order status in the home page.
          </Text>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.mainButton}
            onPress={onBackToCartPressed}
          >
            <Text style={styles.mainButtonText}>Dismiss</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
