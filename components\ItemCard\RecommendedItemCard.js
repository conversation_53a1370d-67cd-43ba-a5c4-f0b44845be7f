import { useMemo } from "react";
import { StyleSheet, View } from "react-native";
import PropTypes from "prop-types";
import { useTheme } from "@react-navigation/native";

import ItemCardContainer from "./ItemCardContainer";
import ItemCardText from "./ItemCardText";
import useGlobalStyles from "../../lib/useGlobalStyles";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      bottom: -7,
      right: -3,
    },
    itemView: {
      alignItems: "center",
      height: 270, // keep sizes consistent
      justifyContent: "center",
      overflow: "hidden",
      padding: 8,
      paddingTop: 15,
      width: "50%",
    },
    textBox: {
      paddingHorizontal: 12,
      width: "100%",
    },
  });
  return styles;
};

const RecommendedItemCard = ({
  highlighted,
  index,
  recommendation,
  userApproved,
}) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();

  return (
    <ItemCardContainer
      buttonPosition={styles.button}
      item={recommendation.item}
      style={styles.itemView}
      recommendedRank={index + 1}
      section={"recommendation-modal"}
      userApproved={userApproved}
    >
      <View style={styles.textBox}>
        <ItemCardText
          highlighted={highlighted}
          item={recommendation && recommendation.item}
          largeStyle={globalStyles.headlineSmall}
          smallStyle={globalStyles.bodyMedium}
          recommendedSold={recommendation && recommendation.quantity}
          userApproved={userApproved}
        />
      </View>
    </ItemCardContainer>
  );
};

RecommendedItemCard.propTypes = {
  highlighted: PropTypes.bool,
  index: PropTypes.number.isRequired,
  recommendation: PropTypes.object.isRequired,
  screenName: PropTypes.string,
  userApproved: PropTypes.bool,
};

export default RecommendedItemCard;
