import { useState, useContext, useCallback, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  InteractionManager,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import MultiSteps from "../../components/MultiSteps";
import Text from "../../components/Text";
import AddressAutocomplete from "../../components/AddressAutocomplete";
import { useCreateUserMutation } from "../../generated/graphql";
import { useTheme } from "@react-navigation/native";
import { getAuth, signInWithEmailAndPassword } from "firebase/auth";
import { UserContext } from "../../context/userContext";
import { posthog } from "../../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    backBtn: {
      alignItems: "center",
      backgroundColor: theme.appColors.primary,
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      marginTop: 40,
      paddingHorizontal: 4,
    },
    backBtnText: {
      color: "white",
      fontWeight: "600",
    },
    boldHeaderText: {
      fontSize: 30,
      fontWeight: "600",
    },
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
    },
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
      marginTop: 10,
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      padding: 10,
    },
    multistepContainer: {
      flex: 1,
      paddingBottom: Platform.OS === "ios" ? 20 : 0,
    },
    multistepInsideContainer: {
      flexDirection: "column",
      flex: 1,
      marginBottom: Platform.OS === "ios" ? 60 : 20,
      marginHorizontal: 20,
    },
    safeContainer: {
      flexDirection: "column",
      flex: 1,
      marginHorizontal: 0,
    },
    signupCompleteError: {
      paddingHorizontal: 15,
      paddingTop: 20,
    },
    textInput: {
      borderRadius: 10,
      borderStyle: "solid",
      borderWidth: 1,
      height: 45,
      marginTop: 5,
      padding: 10,
      width: "80%",
    },
    textInputError: {
      color: "red",
    },
  });
  return styles;
};

export default function SignupScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const auth = getAuth();
  const { setIsLoggedIn, backendUrl } = useContext(UserContext);

  const { control, handleSubmit, formState, watch } = useForm({
    defaultValues: {
      firstName: "",
      // lastName: "",
      signupCode: "",
      name: "",
      addressLine1: "",
      addressLine2: "",
      // addressCity: "",
      // addressState: "",
      // addressZipcode: "",
      phoneNumber: "",
      ein: "",
      // username: "",
      // password: "",
      // confirmPassword: "",
    },
  });
  const { isValid, errors } = formState;
  const [userInfo, setUserInfo] = useState({
    user_name: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [signupComplete, setSignupComplete] = useState(false);
  const [signupLoading, setSignupLoading] = useState(false);
  const [signupError, setSignupError] = useState(false);

  const [createUser] = useCreateUserMutation();

  const onSubmit = async (data) => {
    if (signupLoading) {
      return;
    }
    setSignupLoading(true);
    const storeAddress = `${data.addressLine1}${
      data.addressLine2 ? ", " + data.addressLine2 : ""
    }`;
    posthog.capture("signup_step_2_completed", {
      storeName: data.name,
      storeAddress,
    });
    try {
      const paddedFirstName = `${data.firstName
        .trim()
        .toLowerCase()
        .replaceAll(" ", "-")
        .replaceAll("'", "")
        .replaceAll("'", "")}${
        data.firstName.trim().length <= 3
          ? Math.floor(Math.random() * 900 + 100)
          : ""
      }`;

      const password = `${paddedFirstName}-ny`;
      const user_name = `${data.name}`
        .trim()
        .toLowerCase()
        .replaceAll(" ", "-")
        .replaceAll("'", "")
        .replaceAll("’", "");

      const res = await createUser({
        variables: {
          createUserInput: {
            name: data.name,
            // address: `${data.addressLine1}${
            //   data.addressLine2 ? ", " + data.addressLine2 : ""
            // }, ${data.addressCity}, ${data.addressState} ${
            //   data.addressZipcode
            // }`,
            address: storeAddress,
            phone_number: data.phoneNumber,
            user_name,
            password,
            ein: data.ein,
            // user_name: data.username,
            // password: data.password,
            suppliers: [
              //TODO: Remove hardcoded strings and replace with results from API Endpoint to fetch supplierId.
              ...(data.signupCode.toLowerCase() === "beverage" ? ["30"] : []),
              ...(data.signupCode.toLowerCase() === "empire" ? ["47"] : []),
              ...(data.signupCode.toLowerCase() === "agdist" ? ["60"] : []),
              ...(data.signupCode.toLowerCase() === "cgsnacks" ? ["31"] : []),
              ...(data.signupCode.toLowerCase() === "demosupplier"
                ? ["69"]
                : []),
            ],
            created_by: "self-serve",
          },
        },
      });
      setUserInfo({
        user_name,
        password,
      });
      // ampli.signupCompleted({ username: user_name });
      posthog.capture("signup_completed", { username: user_name });
      setSignupComplete(true);
    } catch (err) {
      console.error(err);
      setSignupError(true);
    } finally {
      setSignupLoading(false);
    }
  };

  const onChangeWrapper = (onChange) => (text) => {
    onChange(text);
  };

  const onBlurWrapper = async () => {
    await handleSubmit(() => {})();
  };

  const signIn = async () => {
    console.log(userInfo.user_name);
    console.log(userInfo.password);
    signInWithEmailAndPassword(
      auth,
      userInfo.user_name + "@joinattain.com",
      userInfo.password
    )
      .then(() => {
        posthog.capture("login", { automated: true });
        setIsLoggedIn(true);
      })
      .catch((error) => {
        const errorCode = error.code;
        var errorMessage = error.message;
        if (errorCode == "auth/too-many-requests") {
          errorMessage = "Too Many Requests, Wait A Minute To Try Again";
        }
        if (errorCode == "auth/wrong-password") {
          errorMessage = "Wrong Password";
        }

        if (errorCode == "auth/user-not-found") {
          errorMessage = "Wrong Username";
        }

        posthog.capture("login_error", {
          automated: true,
          error: errorMessage,
        });
        console.log(errorMessage);
      });
  };

  const handleMoveNext = async () => {
    Keyboard.dismiss();

    if (currentStep === 1) {
      posthog.capture("signup_step_1_completed", {
        firstName: watch("firstName"),
        phoneNumber: watch("phoneNumber"),
        supplierCode: watch("signupCode"),
      });
    }
    setCurrentStep(currentStep + 1);
    await handleSubmit(() => {})();
  };

  const handleMovePrevious = async () => {
    Keyboard.dismiss();

    if (currentStep === 2) {
      posthog.capture("signup_step_2_progress", {
        storeName: watch("name"),
        storeAddress: `${watch("addressLine1")}${
          watch("addressLine2") ? ", " + watch("addressLine2") : ""
        }`,
      });
    }
    setCurrentStep(currentStep - 1);
    await handleSubmit(() => {})();
  };

  const steps = [
    <View style={styles.multistepInsideContainer} key="step-1">
      <Text style={styles.boldHeaderText}>Let&apos;s Get Started!</Text>
      <Text style={styles.boldSecondaryText}>First Name</Text>
      <Controller
        control={control}
        rules={{
          required: true,
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.textInput}
            placeholderTextColor="gray"
            placeholder="Please enter your First Name"
            onChangeText={onChangeWrapper(onChange)}
            onBlur={() => {
              onBlur();
              onBlurWrapper();
            }}
            autoCapitalize="sentences"
            value={value}
            autoCorrect={false}
            textContentType="givenName"
          />
        )}
        name="firstName"
      />
      {Boolean(errors.firstName) && (
        <Text style={styles.textInputError}>First Name required</Text>
      )}
      <Text style={styles.boldSecondaryText}>Phone Number</Text>
      <Controller
        control={control}
        rules={{
          required: true,
          minLength: 10,
          pattern:
            /^([0-9]{10})|((\([0-9]{3}\)|[0-9]{3}-?)\s?[0-9]{3}\s?-?\s?[0-9]{4})$/g,
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.textInput}
            placeholderTextColor="gray"
            placeholder="Please enter your phone number"
            onChangeText={onChangeWrapper(onChange)}
            onBlur={() => {
              onBlur();
              onBlurWrapper();
            }}
            autoCapitalize="none"
            value={value}
            autoCorrect={false}
            textContentType="telephoneNumber"
          />
        )}
        name="phoneNumber"
      />
      {Boolean(errors.phoneNumber) && (
        <Text style={styles.textInputError}>Valid Phone Number required</Text>
      )}
      <Text style={styles.boldSecondaryText}>Supplier Referral Code</Text>
      <Controller
        control={control}
        rules={{
          required: true,
          pattern: /^\s*(beverage|empire|AGDIST|cgsnacks|demosupplier)\s*$/i, //TODO: Remove hardcoded strings and replace with API Endpoint to verify signupCode.
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.textInput}
            placeholderTextColor="gray"
            placeholder="Please enter your Referral Code"
            onChangeText={onChangeWrapper(onChange)}
            onBlur={() => {
              onBlur();
              onBlurWrapper();
            }}
            autoCapitalize="none"
            value={value}
            autoCorrect={false}
            textContentType="none"
          />
        )}
        name="signupCode"
      />
      {Boolean(errors.signupCode) && (
        <Text style={styles.textInputError}>Invalid Referral Code entered</Text>
      )}
    </View>,
    <View style={styles.multistepInsideContainer} key="step-2">
      <Text style={styles.boldHeaderText}>Tell Us About Your Store</Text>
      <Text style={styles.boldSecondaryText}>Store Name</Text>
      <Controller
        control={control}
        rules={{
          required: true,
          minLength: 5,
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.textInput}
            placeholderTextColor="gray"
            placeholder="Example: Joe’s Convenience Store"
            onChangeText={onChangeWrapper(onChange)}
            onBlur={() => {
              onBlur();
              onBlurWrapper();
            }}
            autoCapitalize="none"
            value={value}
            autoCorrect={false}
            textContentType="name"
          />
        )}
        name="name"
      />
      {Boolean(errors.name) && (
        <Text style={styles.textInputError}>
          Your Store Name must be at least 5 characters long
        </Text>
      )}
      <Text style={styles.boldSecondaryText}>Store Address</Text>
      <Controller
        control={control}
        name="addressLine1"
        rules={{ required: true }}
        render={({ field: { onChange, value } }) => (
          <AddressAutocomplete
            style={styles.textInput}
            onAddressSelected={onChange}
            backendUrl={backendUrl}
          />
        )}
      />
      {Boolean(errors.addressLine1) && (
        <Text style={styles.textInputError}>
          Store Address Line 1 is required
        </Text>
      )}

      <Controller
        control={control}
        rules={{
          required: false,
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            style={styles.textInput}
            placeholderTextColor="gray"
            placeholder="Example: Unit 2"
            onChangeText={onChangeWrapper(onChange)}
            onBlur={() => {
              onBlur();
              onBlurWrapper();
            }}
            autoCapitalize="none"
            value={value}
            autoCorrect={false}
            textContentType="streetAddressLine2"
          />
        )}
        name="addressLine2"
      />
      {watch("signupCode").toLowerCase() === "empire" && (
        <>
          <Text style={styles.boldSecondaryText}>EIN / Tax ID</Text>
          <Controller
            control={control}
            rules={{
              required: false,
            }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={styles.textInput}
                placeholderTextColor="gray"
                placeholder="32-2125555"
                onChangeText={onChangeWrapper(onChange)}
                onBlur={() => {
                  onBlur();
                  onBlurWrapper();
                }}
                autoCapitalize="none"
                value={value}
                autoCorrect={false}
                textContentType="none"
              />
            )}
            name="ein"
          />
        </>
      )}
    </View>,
  ];

  const inputFields = [
    ["signupCode"],
    ["name"],
    ["addressLine1", "addressLine2", "addressCityState", "addressZipcode"],
    ["phoneNumber"],
    ["username"],
    ["password", "confirmPassword"],
  ];

  // console.log("errors", errors);
  // console.log(
  //   "errors fields",
  //   inputFields[currentStep - 1]
  //     .map((field) => errors[field])
  //     .filter((error) => error).length > 0
  // );
  // console.log("valid", isValid);

  return (
    <SafeAreaView style={styles.container} edges={["bottom", "left", "right"]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 0}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.safeContainer}>
            {signupComplete ? (
              <View style={styles.signupCompleteError}>
                <Text style={[styles.boldHeaderText, { marginBottom: 40 }]}>
                  Sign Up Complete!
                </Text>
                <Text style={{ ...styles.boldMainText, marginVertical: 10 }}>
                  Click below to login and start shopping:
                </Text>
                <Text style={styles.boldMainText}>
                  Username: {userInfo.user_name}
                </Text>
                <Text style={styles.boldMainText}>
                  Password: {userInfo.password}
                </Text>
                <TouchableOpacity style={styles.backBtn} onPress={signIn}>
                  <Text style={styles.backBtnText}>Start Shopping Now</Text>
                </TouchableOpacity>
              </View>
            ) : signupError ? (
              <View style={styles.signupCompleteError}>
                <Text style={styles.boldHeaderText}>
                  Sign Up Error Occurred
                </Text>
                <Text style={styles.boldMainText}>
                  Whoopsies! Something went wrong with your sign up. Please
                  reach out to us in order to complete the signup process.
                </Text>
              </View>
            ) : (
              <MultiSteps
                containerStyle={styles.multistepContainer}
                onMoveNext={handleMoveNext}
                onMovePrevious={handleMovePrevious}
                onSubmit={handleSubmit(onSubmit)}
                blockNext={
                  inputFields[currentStep - 1]
                    .map((field) => errors[field])
                    .filter((error) => error).length > 0 || !isValid
                }
              >
                {steps}
              </MultiSteps>
            )}
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
