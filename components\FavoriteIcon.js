import { TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@react-navigation/native";

const FavoriteIcon = ({
  isFavorited,
  onPress,
  size = 24,
  style,
  isModal = false,
}) => {
  const theme = useTheme();

  return (
    <TouchableOpacity onPress={onPress} style={[styles.container, style]}>
      <Ionicons
        name={isFavorited ? "heart" : "heart-outline"}
        size={size}
        color={
          isFavorited
            ? theme.appColors.redText
            : isModal
            ? theme.appColors.primary
            : theme.appColors.surface
        }
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {},
});

export default FavoriteIcon;
