import {
  View,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import PropTypes from "prop-types";
import useGlobalStyles from "../lib/useGlobalStyles";
import React, { useContext, useMemo } from "react";
import { useTheme, useNavigation } from "@react-navigation/native";
import { UserContext } from "../context/userContext";
import { posthog } from "../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({});
  return styles;
};
const Spotlights = (props) => {
  const { user } = useContext(UserContext);

  const theme = useTheme();
  const navigation = useNavigation();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();
  return (
    <View
      style={{ paddingLeft: 20, paddingBottom: 16, backgroundColor: "white" }}
    >
      <ScrollView style={globalStyles.scrollViewHorizontal} horizontal={true}>
        {props.spotlights.map((spotlight) => {
          return (
            <TouchableOpacity
              onPress={() => {
                posthog.capture("supplier_spotlight_card_clicked", {
                  supplier: spotlight.name,
                });
                !user.supplier_beta
                  ? navigation.navigate("BrowseSupplier", {
                      supplier: spotlight,
                    })
                  : // : navigation.navigate("SelectItems", {
                    //     supplier: user.suppliers[0].name,
                    //     defaultQuery: spotlight.name,
                    //   });
                    navigation.navigate("SelectItems", {
                      brand: spotlight.name,
                      title: spotlight.name,
                    });
              }}
              key={spotlight.id}
            >
              <Image
                source={{ uri: spotlight.spotlight_image }}
                style={{ width: 327, height: 150 }}
              ></Image>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

Spotlights.propTypes = {
  spotlights: PropTypes.array.isRequired,
};

export default Spotlights;
