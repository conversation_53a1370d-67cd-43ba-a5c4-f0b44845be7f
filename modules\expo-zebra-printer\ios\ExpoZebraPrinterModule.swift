import ExpoModulesCore
import Foundation
import ExternalAccessory
import CoreBluetooth

public class ExpoZebraPrinterModule: Module {
    private var centralManagerDelegate: CentralManagerDelegate!
    private var peripheralDelegate: PeripheralDelegate!
    private var centralManager: CBCentralManager!
    private var externalAccessoryManager: EAAccessoryManager!
    private var selectedDevice: CBPeripheral?
    private var selectedPrinterSerial: String?
    private var selectedPrinter: ZebraPrinter?
    private var selectedPrinterConnection: ZebraPrinterConnection?
    private var writeCharacteristic: CBCharacteristic?
    private var discoveredDevices: [String: CBPeripheral] = [:]
    private var scanCompletion: (([[String: Any]]) -> Void)?
    private var connectCompletion: ((Result<String, Error>) -> Void)?
    private var scanTimer: DispatchSourceTimer?
    private var connectionRetryCount = 0
    private let maxRetries = 3
    private var printerDisconnected = true
    private var previouslyConnectedAccessories: [EAAccessory] = []
    public var isDiscoveringServices = false
    
    
    // Each module class must implement the definition function. The definition consists of components
    // that describes the module's functionality and behavior.
    // See https://docs.expo.dev/modules/module-api for more details about available components.
    public func definition() -> ModuleDefinition {
        // Sets the name of the module that JavaScript code will use to refer to the module. Takes a string as an argument.
        // Can be inferred from module's class name, but it's recommended to set it explicitly for clarity.
        // The module will be accessible from `requireNativeModule('ExpoZebraPrinter')` in JavaScript.
        Name("ExpoZebraPrinter")
        
        OnCreate {
            // Set up managers and delegates first
            centralManagerDelegate = CentralManagerDelegate(module: self)
            peripheralDelegate = PeripheralDelegate(module: self)
            externalAccessoryManager = EAAccessoryManager.shared()
            centralManager = CBCentralManager(delegate: centralManagerDelegate, queue: nil)
            
            // Start the accessory manager notifications
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(self.accessoryManagerDidUpdateConnectedAccessories(_:)),
                name: NSNotification.Name.EAAccessoryDidConnect,
                object: nil
            )
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(self.accessoryManagerDidUpdateConnectedAccessories(_:)),
                name: NSNotification.Name.EAAccessoryDidDisconnect,
                object: nil
            )
            
            // Add this line to register for accessory notifications
            EAAccessoryManager.shared().registerForLocalNotifications()
        }
        
        OnDestroy {
            NotificationCenter.default.removeObserver(self)
        }

        Function("isBluetoothEnabled") { () -> Bool in
            return self.isBluetoothEnabled()
        }
        
        AsyncFunction("getDiscoveredDevices") { (promise: Promise) in
            Task {
                if centralManager.state != .poweredOn {
                    promise.reject("ERROR", "Bluetooth is not enabled")
                    return
                }
                if centralManager.isScanning {
                    centralManager.stopScan()
                }
                do {
                    let devices = try await self.getDiscoveredDevices()
                    promise.resolve(devices)
                } catch {
                    promise.reject("ERROR", "Failed to discover devices: \(error.localizedDescription)")
                }
            }
        }

        AsyncFunction("getDiscoveredExternalAccessories") { (promise: Promise) in
            Task {
                do {
                    let accessories = try await self.getDiscoveredExternalAccessories()
                    promise.resolve(accessories)
                } catch {
                    promise.reject("ERROR", "Failed to discover external accessories: \(error.localizedDescription)")
                }
            }
        }

        AsyncFunction("connect") { (deviceUUID: String, promise: Promise) in
            Task {
                if centralManager.state != .poweredOn {
                    promise.reject("ERROR", "Bluetooth is not enabled")
                    return
                }
                do {
                    let connectedUUID = try await self.connect(uuid: deviceUUID)
                    promise.resolve(connectedUUID)
                } catch {
                    promise.reject("ERROR", "Failed to connect to the device with UUID \(deviceUUID): \(error.localizedDescription)")
                }
            }
        }
        
        AsyncFunction("connectAccessory") { (accessoryID: String, force: Bool, promise: Promise) in
            Task {
                do {
                    try self.connectAccessory(withID: accessoryID, force: force)
                    promise.resolve(true)
                } catch {
                    promise.reject("ERROR", "Failed to connect to accessory: \(error.localizedDescription)")
                }
            }
        }

        AsyncFunction("sendZPL") { (zpl: String, promise: Promise) in
            Task {
                do {
                    try self.sendZPL(zpl: zpl)
                    promise.resolve(true)
                } catch {
                    promise.reject("ERROR", "Failed to send ZPL: \(error.localizedDescription)")
                }
            }
        }

        AsyncFunction("sendZPLToAccessory") { (zpl: String, promise: Promise) in
            Task {
                do {
                    try self.sendZPLToAccessory(zpl: zpl)
                    promise.resolve(true)
                } catch {
                    promise.reject("ERROR", "Failed to send ZPL to Accessory: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func isBluetoothEnabled() -> Bool {
        return centralManager.state == .poweredOn
    }
    
    private func getDiscoveredDevices() async throws -> [[String: Any]] {
        print("getDiscoveredDevices called!")
        
        if (scanTimer != nil) {
            print("getDiscoveredDevices triggered again, ending scan early!")
            finishScanning()
        }

        // Clear existing discovered devices before starting new scan
        discoveredDevices.removeAll()

        // Get all connected peripherals with the Zebra printer service UUID
        let connectedPrinters = centralManager.retrieveConnectedPeripherals(withServices: [CBUUID(string: ZPRINTER_SERVICE_UUID)])
        for printer in connectedPrinters {
            addDiscoveredDevice(printer)
        }
        print("Found \(connectedPrinters.count) connected printers!")
        print(connectedPrinters)

        return try await withCheckedThrowingContinuation { continuation in
            print("getDiscoveredDevices inside called!")
            
            self.scanCompletion = { printers in
                continuation.resume(returning: printers)
            }
            
            // Start scanning for peripherals
            centralManager.scanForPeripherals(withServices: nil, options: [CBCentralManagerScanOptionAllowDuplicatesKey: false])
            
            // Set up a timer to stop the scan after 30 seconds
            scanTimer = DispatchSource.makeTimerSource()
            scanTimer?.schedule(deadline: .now() + 30) // 30 seconds
            scanTimer?.setEventHandler { [weak self] in
                self?.finishScanning()
            }
            scanTimer?.resume()
        }
    }

    // New method for discovering external accessories
    private func getDiscoveredExternalAccessories() async throws -> [[String: Any]] {
        print("getDiscoveredExternalAccessories called!")

        let accessories = externalAccessoryManager.connectedAccessories
        print("Found \(accessories.count) connected external accessories!")

        return accessories.map { accessory in
            return ["id": accessory.serialNumber, "name": accessory.name, "connected": accessory.serialNumber == self.selectedPrinterSerial]
        }
    }
    
    private func connect(uuid: String) async throws -> String {
        guard centralManager.state == .poweredOn else {
            print("Central manager not powered on: \(centralManager.state.rawValue)")
            throw NSError(domain: "ExpoZebraPrinter", code: 1,
                userInfo: ["message": "Bluetooth is not ready (state: \(centralManager.state.rawValue))"])
        }

        // First check for currently connected devices
        let connectedPrinters = centralManager.retrieveConnectedPeripherals(withServices: [CBUUID(string: ZPRINTER_SERVICE_UUID)])
        if let existingDevice = connectedPrinters.first(where: { $0.identifier.uuidString == uuid }) {
            print("Found currently connected device with UUID \(uuid)")
            // If device is already connected and ready, return immediately
            if existingDevice.state == .connected {
                // Ensure we have the proper delegate set
                existingDevice.delegate = peripheralDelegate
                self.selectedDevice = existingDevice
                
                // If services are already discovered, return success immediately
                if existingDevice.services?.contains(where: { $0.uuid == CBUUID(string: ZPRINTER_SERVICE_UUID) }) == true {
                    print("Device already connected and services discovered")
                    return existingDevice.identifier.uuidString
                }
                
                // If services aren't discovered yet, we need to discover them
                print("Device connected but needs service discovery")
                connectionRetryCount = 0
                return try await attemptConnection(device: existingDevice)
            }
        }

        // Then check for known/paired devices
        if let knownDeviceUUID = UUID(uuidString: uuid) {
            let knownDevices = centralManager.retrievePeripherals(withIdentifiers: [knownDeviceUUID])
            if let pairedDevice = knownDevices.first {
                print("Found paired device with UUID \(uuid)")
                connectionRetryCount = 0
                return try await attemptConnection(device: pairedDevice)
            }
        }

        // Finally check discovered devices from recent scan
        let foundDevice = discoveredDevices[uuid]
        if foundDevice == nil {
            print("Didn't find device with UUID \(uuid) in connected, paired, or discovered devices.")
            throw NSError(domain: "ExpoZebraPrinter", code: 1, 
                userInfo: ["message": "Device with UUID \(uuid) not found."])
        }
        
        connectionRetryCount = 0
        return try await attemptConnection(device: foundDevice!)
    }

    private func connectAccessory(withID accessoryID: String, force: Bool?) throws {
        print("connectAccessory(withID: \(accessoryID), force: \(String(describing: force)))")
        if (selectedPrinterSerial == accessoryID && (force != nil && !force!)) {
            print("Accessory with ID \(accessoryID) already connected!")
            return
        }
        let accessories = externalAccessoryManager.connectedAccessories
        self.previouslyConnectedAccessories = accessories
        guard let accessory = accessories.first(where: { $0.serialNumber == accessoryID }) else {
            print("Accessory with ID \(accessoryID) not found.")
            throw NSError(domain: "ExpoZebraPrinter", code: 1, userInfo: ["message": "Accessory with ID \(accessoryID) not found."])
        }

        print("Connecting to accessory: \(accessory.name)")

        // Instantiate the MfiBtPrinterConnection using the provided serial number
        let printerConnection = MfiBtPrinterConnection(serialNumber: accessoryID)
        printerConnection?.setMaxTimeoutForRead(5000)

        // Attempt to open the connection
        guard printerConnection!.open() else {
            throw NSError(domain: "ExpoZebraPrinter", code: 3, userInfo: ["message": "Failed to open connection with Accessory ID \(accessoryID)."])
        }
        
        do {
            self.selectedPrinter = try ZebraPrinterFactory.getInstance(printerConnection)
            self.selectedPrinterConnection = printerConnection
            self.selectedPrinterSerial = accessoryID
            self.printerDisconnected = false
            if self.selectedPrinter!.getControlLanguage() != PRINTER_LANGUAGE_ZPL {
                try SGD.set("device.languages", withValue: "zpl", andWithPrinterConnection: printerConnection)
                print("Successfully changed the printer language to \(try SGD.get("device.languages", withPrinterConnection: printerConnection))")
            }
            print("Printer Width: \(try SGD.get("ezpl.print_width", withPrinterConnection: printerConnection))")
            
        } catch let error as NSError {
            throw NSError(domain: "ExpoZebraPrinter", code: 3, userInfo: ["message": "Failed to set printer to \(accessoryID): \(error.localizedDescription)."])
        }
    }
    
    @objc
    func accessoryManagerDidUpdateConnectedAccessories(_ notification: Notification) {
        guard let selectedSerial = self.selectedPrinterSerial else {
            print("No printer was previously selected, skipping reconnection.")
            return
        }

        let currentConnectedAccessories = externalAccessoryManager.connectedAccessories
        
        // Check if our selected printer was disconnected
        if !currentConnectedAccessories.contains(where: { $0.serialNumber == selectedSerial }) {
            self.selectedPrinterConnection?.close()
            self.printerDisconnected = true
            self.previouslyConnectedAccessories = currentConnectedAccessories
            print("Previously connected printer was disconnected.")
            return
        }
        
        // Check if our selected printer was newly connected
        let isNewlyConnected = currentConnectedAccessories.contains(where: { $0.serialNumber == selectedSerial }) &&
            !self.previouslyConnectedAccessories.contains(where: { $0.serialNumber == selectedSerial })
        
        // Update our cached list of accessories
        self.previouslyConnectedAccessories = currentConnectedAccessories
        
        if isNewlyConnected {
            // Perform connection asynchronously
            Task {
                do {
                    try await Task { try self.connectAccessory(withID: selectedSerial, force: true) }.value
                } catch let error as NSError {
                    print("Error re-connecting to printer: \(error.localizedDescription)")
                }
            }
        } else {
            print("Previously connected printer wasn't newly connected. Skipping reconnection.")
        }
    }

    private func sendZPL(zpl: String) throws -> Void {
        let zplWithCarriageReturn = zpl + "\r\n"
        print("sendZPL called")
        // print("sendZPL called with: \(zpl)")
        if selectedDevice == nil {
            print("No device selected!")
            throw NSError(domain: "ExpoZebraPrinter", code: 1, 
                userInfo: ["message": "No device selected!"])
        }
        if writeCharacteristic == nil {
            print("No write characteristic found!")
            throw NSError(domain: "ExpoZebraPrinter", code: 1, 
                userInfo: ["message": "No write characteristic found!"])
        }
        
        print("Selected device: \(selectedDevice!.identifier.uuidString), writeCharacteristic: \(writeCharacteristic!.uuid.uuidString)")
        
        // Convert the string to data
        guard let data = zplWithCarriageReturn.data(using: .utf8) else {
            throw NSError(domain: "ExpoZebraPrinter", code: 1,
                userInfo: ["message": "Failed to convert ZPL to data"])
        }
        
        // Send data in chunks
        let chunkSize = 50
        var offset = 0
        
        while offset < data.count {
            let length = min(chunkSize, data.count - offset)
            let chunk = data.subdata(in: offset..<(offset + length))
            selectedDevice!.writeValue(chunk, for: writeCharacteristic!, type: .withResponse)
            offset += length
        }
    }
    
    private func sendZPLToAccessory(zpl: String) throws -> Void {
        let zplWithCarriageReturn = zpl + "\r\n"
        print("sendZPLToAccessory called")
        // print("sendZPLToAccessory called with: \(zpl)")
        if selectedPrinter == nil {
            print("Selected Printer is nil.")
            throw NSError(domain: "ExpoZebraPrinter", code: 1,
                userInfo: ["message": "No printer selected!"])
        }
        if selectedPrinterConnection == nil {
            print("Selected Printer Connection is nil")
            throw NSError(domain: "ExpoZebraPrinter", code: 3,
                userInfo: ["message": "Printer selected but connection not found!"])
        }
        var error: NSError?
        let bytesWritten = selectedPrinterConnection!.write(zplWithCarriageReturn.data(using: .utf8)!, error: &error)
        if bytesWritten == -1 {
            throw NSError(domain: "ExpoZebraPrinter", code: 3, userInfo: ["message": "Failed to send ZPL data to the printer: \(String(describing: error?.localizedDescription))"])
        }
    }
    
    func addDiscoveredDevice(_ peripheral: CBPeripheral) {
        discoveredDevices[peripheral.identifier.uuidString] = peripheral
    }

    private func attemptConnection(device: CBPeripheral) async throws -> String {
        print("Attempting connection (attempt \(connectionRetryCount + 1) of \(maxRetries + 1))")
        print("Device state: \(device.state.rawValue)")
        
        // Store strong reference
        self.selectedDevice = device
        self.selectedDevice!.delegate = peripheralDelegate
        
        return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<String, Error>) in
            // Set up a timeout
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
                guard let self = self else { return }
                
                if self.selectedDevice?.state != .connected {
                    print("Connection attempt \(self.connectionRetryCount + 1) timed out")
                    
                    if self.connectionRetryCount < self.maxRetries {
                        self.connectionRetryCount += 1
                        
                        // Disconnect before retrying
                        if self.selectedDevice?.state != .disconnected {
                            self.centralManager.cancelPeripheralConnection(self.selectedDevice!)
                        }
                        
                        // Wait a bit before retrying
                        Task {
                            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
                            do {
                                let result = try await self.attemptConnection(device: device)
                                continuation.resume(returning: result)
                            } catch {
                                continuation.resume(throwing: error)
                            }
                        }
                    } else {
                        print("All connection attempts failed")
                        continuation.resume(throwing: NSError(domain: "ExpoZebraPrinter", code: 3,
                            userInfo: ["message": "Failed to connect after \(self.maxRetries + 1) attempts"]))
                    }
                }
            }
            
            self.connectCompletion = { result in
                switch result {
                case .success(let value):
                    print("Successfully connected to device with UUID \(value)!")
                    continuation.resume(returning: value)
                case .failure(let error):
                    print("Failed to connect to device: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                }
            }
            
            let options: [String: Any] = [
                CBConnectPeripheralOptionNotifyOnConnectionKey: true,
                CBConnectPeripheralOptionNotifyOnDisconnectionKey: true,
                CBConnectPeripheralOptionStartDelayKey: 1
            ]
            
            print("Initiating connection attempt \(self.connectionRetryCount + 1)...")
            self.centralManager.connect(self.selectedDevice!, options: options)
        }
    }

    func confirmCharacteristics(peripheral: CBPeripheral) {
        // Prevent multiple simultaneous service discovery attempts
        guard !isDiscoveringServices else {
            print("Service discovery already in progress, skipping...")
            return
        }
        
        print("confirmCharacteristics called for \(peripheral.identifier.uuidString)!")
        isDiscoveringServices = true
        peripheral.discoverServices([CBUUID(string: ZPRINTER_SERVICE_UUID)])
    }

    func setWriteCharacteristic(characteristic: CBCharacteristic) {
        writeCharacteristic = characteristic
    }
    
    func finishScanning() {
        // Stop the scan and clear the timer
        centralManager.stopScan()
        scanTimer?.cancel() // Cancel the timer
        scanTimer = nil
        
        let deviceInfoArray = discoveredDevices.map { (uuid, peripheral) in
            return ["id": uuid, "name": peripheral.name as Any, "connected": peripheral.state == .connected]
        }
        
        scanCompletion?(deviceInfoArray)
    }

    func finishConnecting(peripheral: CBPeripheral, error: Error? = nil) {
        // Reset the discovering services flag
        isDiscoveringServices = false
        
        if let error = error {
            print("Connection failed with error: \(error.localizedDescription)")
            connectCompletion?(.failure(error))
        } else {
            print("Connection and characteristic discovery completed successfully!")
            connectCompletion?(.success(peripheral.identifier.uuidString))
        }
        
        // Clear the completion handler after using it
        connectCompletion = nil
    }
}

class CentralManagerDelegate: NSObject, CBCentralManagerDelegate {
    private var module: ExpoZebraPrinterModule

    init(module: ExpoZebraPrinterModule) {
        self.module = module
    }

    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        print("centralManagerDidUpdateState called!")
        switch central.state {
        case .poweredOff:
            print("Bluetooth is off!")
            module.finishScanning()
        case .unauthorized, .unknown, .resetting, .unsupported:
            print("Other Bluetooth state detected! \(central.state.rawValue)")
        case .poweredOn:
            print("Bluetooth is on!")
        @unknown default:
            module.finishScanning()
        }
    }

    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String: Any], rssi RSSI: NSNumber) {
        print("Found device \(String(describing: peripheral.name))! [\(peripheral.identifier.uuidString)]: \(peripheral)")
        module.addDiscoveredDevice(peripheral)
    }

    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        print("didConnect called for \(peripheral.identifier.uuidString)!")
        print("Peripheral state after connection: \(peripheral.state.rawValue)")
        module.confirmCharacteristics(peripheral: peripheral)
    }

    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("didFailToConnect called for \(peripheral.identifier.uuidString)!")
        print("Error: \(String(describing: error))")
        print("Peripheral state: \(peripheral.state.rawValue)")
        module.finishConnecting(peripheral: peripheral, error: error)
    }
}

class PeripheralDelegate: NSObject, CBPeripheralDelegate {
    private var module: ExpoZebraPrinterModule
    
    init(module: ExpoZebraPrinterModule) {
        self.module = module
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        // Reset the discovering services flag regardless of outcome
        module.isDiscoveringServices = false
        
        if let error = error {
            print("Error discovering services: \(error.localizedDescription)")
            module.finishConnecting(peripheral: peripheral, error: error)
            return
        }
        
        guard let services = peripheral.services else {
            module.finishConnecting(peripheral: peripheral, 
                error: NSError(domain: "ExpoZebraPrinter", code: 2, userInfo: ["message": "No services found"]))
            return
        }
        
        for service in services {
            if service.uuid == CBUUID(string: ZPRINTER_SERVICE_UUID) {
                peripheral.discoverCharacteristics(
                    [
                        CBUUID(string: WRITE_TO_ZPRINTER_CHARACTERISTIC_UUID),
                        CBUUID(string: READ_FROM_ZPRINTER_CHARACTERISTIC_UUID)
                    ],
                    for: service
                )
                return
            }
        }
        
        module.finishConnecting(peripheral: peripheral, 
            error: NSError(domain: "ExpoZebraPrinter", code: 2, userInfo: ["message": "Required service not found"]))
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            print("Error discovering characteristics: \(error.localizedDescription)")
            module.finishConnecting(peripheral: peripheral, error: error)
            return
        }
        
        guard let characteristics = service.characteristics else {
            module.finishConnecting(peripheral: peripheral, 
                error: NSError(domain: "ExpoZebraPrinter", code: 2, userInfo: ["message": "No characteristics found"]))
            return
        }
        
        var foundWrite = false
        var foundRead = false
        
        for characteristic in characteristics {
            if characteristic.uuid == CBUUID(string: WRITE_TO_ZPRINTER_CHARACTERISTIC_UUID) {
                foundWrite = true
            }
            if characteristic.uuid == CBUUID(string: READ_FROM_ZPRINTER_CHARACTERISTIC_UUID) {
                foundRead = true
            }
        }
        
        if foundWrite && foundRead {
            // Successfully found all required characteristics
            let writeCharacteristic = characteristics.first(where: { $0.uuid == CBUUID(string: WRITE_TO_ZPRINTER_CHARACTERISTIC_UUID) })
            module.setWriteCharacteristic(characteristic: writeCharacteristic!)
            module.finishConnecting(peripheral: peripheral)
        } else {
            module.finishConnecting(peripheral: peripheral, 
                error: NSError(domain: "ExpoZebraPrinter", code: 2, userInfo: ["message": "Required characteristics not found"]))
        }
    }

    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        print("didWriteValueFor called for peripheral \(peripheral.identifier.uuidString), characteristic \(characteristic.uuid.uuidString)!")
        if let error = error {
            print("Error writing value: \(error.localizedDescription)")
            print(error)
        }
    }
}
