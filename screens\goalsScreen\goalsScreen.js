import { useContext } from "react";
import { StyleSheet, View, ScrollView, StatusBar } from "react-native";
import { useTheme } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import Svg, { Circle } from "react-native-svg";

import SafeAreaView from "../../components/SafeAreaView";
import Text from "../../components/Text";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import { UserContext } from "../../context/userContext";
import {
  useGetUserGoalsQuery,
  Ordering,
  GoalStatus,
} from "../../generated/graphql";
import {
  formatCurrency,
  formatGoalType,
  formatPeriod,
  getBadgeColor,
  getProgressColor,
} from "../../lib/goalsUtils";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.appColors.backdropNeutral,
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    content: {
      flex: 1,
      paddingHorizontal: 15,
      paddingTop: 20,
    },
    emptyState: {
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 40,
      paddingVertical: 60,
    },
    emptyStateIcon: {
      fontSize: 60,
      marginBottom: 20,
      opacity: 0.3,
    },
    emptyStateText: {
      color: "#666",
      fontSize: 16,
      lineHeight: 22,
      textAlign: "center",
    },
    emptyStateTitle: {
      color: "#1a1a1a",
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 8,
      textAlign: "center",
    },
    goalCard: {
      backgroundColor: "white",
      borderRadius: 16,
      elevation: 5,
      marginBottom: 10,
      padding: 12,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },
    goalHeader: {
      alignItems: "flex-start",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 0,
    },
    goalInfo: {
      flex: 1,
    },
    goalMeta: {
      color: "#666",
      fontSize: 12,
    },
    goalTitle: {
      color: "#1a1a1a",
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 4,
    },
    progressAmount: {
      color: "#1a1a1a",
      fontSize: 20,
      fontWeight: "700",
      textAlign: "center",
    },
    progressBadge: {
      alignSelf: "flex-start",
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    progressBadgeText: {
      fontSize: 12,
      fontWeight: "600",
    },
    progressSection: {
      alignItems: "center",
      marginTop: 5,
    },
    progressTarget: {
      color: "#666",
      fontSize: 14,
      marginBottom: 8,
      textAlign: "center",
    },
  });
  return styles;
};

const CircularProgress = ({
  percentage,
  size = 120,
  strokeWidth = 8,
  color = "#4285f4",
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <View style={{ width: size, height: size, position: "relative" }}>
      <Svg
        width={size}
        height={size}
        style={{ transform: [{ rotateZ: "-90deg" }] }}
      >
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#f0f0f0"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
        />
      </Svg>
      <View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Text style={{ fontSize: 20, fontWeight: "700", color: "#1a1a1a" }}>
          {Math.round(percentage)}%
        </Text>
      </View>
    </View>
  );
};

const GoalsScreenComponent = () => {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);

  const supplier = user.suppliers[0];
  const employeeId = user.id;

  const { loading, data, error, refetch } = useGetUserGoalsQuery({
    variables: {
      goalsInput: {
        filters: {
          supplier_id: supplier.id,
          employee_id: employeeId,
          status: GoalStatus.Active,
        },
        pagination: { offset: 0, limit: 50 },
        sortBy: { field: "target_amount", ordering: Ordering.Desc },
        goal_period_selections: [],
      },
    },
    fetchPolicy: "cache-and-network",
  });

  const renderGoalCard = (goal) => {
    const current_period = goal.available_periods[0].label;

    let userAssignment = null;

    if (goal.assignments && goal.assignments.length > 0) {
      userAssignment = goal.assignments.find(
        (assignment) => assignment.employee_id === employeeId
      );

      if (!userAssignment) {
        userAssignment = goal.assignments.find(
          (assignment) =>
            !assignment.employee_id || assignment.employee_id === null
        );
      }

      if (!userAssignment) {
        userAssignment = goal.assignments[0];
      }
    }

    if (!userAssignment) {
      userAssignment = {
        target_amount: goal.target_amount,
        current_progress: 0,
        percentage_complete: 0,
      };
    }

    const progress = {
      percentage: userAssignment.percentage_complete || 0,
      current: userAssignment.current_progress || 0,
      target: userAssignment.target_amount || goal.target_amount,
    };

    const badgeColors = getBadgeColor(progress.percentage);
    const progressColor = getProgressColor(progress.percentage);

    return (
      <View key={goal.id} style={styles.goalCard}>
        <View style={styles.goalHeader}>
          <View style={styles.goalInfo}>
            <Text style={styles.goalTitle}>{goal.name}</Text>
            <Text style={styles.goalMeta}>
              {formatGoalType(goal.type)} • {formatPeriod(goal.period)}
            </Text>
          </View>
          <View
            style={[
              styles.progressBadge,
              { backgroundColor: badgeColors.backgroundColor },
            ]}
          >
            <Text
              style={[styles.progressBadgeText, { color: badgeColors.color }]}
            >
              {current_period}
            </Text>
          </View>
        </View>

        <View style={styles.progressSection}>
          <CircularProgress
            percentage={progress.percentage}
            color={progressColor}
            size={80}
            strokeWidth={6}
          />

          <Text style={styles.progressAmount}>
            {goal.type === "SALES_AMOUNT"
              ? formatCurrency(progress.current)
              : Math.round(progress.current).toLocaleString()}
            <Text style={styles.progressTarget}>
              {" "}
              of{" "}
              {goal.type === "SALES_AMOUNT"
                ? formatCurrency(progress.target)
                : Math.round(progress.target).toLocaleString()}
            </Text>
          </Text>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>🎯</Text>
      <Text style={styles.emptyStateTitle}>No Active Goals</Text>
      <Text style={styles.emptyStateText}>
        You don't have any active goals at the moment. Check back later or
        contact your manager for goal assignments.
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingErrorStatus message="Loading goals..." errorStatus={false} />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingErrorStatus
          message={`Error loading goals: ${error.message}`}
          errorStatus={true}
          retryFunction={refetch}
        />
      </SafeAreaView>
    );
  }

  const goals = data?.goals?.goals || [];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {goals.length > 0 ? goals.map(renderGoalCard) : renderEmptyState()}
      </ScrollView>
    </SafeAreaView>
  );
};

const GoalsStack = createStackNavigator();

export default function GoalsScreen() {
  return (
    <GoalsStack.Navigator>
      <GoalsStack.Screen
        name="GoalsScreen"
        component={GoalsScreenComponent}
        options={{ headerShown: false, title: "Goals" }}
      />
    </GoalsStack.Navigator>
  );
}
