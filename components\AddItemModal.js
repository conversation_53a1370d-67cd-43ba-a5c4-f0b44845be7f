import { useState, useContext, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import PropTypes from "prop-types";
import Text from "./Text";
import NumericInput from "./NumericInput";
import { useUpdateItemInCartMutation } from "../generated/graphql";
import { UserContext } from "../context/userContext";
import { Ionicons } from "@expo/vector-icons";
import Button from "./Button";
import useGlobalStyles from "../lib/useGlobalStyles";
import { getPrice } from "../lib/getPrice";
import { discountExists } from "../lib/discountExists";
import { useTheme } from "@react-navigation/native";
import { useTranslation } from "react-i18next";
import {
  getUomOptions,
  getUomPrice,
  getUomQuantity,
  getUomName,
  hasUoms,
} from "../lib/uomHelpers";
import FavoriteIcon from "./FavoriteIcon";
import { useFavorite } from "../hooks/useFavorite";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
    },
    container: {
      bottom: 0,
      position: "absolute",
    },
    disabledAppearance: {
      backgroundColor: theme.appColors.disabled,
      opacity: 0.5,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginBottom: 30,
    },
    itemDetailLine: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 120,
      width: 120,
    },
    itemTextContainer: {
      flex: 2,
      marginLeft: 20,
    },
    keyboardContainer: {
      flex: 1,
    },
    originalPriceText: {
      color: theme.colors.redText,
    },
    quantityContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      marginBottom: 30,
    },
    uomSelectorContainer: {
      marginBottom: 20,
    },
    uomSelector: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 15,
      paddingHorizontal: 0,
      borderTopWidth: 1,
      borderTopColor: theme.appColors.backdropDark,
    },
    uomOptionsContainer: {
      marginTop: 10,
      paddingVertical: 10,
    },
    uomOptionsContent: {
      paddingHorizontal: 0,
    },
    uomOption: {
      paddingHorizontal: 15,
      paddingVertical: 10,
      marginRight: 10,
      borderWidth: 1,
      borderColor: theme.appColors.backdropDark,
      borderRadius: 8,
      backgroundColor: "white",
    },
    selectedUomOption: {
      backgroundColor: theme.appColors.primary,
      borderColor: theme.appColors.primary,
    },
    selectedUomText: {
      color: "white",
    },
    safeContainer: {
      backgroundColor: "white",
      borderRadius: 20,
      paddingBottom: 50,
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    scrollContainer: {
      flexGrow: 1,
    },
  });
  return styles;
};

const AddItemModal = ({ item, navigation, onClose }) => {
  const { user } = useContext(UserContext);
  const supplier_id = user.suppliers[0].id;
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const supplier = user.suppliers.find(
    (supplier) => supplier.name === item.supplier
  );
  const [updateItemInCart] = useUpdateItemInCartMutation();

  const { isFavorited, toggleFavorite, isToggling } = useFavorite(
    item,
    user.id
  );

  const onEditQuantity = (value) => {
    setQuantity(value);
  };
  const [quantity, setQuantity] = useState(1);

  // UOM state
  const [selectedUomId, setSelectedUomId] = useState("default");
  const [uomDropdownOpen, setUomDropdownOpen] = useState(false);

  const cart = user.cart;

  const getItemQuantityInCart = () => {
    // Find cart item with matching item_id AND UOM
    const checkItemIdAndUom = (cartItem) =>
      cartItem.item_id == item.id &&
      cartItem.item_uom_id ===
        (selectedUomId === "default" ? null : selectedUomId);

    if (cart.cartItems.some(checkItemIdAndUom)) {
      const cartItem = cart.cartItems.find(checkItemIdAndUom);
      // Calculate UOM-adjusted display quantity
      const uomQuantity = cartItem?.item_uom_id
        ? getUomQuantity(cartItem, cartItem.item_uom_id)
        : 1;
      const displayQuantity = Number(
        (cartItem.quantity / uomQuantity).toFixed(2)
      );
      return displayQuantity;
    }
    return 0;
  };

  useEffect(() => {
    const itemQuantity = getItemQuantityInCart();
    if (itemQuantity > 0) {
      setQuantity(itemQuantity);
    } else {
      setQuantity(1); // Reset to 1 if no matching UOM in cart
    }
  }, [cart, item.id, selectedUomId]);

  // Get UOM-specific pricing and info
  const uomOptions = getUomOptions(item, user);
  const item_price = getUomPrice(item, user, selectedUomId);
  const itemHasUoms = hasUoms(item);
  const selectedUomName = getUomName(item, selectedUomId);

  const onAddToCartPressed = () => {
    const uomQuantityMultiplier = getUomQuantity(item, selectedUomId);
    const backendQuantity = quantity * uomQuantityMultiplier;

    // Only add existing quantity if it's the same UOM (separate entries for different UOMs)
    var quantityToBeUpdated = backendQuantity;
    quantityToBeUpdated += getItemQuantityInCart(); // This now only finds same UOM

    updateItemInCart({
      variables: {
        updateItemInCartInput: {
          cartId: cart.id,
          itemId: item.id,
          quantity: quantityToBeUpdated,
          itemUomId: selectedUomId === "default" ? null : selectedUomId,
          userId: user.id,
          supplierId: supplier_id,
        },
      },
    });
    onClose();
    // navigation.goBack()
  };

  const viewCartButtonPressed = () => {
    navigation.navigate("Cart");
  };

  const itemOOS = () => {
    if (typeof item.oos === "boolean" && item.oos) {
      return true;
    }
    if (typeof item.oos === "string" && item.oos === "TRUE") {
      return true;
    }

    return false;
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"} // Adjust behavior for iOS vs Android
      style={styles.keyboardContainer}
      keyboardVerticalOffset={20}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.container}>
          <View style={styles.safeContainer}>
            <View
              style={{
                width: "100%",
                marginBottom: 10,
                flexDirection: "row",
                justifyContent: "flex-end",
              }}
            >
              <FavoriteIcon
                isFavorited={isFavorited}
                onPress={toggleFavorite}
                size={33}
                isModal={true}
                style={{ opacity: isToggling ? 0.5 : 1 }}
              />
              <TouchableOpacity onPress={onClose}>
                <Ionicons
                  name="close"
                  size={35}
                  color={theme.appColors.primary}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.itemContainer}>
              <Image
                style={styles.itemImage}
                source={{
                  uri:
                    item.image ||
                    supplier?.config?.defaultItemImage ||
                    "https://fakeimg.pl/100x100?text=no+image",
                }}
              />
              <View style={styles.itemTextContainer}>
                <Text
                  style={[
                    globalStyles.headlineMedium,
                    globalStyles.text,
                    { marginBottom: 8 },
                  ]}
                >
                  {item.name}
                </Text>

                {Boolean(item.unit_size) && (
                  <View style={styles.itemDetailLine}>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      Unit Size
                    </Text>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      {item.unit_size} ct
                    </Text>
                  </View>
                )}
                {user.show_prices && user.doesnt_use_uom ? (
                  <View style={styles.itemDetailLine}>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      Price
                    </Text>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      ${parseFloat(item_price).toFixed(2)}
                    </Text>
                  </View>
                ) : null}
                {item.avg_cases_per_week !== undefined &&
                  item.avg_cases_per_week !== null && (
                    <View style={styles.itemDetailLine}>
                      <Text
                        style={[globalStyles.bodyLarge, globalStyles.textLight]}
                      >
                        {supplier_id == 31
                          ? "Average Cases Per Week"
                          : "Average Cases Per Order"}
                      </Text>
                      <Text
                        style={[globalStyles.bodyLarge, globalStyles.textLight]}
                      >
                        {item.avg_cases_per_week}
                      </Text>
                    </View>
                  )}
                {Boolean(item.moq) && (
                  <View style={styles.itemDetailLine}>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      MOQ
                    </Text>
                    <Text
                      style={[globalStyles.bodyLarge, globalStyles.textLight]}
                    >
                      {item.moq}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* UOM Selection */}
            {itemHasUoms && !user.doesnt_use_uom && (
              <View style={styles.uomSelectorContainer}>
                <TouchableOpacity
                  style={styles.uomSelector}
                  onPress={() => setUomDropdownOpen(!uomDropdownOpen)}
                >
                  <Text
                    style={[globalStyles.headlineMedium, globalStyles.text]}
                  >
                    UOM:{" "}
                    {uomOptions.find((opt) => opt.value === selectedUomId)
                      ?.label || "Unit"}
                  </Text>
                  <Ionicons
                    name={uomDropdownOpen ? "chevron-up" : "chevron-down"}
                    size={28}
                    color={theme.appColors.text}
                  />
                </TouchableOpacity>

                {uomDropdownOpen && (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.uomOptionsContainer}
                    contentContainerStyle={styles.uomOptionsContent}
                  >
                    {uomOptions.map((option) => (
                      <TouchableOpacity
                        key={option.value}
                        style={[
                          styles.uomOption,
                          selectedUomId === option.value &&
                            styles.selectedUomOption,
                        ]}
                        onPress={() => {
                          setSelectedUomId(option.value);
                          setUomDropdownOpen(false);
                        }}
                      >
                        <Text
                          style={[
                            globalStyles.bodyMedium,
                            globalStyles.text,
                            selectedUomId === option.value &&
                              styles.selectedUomText,
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                )}
              </View>
            )}

            <View style={styles.quantityContainer}>
              <View
                style={{
                  flexDirection: "column",
                  justifyContent: "center",
                  flexWrap: "wrap",
                  verticalAlign: "middle",
                }}
              >
                <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                  {user.doesnt_use_uom
                    ? "Quantity"
                    : `Quantity (${selectedUomName})`}
                </Text>
              </View>
              <NumericInput
                onEditQuantity={onEditQuantity}
                quantity={quantity}
              />
            </View>

            {/* MOQ warning logic - mutually exclusive conditions */}
            {user.is_user_account_selected_by_driver &&
            Number(item.moq) > 0 &&
            getItemQuantityInCart() < Number(item.moq) &&
            getItemQuantityInCart() > 0 ? (
              <View
                style={{
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "center",
                }}
              >
                <Ionicons
                  name="warning"
                  size={16}
                  color={theme.appColors.redText}
                  style={{ marginRight: 5 }}
                />
                <Text
                  style={[
                    globalStyles.bodyMedium,
                    globalStyles.text,
                    { color: theme.appColors.redText },
                  ]}
                >
                  {t("item.cart_is_less_than_moq")}
                </Text>
              </View>
            ) : user.is_user_account_selected_by_driver &&
              Number(item.moq) > 0 &&
              quantity < Number(item.moq) &&
              getItemQuantityInCart() === 0 ? (
              <View
                style={{
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "center",
                }}
              >
                <Ionicons
                  name="warning"
                  size={16}
                  color={theme.appColors.redText}
                  style={{ marginRight: 5 }}
                />
                <Text
                  style={[
                    globalStyles.bodyMedium,
                    globalStyles.text,
                    { color: theme.appColors.redText },
                  ]}
                >
                  {t("item.quantity_below_moq")}
                </Text>
              </View>
            ) : !user.is_user_account_selected_by_driver &&
              Number(item.moq) > 0 &&
              quantity < Number(item.moq) ? (
              <View
                style={{
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "center",
                }}
              >
                <Ionicons
                  name="warning"
                  size={16}
                  color={theme.appColors.redText}
                  style={{ marginRight: 5 }}
                />
                <Text
                  style={[
                    globalStyles.bodyMedium,
                    globalStyles.text,
                    { color: theme.appColors.redText },
                  ]}
                >
                  {t("item.quantity_must_be_at_least_moq")}
                </Text>
              </View>
            ) : null}
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                marginTop: 10,
              }}
            >
              <Button
                onPress={onAddToCartPressed}
                variant="light"
                size="lg"
                disabled={
                  (itemOOS() &&
                    !(
                      supplier.id === "31" &&
                      user.is_user_account_selected_by_driver
                    )) ||
                  (item.moq &&
                    quantity < item.moq &&
                    !user.is_user_account_selected_by_driver)
                }
              >
                {!itemOOS() ? (
                  <Text>Add To Cart</Text>
                ) : supplier.id === "31" &&
                  user.is_user_account_selected_by_driver ? (
                  <Text>Add To Cart (OOS)</Text>
                ) : (
                  <Text>Out of Stock</Text>
                )}
              </Button>
            </View>
            {(() => {
              // Get all cart items for this item (different UOMs)
              const allCartItems = cart.cartItems.filter(
                (cartItem) => cartItem.item_id == item.id
              );
              if (allCartItems.length === 0) return null;

              return (
                <View style={{ alignItems: "center", marginTop: 10 }}>
                  <Text style={[globalStyles.headlineSmall, globalStyles.text]}>
                    Already In Cart:{" "}
                    {allCartItems
                      .map((cartItem, index) => {
                        const uomQuantity = cartItem?.item_uom_id
                          ? getUomQuantity(cartItem, cartItem.item_uom_id)
                          : 1;
                        const displayQuantity = Number(
                          (cartItem.quantity / uomQuantity).toFixed(2)
                        );
                        const uomName = cartItem?.item_uom_id
                          ? getUomName(item, cartItem.item_uom_id)
                          : "Unit";

                        return `${displayQuantity} ${uomName}${
                          index < allCartItems.length - 1 ? ", " : ""
                        }`;
                      })
                      .join("")}
                  </Text>
                </View>
              );
            })()}
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

AddItemModal.propTypes = {
  item: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default AddItemModal;
