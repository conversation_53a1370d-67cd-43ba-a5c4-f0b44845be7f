query GetOrdersV2($ordersV2Input: OrdersInputV2!) {
  ordersV2(ordersInput: $ordersV2Input) {
    orders {
        customerDetails {
            id
            name
        }
        id
        status
        subtotal
        date_submitted
        orderName
        config
        delivery_date
        supplier
        supplier_logo
        invoice {
          id
          discount
          paid
          credit
          payment_method
          payment_status
          subtotal
          total
          config
        }
      }
    }
  }
