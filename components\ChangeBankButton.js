import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo } from "react";
import Button from "./Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderColor: "white",
      borderRadius: 10,
      borderWidth: 1,
      justifyContent: "center",
      marginRight: 0,
    },
    buttonText: {
      color: "white",
      fontSize: 14,
      fontWeight: theme.fonts.medium.fontWeight,
      textAlign: "center",
    },
    container: {
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 10,
      display: "flex",
      flexDirection: "row",
      height: 50,
      justifyContent: "space-between",

      // marginHorizontal: 15,
    },
    mainText: {
      color: "white",
      fontSize: 14,
      fontWeight: theme.fonts.medium.fontWeight,
      marginLeft: 15,
    },
    standardColumn: {
      display: "flex",
      justifyContent: "center",
    },
  });
  return styles;
};

export default function ChangeBankButton({ mainText, buttonText, onPress }) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.container}>
      <View style={[styles.standardColumn, { width: "65%" }]}>
        <Text numberOfLines={0} style={styles.mainText}>
          {mainText}
        </Text>
      </View>
      <View style={[styles.standardColumn, { width: "35%" }]}>
        <View
          style={{
            display: "flex",
            justifyContent: "center",
            flexDirection: "row",
          }}
        >
          <Button
            style={styles.button}
            variant="contained"
            size="sm"
            onPress={onPress}
          >
            <Text style={styles.buttonText}>{buttonText}</Text>
          </Button>
        </View>
      </View>
    </View>
  );
}
