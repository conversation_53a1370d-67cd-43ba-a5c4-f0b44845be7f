import { useTheme } from "@react-navigation/native";
import { useContext, useMemo, useState } from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import CartItem from "../../components/CartItem";
import FooterButton from "../../components/FooterButton";
import Header from "../../components/Header";
import SafeAreaView from "../../components/SafeAreaView";
import StoreSummary from "../../components/StoreSummary";
import { UserContext } from "../../context/userContext";
import { useUpdateItemInCartMutation } from "../../generated/graphql";
import { getCustomPrice } from "../../lib/getPrice";
import { getTotal } from "../../lib/getTotal";
import { posthog } from "../../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.appColors.primary,
      flex: 1,
    },
    itemsContainer: {
      marginBottom: 30,
      marginTop: 20,
    },
    scrollView: {
      backgroundColor: "white",
      paddingHorizontal: 20,
    },
  });
  return styles;
};

export default function SubCartScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const supplier_id = user.suppliers[0].id;

  const { supplier } = route.params;
  const [updateItemInCart] = useUpdateItemInCartMutation();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const [clearAllPressed, setClearAllPressed] = useState(false);
  const [isCredit, setIsCredit] = useState(false);

  const subCart = user.cart.subCarts.find(
    (subCart) => subCart.supplier === supplier
  ) || {
    cartItems: [],
  };

  const supplierObj = user.suppliers.find(
    (element) => element.name === supplier
  );

  const cart = user.cart;
  const minimum = user.suppliers.find(
    (element) => element.name === supplier
  )?.minimum;

  const goToCheckout = () => {
    posthog.capture("checkout_clicked", {
      supplier: supplier,
      cartId: cart.id,
    });
    navigation.navigate("CheckoutScreen", {
      cartId: cart.id,
      supplier: supplier,
      imageUri: supplierObj?.config?.squareLogo ?? supplierObj.logo ?? null,
      isCredit,
    });
  };

  const onEditQuantity = async (cartId, itemId, quantity) => {
    await updateItemInCart({
      variables: {
        updateItemInCartInput: {
          cartId: cartId,
          itemId: itemId,
          quantity: quantity,
          userId: user.id,
          supplierId: supplier_id,
        },
      },
    });

    if (
      user &&
      user.suppliers &&
      user.suppliers[0] &&
      user.suppliers[0].id == "68"
    ) {
      const orderItems = subCart.cartItems;

      const crv5Quantity = orderItems.reduce((total, item) => {
        if (item.crv === "CA CRV $0.05") {
          return total + (Number(item.quantity) || 0);
        }
        return total;
      }, 0);

      const crv10Quantity = orderItems.reduce((total, item) => {
        if (item.crv === "CA CRV $0.10") {
          return total + (Number(item.quantity) || 0);
        }
        return total;
      }, 0);

      if (crv5Quantity > 0) {
        await updateItemInCart({
          variables: {
            updateItemInCartInput: {
              cartId: user.cart.id,
              itemId: "391457",
              quantity: crv5Quantity,
              userId: user.id,
              supplierId: supplier_id,
            },
          },
        });
      }
      if (crv10Quantity > 0) {
        await updateItemInCart({
          variables: {
            updateItemInCartInput: {
              cartId: user.cart.id,
              itemId: "391456",
              quantity: crv10Quantity,
              userId: user.id,
              supplierId: supplier_id,
            },
          },
        });
      }
    }
  };

  const onClearAllPressed = async () => {
    if (clearAllPressed) {
      for (const item of subCart.cartItems) {
        await onEditQuantity(cart.id, item.item_id, 0);
      }
    }
    setClearAllPressed(!clearAllPressed);
    setTimeout(() => setClearAllPressed(false), 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        goBack={navigation.goBack}
        title={`Review ${!isCredit ? "Order" : "Credit"}`}
      />
      <StoreSummary
        imageUri={supplierObj?.config?.squareLogo ?? supplierObj.logo ?? null}
        store={supplier}
        onPress={onClearAllPressed}
        buttonPressed={clearAllPressed}
        buttonText={"Clear All"}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.itemsContainer}>
          {subCart.cartItems.map((cartItem) => {
            return (
              <CartItem
                key={cartItem.id}
                imageUri={
                  cartItem.image ||
                  supplierObj?.config?.defaultItemImage ||
                  "https://fakeimg.pl/100x100?text=no+image"
                }
                itemName={cartItem.name}
                unitSize={cartItem.unit_size}
                quantity={cartItem.quantity}
                amount={(
                  Math.round(
                    getCustomPrice(cartItem) * cartItem.quantity * 100
                  ).toFixed(2) / 100
                ).toFixed(2)}
                onEditQuantity={onEditQuantity}
                cartId={cart.id}
                itemId={cartItem.item_id}
                item={cartItem}
              />
            );
          })}
        </View>
      </ScrollView>
      <FooterButton
        minimum={minimum}
        mainText={
          user.show_prices
            ? `Subtotal: ${isCredit ? "-" : ""}$` + getTotal(subCart.cartItems)
            : null
        }
        buttonText={
          getTotal(subCart.cartItems) < minimum
            ? `Add $${Math.round(
                minimum - parseFloat(getTotal(subCart.cartItems))
              ).toFixed(2)} To Checkout`
            : "Checkout"
        }
        onPress={goToCheckout}
        disabled={getTotal(subCart.cartItems) < minimum}
        isCredit={isCredit}
        onChangeIsCredit={setIsCredit}
      />
    </SafeAreaView>
  );
}
