import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  Date: any;
  DateTime: any;
  JSON: any;
  JSONObject: any;
  Time: any;
};

export type Account = {
  __typename?: 'Account';
  balanceBankAccount?: Maybe<BalanceBankAccount>;
  balanceCreditCard?: Maybe<BalanceCreditCard>;
  id?: Maybe<Scalars['String']>;
  is_default?: Maybe<Scalars['Boolean']>;
  type?: Maybe<Scalars['String']>;
};

export type ActionItem = {
  __typename?: 'ActionItem';
  description?: Maybe<Scalars['String']>;
  invoiceItem?: Maybe<InvoiceItem>;
};

export type ActivityLog = {
  __typename?: 'ActivityLog';
  created_at: Scalars['DateTime'];
  employee_id?: Maybe<Scalars['Int']>;
  id: Scalars['Int'];
  location?: Maybe<Scalars['String']>;
  log_number: Scalars['Int'];
  metadata?: Maybe<Scalars['JSONObject']>;
  supplier_id: Scalars['Int'];
  type: Scalars['String'];
  user_id?: Maybe<Scalars['Int']>;
};

export type ActivityLogFilters = {
  employeeId?: InputMaybe<Scalars['Int']>;
  location?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['Int']>;
};

export type ActivityLogInput = {
  filters?: InputMaybe<ActivityLogFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['Int'];
};

export type ActivityLogOutput = {
  __typename?: 'ActivityLogOutput';
  activityLog?: Maybe<Array<Maybe<ActivityLog>>>;
  totalCount?: Maybe<Scalars['Int']>;
};

export enum ActivityLogType {
  CreditCreated = 'CREDIT_CREATED',
  OrderCreated = 'ORDER_CREATED',
  OrderSubmitted = 'ORDER_SUBMITTED'
}

export type AddInvoiceInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  credit?: InputMaybe<Scalars['Float']>;
  date_created?: InputMaybe<Scalars['DateTime']>;
  date_received?: InputMaybe<Scalars['DateTime']>;
  discount?: InputMaybe<Scalars['Float']>;
  invoice_id?: InputMaybe<Scalars['String']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
  notes?: InputMaybe<Scalars['String']>;
  order_id?: InputMaybe<Scalars['ID']>;
  order_number?: InputMaybe<Scalars['Int']>;
  paid?: InputMaybe<Scalars['Float']>;
  payment_method?: InputMaybe<Scalars['String']>;
  return_items?: InputMaybe<Scalars['Boolean']>;
  subtotal: Scalars['Float'];
  supplier_id: Scalars['ID'];
  total?: InputMaybe<Scalars['Float']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_id?: InputMaybe<Scalars['ID']>;
};

export type AddInvoiceItemsInput = {
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
};

export type AddItemToCartInput = {
  cartId: Scalars['ID'];
  itemId: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  quantity: Scalars['Int'];
};

export type AddPushNotificationTokenInput = {
  token: Scalars['String'];
  user_id: Scalars['ID'];
};

export type AffectedItem = {
  __typename?: 'AffectedItem';
  discount_amount: Scalars['Float'];
  item_id: Scalars['ID'];
  promotion_id: Scalars['ID'];
};

export type AllCustomPricesInputV2 = {
  supplierId: Scalars['ID'];
};

export type AllCustomPricesOutputV2 = BaseOutput & {
  __typename?: 'AllCustomPricesOutputV2';
  totalCount: Scalars['Int'];
  userCustomPrices?: Maybe<Array<Maybe<UserCustomPrices>>>;
};

export type BalanceBankAccount = {
  __typename?: 'BalanceBankAccount';
  accountName?: Maybe<Scalars['String']>;
  accountNumberMask?: Maybe<Scalars['String']>;
  institutionName?: Maybe<Scalars['String']>;
};

export type BalanceCreditCard = {
  __typename?: 'BalanceCreditCard';
  brand?: Maybe<Scalars['String']>;
  expiredMonth?: Maybe<Scalars['String']>;
  expiredYear?: Maybe<Scalars['String']>;
  last4?: Maybe<Scalars['String']>;
};

export type BalanceLink = {
  __typename?: 'BalanceLink';
  link?: Maybe<Scalars['String']>;
};

export type BaseOutput = {
  totalCount: Scalars['Int'];
};

export type BasicCategory = {
  __typename?: 'BasicCategory';
  image?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type BrandSpotlights = {
  __typename?: 'BrandSpotlights';
  id: Scalars['ID'];
  name: Scalars['String'];
  spotlight_image?: Maybe<Scalars['String']>;
};

export type CaptureTransactionInput = {
  amount?: InputMaybe<Scalars['Float']>;
  transactionId?: InputMaybe<Scalars['String']>;
};

export type Cart = {
  __typename?: 'Cart';
  cartItems?: Maybe<Array<Maybe<CartItem>>>;
  created_at?: Maybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  is_open?: Maybe<Scalars['Boolean']>;
  subCarts?: Maybe<Array<Maybe<SubCart>>>;
  subtotal?: Maybe<Scalars['Float']>;
  total_quantity?: Maybe<Scalars['Int']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  userId: Scalars['ID'];
};

export type CartItem = {
  __typename?: 'CartItem';
  archived?: Maybe<Scalars['Boolean']>;
  cart_id: Scalars['ID'];
  cog_price?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  crv?: Maybe<Scalars['String']>;
  custom_price?: Maybe<Scalars['Float']>;
  discounted_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  item_id: Scalars['ID'];
  item_uom_id?: Maybe<Scalars['ID']>;
  metadata?: Maybe<Scalars['String']>;
  nacs_category?: Maybe<Scalars['String']>;
  nacs_subcategory?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  notes?: Maybe<Scalars['String']>;
  oos?: Maybe<Scalars['Boolean']>;
  price?: Maybe<Scalars['Float']>;
  price_purchased_at?: Maybe<Scalars['Float']>;
  qb_id?: Maybe<Scalars['String']>;
  qoh?: Maybe<Scalars['Int']>;
  quantity?: Maybe<Scalars['Int']>;
  size?: Maybe<Scalars['String']>;
  supplier?: Maybe<Scalars['String']>;
  supplier_code?: Maybe<Scalars['String']>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<ItemUom>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type CatalogDisplayOptionsInput = {
  show_case_size: Scalars['Boolean'];
  show_sku: Scalars['Boolean'];
  show_stock: Scalars['Boolean'];
  show_upc: Scalars['Boolean'];
};

export type CatalogTemplate = {
  __typename?: 'CatalogTemplate';
  config: Scalars['JSON'];
  created_at: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  pdf_path?: Maybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
  updated_at: Scalars['DateTime'];
};

export type CatalogTemplateConfigInput = {
  category?: InputMaybe<Scalars['String']>;
  company_info: Scalars['String'];
  display_options: CatalogDisplayOptionsInput;
  pricing_enabled: Scalars['Boolean'];
  sort_by: CatalogTemplateSort;
  subtitle?: InputMaybe<Scalars['String']>;
  title: Scalars['String'];
};

export enum CatalogTemplateSort {
  Category = 'CATEGORY',
  ProductName = 'PRODUCT_NAME',
  Sku = 'SKU'
}

export type Category = {
  __typename?: 'Category';
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  items?: Maybe<Array<Maybe<Item>>>;
  name: Scalars['String'];
  ordering?: Maybe<Scalars['Int']>;
  supplier_id: Scalars['ID'];
};

export type CategoryInput = {
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['String']>;
  items?: InputMaybe<Array<InputMaybe<ItemInput>>>;
  name: Scalars['String'];
  ordering?: InputMaybe<Scalars['Int']>;
  supplier_id: Scalars['ID'];
};

export type CreateActivityLogInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  location?: InputMaybe<Scalars['String']>;
  metadata?: InputMaybe<Scalars['JSONObject']>;
  supplierId: Scalars['Int'];
  type: Scalars['String'];
  userId?: InputMaybe<Scalars['Int']>;
};

export type CreateBalanceTransactionInput = {
  amount: Scalars['Float'];
  title: Scalars['String'];
  userId: Scalars['ID'];
};

export type CreateCartInput = {
  userId: Scalars['ID'];
};

export type CreateCatalogTemplateInput = {
  config: CatalogTemplateConfigInput;
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type CreateCreditInput = {
  credit_items: Array<CreditItemInput>;
  images?: InputMaybe<Array<Scalars['String']>>;
  invoice_id: Scalars['Int'];
  status?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['Int'];
  user_id: Scalars['Int'];
};

export type CreateEmployeeInput = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email: Scalars['String'];
  name: Scalars['String'];
  password: Scalars['String'];
  phone: Scalars['String'];
  roleIds?: InputMaybe<Array<Scalars['ID']>>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  supplierId: Scalars['ID'];
};

export type CreateGoalAssignmentInput = {
  employee_id?: InputMaybe<Scalars['ID']>;
  target_amount: Scalars['Float'];
};

export type CreateGoalInput = {
  assignments: Array<CreateGoalAssignmentInput>;
  end_date?: InputMaybe<Scalars['Date']>;
  name: Scalars['String'];
  period: GoalPeriod;
  start_date: Scalars['Date'];
  supplier_id: Scalars['ID'];
  target_amount: Scalars['Float'];
  type: GoalType;
};

export type CreateOrderInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  notes?: InputMaybe<Scalars['String']>;
  orderItems?: InputMaybe<Array<InputMaybe<UpdateOrderItemInput>>>;
  orderName?: InputMaybe<Scalars['String']>;
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type CreatePromotionInput = {
  active?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_items: Scalars['Boolean'];
  applies_to_all_users: Scalars['Boolean'];
  buy_quantity?: InputMaybe<Scalars['Int']>;
  discount_amount?: InputMaybe<Scalars['Float']>;
  discount_percentage?: InputMaybe<Scalars['Float']>;
  end_date: Scalars['DateTime'];
  free_quantity?: InputMaybe<Scalars['Int']>;
  items?: InputMaybe<Array<PromotionItemInput>>;
  max_uses_per_customer?: InputMaybe<Scalars['Int']>;
  min_order_amount?: InputMaybe<Scalars['Float']>;
  name: Scalars['String'];
  promotion_type: Scalars['ID'];
  start_date: Scalars['DateTime'];
  supplier_id: Scalars['ID'];
  total_usage_limit?: InputMaybe<Scalars['Int']>;
  users?: InputMaybe<Array<PromotionUserInput>>;
};

export type CreateSupplierConfigInput = {
  show_order_tabs?: InputMaybe<Scalars['Boolean']>;
  supplierId: Scalars['ID'];
};

export type CreateSupplierInput = {
  address?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  email: Scalars['String'];
  logo?: InputMaybe<Scalars['String']>;
  minimum?: InputMaybe<Scalars['Int']>;
  name: Scalars['String'];
  need_signup?: InputMaybe<Scalars['Boolean']>;
  password: Scalars['String'];
  phone_number?: InputMaybe<Scalars['String']>;
  spotlight_image?: InputMaybe<Scalars['String']>;
};

export type CreateTable = {
  cartID: Scalars['ID'];
  itemID: Scalars['ID'];
  quantity: Scalars['Int'];
};

export type CreateUserInput = {
  address?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  contact_email?: InputMaybe<Scalars['String']>;
  created_at?: InputMaybe<Scalars['DateTime']>;
  created_by?: InputMaybe<Scalars['String']>;
  custom_prices?: InputMaybe<Array<InputMaybe<CustomPriceWithoutUserIdInput>>>;
  custom_uom_prices?: InputMaybe<Array<InputMaybe<CustomUomPriceInput>>>;
  delivery_window?: InputMaybe<DeliveryWindowInput>;
  ein?: InputMaybe<Scalars['String']>;
  hidden_products?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  net_terms_days?: InputMaybe<Scalars['Int']>;
  password?: InputMaybe<Scalars['String']>;
  phone_number?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  route_id?: InputMaybe<Scalars['ID']>;
  store_group?: InputMaybe<Scalars['String']>;
  suppliers: Array<Scalars['ID']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_name?: InputMaybe<Scalars['String']>;
};

export type Credit = {
  __typename?: 'Credit';
  archived?: Maybe<Scalars['Boolean']>;
  cash_amount?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  creditItems?: Maybe<Array<Maybe<CreditItem>>>;
  credit_number?: Maybe<Scalars['Int']>;
  customerDetails?: Maybe<User>;
  id?: Maybe<Scalars['Int']>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  invoiceDetails?: Maybe<Invoice>;
  invoice_id?: Maybe<Scalars['Int']>;
  order_id?: Maybe<Scalars['Int']>;
  order_number?: Maybe<Scalars['Int']>;
  status: CreditStatus;
  supplier_id?: Maybe<Scalars['Int']>;
  total?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  user_id?: Maybe<Scalars['Int']>;
};

export type CreditItem = {
  __typename?: 'CreditItem';
  credit_id?: Maybe<Scalars['Int']>;
  id?: Maybe<Scalars['Int']>;
  invoice_item_id?: Maybe<Scalars['Int']>;
  item_id?: Maybe<Scalars['Int']>;
  item_snapshot?: Maybe<Item>;
  note?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  reason?: Maybe<Scalars['String']>;
  total_price?: Maybe<Scalars['Float']>;
  unit_price?: Maybe<Scalars['Float']>;
};

export type CreditItemInput = {
  item_id?: InputMaybe<Scalars['Int']>;
  note?: InputMaybe<Scalars['String']>;
  quantity?: InputMaybe<Scalars['Int']>;
  reason?: InputMaybe<CreditReason>;
};

export enum CreditReason {
  Damaged = 'DAMAGED',
  Expired = 'EXPIRED',
  Mispick = 'MISPICK',
  Missing = 'MISSING',
  Other = 'OTHER',
  Overage = 'OVERAGE',
  Return = 'RETURN'
}

export type CreditRequest = {
  __typename?: 'CreditRequest';
  customerDetails?: Maybe<User>;
  damaged?: Maybe<Scalars['Boolean']>;
  expired?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  itemDetails?: Maybe<Item>;
  item_id: Scalars['ID'];
  mispick?: Maybe<Scalars['Boolean']>;
  order_id: Scalars['ID'];
  price_purchased_at?: Maybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  status?: Maybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
  user_id: Scalars['ID'];
};

export type CreditRequestInput = {
  isDamaged?: InputMaybe<Scalars['Boolean']>;
  isExpired?: InputMaybe<Scalars['Boolean']>;
  isMispick?: InputMaybe<Scalars['Boolean']>;
  itemId: Scalars['ID'];
  pricePurchasedAt?: InputMaybe<Scalars['Float']>;
  quantity: Scalars['Int'];
};

export enum CreditStatus {
  Approved = 'APPROVED',
  Cancelled = 'CANCELLED',
  Pending = 'PENDING',
  Processed = 'PROCESSED',
  Rejected = 'REJECTED'
}

export type CustomPrice = {
  __typename?: 'CustomPrice';
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomPriceInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomPriceWithoutUserId = {
  __typename?: 'CustomPriceWithoutUserId';
  item_id: Scalars['ID'];
  price: Scalars['Float'];
};

export type CustomPriceWithoutUserIdInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
};

export type CustomUomPrice = {
  __typename?: 'CustomUOMPrice';
  item_uom_id?: Maybe<Scalars['ID']>;
  price: Scalars['Float'];
  uom_id?: Maybe<Scalars['ID']>;
  uom_name: Scalars['String'];
  user_id: Scalars['ID'];
};

export type CustomUomPriceInput = {
  item_uom_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomUomPricesByItem = {
  __typename?: 'CustomUOMPricesByItem';
  item_id: Scalars['ID'];
  uom_prices: Array<CustomUomPrice>;
};

export type CustomerGroup = {
  __typename?: 'CustomerGroup';
  active?: Maybe<Scalars['Int']>;
  count?: Maybe<Scalars['Int']>;
  group?: Maybe<Scalars['String']>;
};

export type CustomerHiddenProduct = {
  __typename?: 'CustomerHiddenProduct';
  itemId: Scalars['ID'];
  supplierId?: Maybe<Scalars['ID']>;
  userId?: Maybe<Scalars['ID']>;
};

export type CustomerHiddenProductsInput = {
  customerId?: InputMaybe<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type CustomerHiddenProductsOutput = {
  __typename?: 'CustomerHiddenProductsOutput';
  items: Array<Maybe<CustomerHiddenProduct>>;
};

export type CutoffTime = {
  __typename?: 'CutoffTime';
  cutoffDay?: Maybe<Scalars['String']>;
  cutoffTime?: Maybe<Scalars['String']>;
  daysToDelivery?: Maybe<Scalars['Int']>;
  deliveryDay?: Maybe<Scalars['String']>;
  deliveryTime?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
  supplier: Scalars['String'];
  supplierInfo?: Maybe<Supplier>;
};

export type DashboardMetricBestSeller = {
  __typename?: 'DashboardMetricBestSeller';
  category?: Maybe<Category>;
  image?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
};

export type DashboardMetricBrandBreakdown = {
  __typename?: 'DashboardMetricBrandBreakdown';
  brand?: Maybe<Scalars['String']>;
  breakdown?: Maybe<Scalars['Float']>;
  quantity?: Maybe<Scalars['Int']>;
};

export enum DashboardMetricType {
  ActiveBuyers = 'ACTIVE_BUYERS',
  AvgOrderValue = 'AVG_ORDER_VALUE',
  BestSellers = 'BEST_SELLERS',
  BrandBreakdown = 'BRAND_BREAKDOWN',
  CasesOrdered = 'CASES_ORDERED',
  CreditTotal = 'CREDIT_TOTAL',
  FirstOrders = 'FIRST_ORDERS',
  GmvTotal = 'GMV_TOTAL',
  NetRevenue = 'NET_REVENUE',
  NumCanceledOrders = 'NUM_CANCELED_ORDERS',
  NumConfirmedOrders = 'NUM_CONFIRMED_ORDERS',
  NumInvoices = 'NUM_INVOICES',
  NumOrders = 'NUM_ORDERS',
  NumUnapprovedCustomers = 'NUM_UNAPPROVED_CUSTOMERS',
  NumUnconfirmedOrders = 'NUM_UNCONFIRMED_ORDERS',
  NumUsers = 'NUM_USERS',
  ProfitTotal = 'PROFIT_TOTAL',
  SalesByRoutes = 'SALES_BY_ROUTES'
}

export type DashboardMetrics = {
  __typename?: 'DashboardMetrics';
  ACTIVE_BUYERS?: Maybe<Scalars['Int']>;
  AVG_ORDER_VALUE?: Maybe<Scalars['Float']>;
  BEST_SELLERS?: Maybe<Array<Maybe<DashboardMetricBestSeller>>>;
  BRAND_BREAKDOWN?: Maybe<Array<Maybe<DashboardMetricBrandBreakdown>>>;
  CASES_ORDERED?: Maybe<Scalars['Int']>;
  CREDIT_TOTAL?: Maybe<Scalars['Float']>;
  FIRST_ORDERS?: Maybe<Scalars['Int']>;
  GMV_TOTAL?: Maybe<Scalars['Float']>;
  NET_REVENUE?: Maybe<Scalars['Float']>;
  NUM_CANCELED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_CONFIRMED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_INVOICES?: Maybe<Scalars['Int']>;
  NUM_ORDERS?: Maybe<Scalars['Int']>;
  NUM_UNAPPROVED_CUSTOMERS?: Maybe<Scalars['Int']>;
  NUM_UNCONFIRMED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_USERS?: Maybe<Scalars['Int']>;
  PROFIT_TOTAL?: Maybe<Scalars['Float']>;
  SALES_BY_ROUTES?: Maybe<Array<Maybe<SalesByRoute>>>;
};

export type Deal = {
  __typename?: 'Deal';
  deal_id?: Maybe<Scalars['ID']>;
  discount?: Maybe<Scalars['Float']>;
  item_id?: Maybe<Scalars['ID']>;
  quantity?: Maybe<Scalars['Int']>;
  type?: Maybe<Scalars['String']>;
};

export type DeleteCreditInput = {
  id: Scalars['Int'];
};

export type DeliveryWindow = {
  __typename?: 'DeliveryWindow';
  days_of_week?: Maybe<Array<Maybe<Scalars['String']>>>;
  end_time?: Maybe<Scalars['String']>;
  start_time?: Maybe<Scalars['String']>;
};

export type DeliveryWindowInput = {
  days_of_week?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  end_time?: InputMaybe<Scalars['String']>;
  start_time?: InputMaybe<Scalars['String']>;
};

export type Employee = {
  __typename?: 'Employee';
  app_access: Scalars['Boolean'];
  archived: Scalars['Boolean'];
  created_at: Scalars['DateTime'];
  dashboard_access: Scalars['Boolean'];
  email: Scalars['String'];
  id: Scalars['ID'];
  last_login?: Maybe<Scalars['DateTime']>;
  name: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  roles?: Maybe<Array<Role>>;
  routes?: Maybe<Array<Route>>;
  updated_at: Scalars['DateTime'];
};

export type EmployeesFiltersV2 = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  createdAfter?: InputMaybe<Scalars['DateTime']>;
  createdBefore?: InputMaybe<Scalars['DateTime']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  includeArchived?: InputMaybe<Scalars['Boolean']>;
  lastLoginAfter?: InputMaybe<Scalars['DateTime']>;
  lastLoginBefore?: InputMaybe<Scalars['DateTime']>;
  name?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  roleIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type EmployeesInputV2 = {
  filters?: InputMaybe<EmployeesFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type EmployeesOutputV2 = BaseOutput & {
  __typename?: 'EmployeesOutputV2';
  employees: Array<Employee>;
  totalCount: Scalars['Int'];
};

export type GetActionItemsInput = {
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_item_id?: InputMaybe<Scalars['ID']>;
};

export type GetBestSellersFilters = {
  category?: InputMaybe<Scalars['ID']>;
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  routeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  serviceType?: InputMaybe<Scalars['String']>;
};

export type GetBestSellersInput = {
  filters?: InputMaybe<GetBestSellersFilters>;
  limit?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  supplier_id: Scalars['ID'];
};

export type GetBestSellersOutput = {
  __typename?: 'GetBestSellersOutput';
  bestSellers: Array<Maybe<DashboardMetricBestSeller>>;
  totalCount: Scalars['Int'];
};

export type GetCartsInput = {
  ids?: InputMaybe<Array<Scalars['ID']>>;
  userId?: InputMaybe<Scalars['ID']>;
  userIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type GetCatalogTemplatesInput = {
  id?: InputMaybe<Scalars['ID']>;
  supplier_id: Scalars['ID'];
};

export type GetCategoriesBySupplierInput = {
  fillItemsData?: InputMaybe<Scalars['Boolean']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetCategoriesInput = {
  pagination?: InputMaybe<PaginationInput>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetCreditsFilters = {
  archived?: InputMaybe<Scalars['Boolean']>;
  createdAtRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  invoice_ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  statuses?: InputMaybe<Array<InputMaybe<CreditStatus>>>;
  user_ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type GetCreditsInput = {
  filters?: InputMaybe<GetCreditsFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplier_id: Scalars['Int'];
};

export type GetCutoffTimesInput = {
  businessId: Scalars['ID'];
};

export type GetFavoritesInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  userId: Scalars['Int'];
};

export type GetInvoiceItemMatchInput = {
  invoiceId: Scalars['ID'];
  upc: Scalars['String'];
};

export type GetInvoicesBySupplierFilters = {
  dateRange?: InputMaybe<Array<Scalars['DateTime']>>;
  deliveryDate?: InputMaybe<Scalars['DateTime']>;
  driver?: InputMaybe<Scalars['String']>;
  lastPaidDate?: InputMaybe<Scalars['Date']>;
  paidStatus?: InputMaybe<Scalars['String']>;
  payment_status?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
};

export type GetInvoicesBySupplierInput = {
  filters?: InputMaybe<GetInvoicesBySupplierFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type GetInvoicesInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  onlyWithCredits?: InputMaybe<Scalars['Boolean']>;
  orderIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsByFilterInput = {
  category?: InputMaybe<Scalars['String']>;
  pagination?: InputMaybe<PaginationInput>;
  tag?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsBySupplierInput = {
  brand?: InputMaybe<Scalars['String']>;
  category?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  section?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  upc?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  upcs?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetOrderItemTotalsInput = {
  currentTz?: InputMaybe<Scalars['String']>;
  dayOffset?: InputMaybe<Scalars['Int']>;
  numWeeks: Scalars['Int'];
  routeId?: InputMaybe<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type GetOrdersBySupplierFilters = {
  deliveryDate?: InputMaybe<Scalars['Date']>;
  deliveryDateRange?: InputMaybe<Array<Scalars['Date']>>;
  driver?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
  userIds?: InputMaybe<Array<Scalars['String']>>;
};

export type GetOrdersBySupplierInput = {
  filters?: InputMaybe<GetOrdersBySupplierFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type GetOrdersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetPromotionsInput = {
  filters?: InputMaybe<PromotionFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplier_id: Scalars['ID'];
};

export type GetPushNotificationTokensInput = {
  user_ids?: InputMaybe<Array<Scalars['ID']>>;
};

export type GetRecommendationsInput = {
  limit?: InputMaybe<Scalars['Int']>;
  userId: Scalars['ID'];
};

export type GetRouteTotalsInput = {
  currentTz?: InputMaybe<Scalars['String']>;
  dayOffset?: InputMaybe<Scalars['Int']>;
  numWeeks: Scalars['Int'];
  supplierId: Scalars['ID'];
};

export type GetRoutesBySupplierInput = {
  supplierId: Scalars['ID'];
};

export type GetRoutesInput = {
  ids: Array<Scalars['ID']>;
};

export type GetSalesByRoutesFilters = {
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  serviceType?: InputMaybe<Scalars['String']>;
};

export type GetSalesByRoutesInput = {
  filters?: InputMaybe<GetSalesByRoutesFilters>;
  limit?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  supplier_id: Scalars['ID'];
};

export type GetSalesByRoutesOutput = {
  __typename?: 'GetSalesByRoutesOutput';
  salesByRoutes: Array<Maybe<SalesByRoute>>;
  totalCount: Scalars['Int'];
};

export type GetSectionsInput = {
  pagination?: InputMaybe<PaginationInput>;
  sections?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetSuppliersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
};

export type GetTagsInput = {
  category?: InputMaybe<Scalars['String']>;
  pagination?: InputMaybe<PaginationInput>;
};

export type GetUsersByFilterInput = {
  filters?: InputMaybe<GetUsersFilters>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetUsersFilters = {
  active?: InputMaybe<Scalars['String']>;
  driver?: InputMaybe<Scalars['String']>;
  hasOpenCart?: InputMaybe<Scalars['Boolean']>;
  hasUnpaidBalance?: InputMaybe<Scalars['Boolean']>;
  route?: InputMaybe<Scalars['String']>;
  searchTerm?: InputMaybe<Scalars['String']>;
};

export type GetUsersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  search?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type Goal = {
  __typename?: 'Goal';
  assignments: Array<GoalAssignment>;
  available_periods: Array<GoalPeriodOption>;
  created_at: Scalars['DateTime'];
  end_date?: Maybe<Scalars['Date']>;
  id: Scalars['ID'];
  is_active: Scalars['Boolean'];
  name: Scalars['String'];
  period: GoalPeriod;
  start_date: Scalars['Date'];
  status: GoalStatus;
  supplier_id: Scalars['ID'];
  target_amount: Scalars['Float'];
  type: GoalType;
  updated_at: Scalars['DateTime'];
};

export type GoalAssignment = {
  __typename?: 'GoalAssignment';
  created_at: Scalars['DateTime'];
  current_progress: Scalars['Float'];
  employee?: Maybe<Employee>;
  employee_id?: Maybe<Scalars['ID']>;
  goal_id: Scalars['ID'];
  id: Scalars['ID'];
  percentage_complete: Scalars['Float'];
  target_amount: Scalars['Float'];
  updated_at: Scalars['DateTime'];
};

export enum GoalPeriod {
  Daily = 'DAILY',
  Monthly = 'MONTHLY',
  Weekly = 'WEEKLY'
}

export type GoalPeriodOption = {
  __typename?: 'GoalPeriodOption';
  label: Scalars['String'];
  value: Scalars['String'];
};

export type GoalPeriodSelection = {
  goal_id: Scalars['ID'];
  period_start: Scalars['Date'];
};

export enum GoalStatus {
  Active = 'ACTIVE',
  Completed = 'COMPLETED',
  Past = 'PAST'
}

export enum GoalType {
  SalesAmount = 'SALES_AMOUNT',
  StoresSoldTo = 'STORES_SOLD_TO'
}

export type GoalsFilters = {
  employee_id?: InputMaybe<Scalars['ID']>;
  is_active?: InputMaybe<Scalars['Boolean']>;
  period?: InputMaybe<GoalPeriod>;
  status?: InputMaybe<GoalStatus>;
  supplier_id: Scalars['ID'];
  type?: InputMaybe<GoalType>;
};

export type GoalsInput = {
  filters: GoalsFilters;
  goal_period_selections?: InputMaybe<Array<GoalPeriodSelection>>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
};

export type GoalsResponse = {
  __typename?: 'GoalsResponse';
  goals: Array<Goal>;
  totalCount: Scalars['Int'];
};

export type GroupPrices = {
  __typename?: 'GroupPrices';
  group: Scalars['String'];
  prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
};

export type GroupPricesInputV2 = {
  groupName?: InputMaybe<Scalars['String']>;
  supplierId: Scalars['ID'];
};

export type GroupPricesOutputV2 = BaseOutput & {
  __typename?: 'GroupPricesOutputV2';
  groupPrices?: Maybe<Array<Maybe<GroupPrices>>>;
  totalCount: Scalars['Int'];
};

export type Invoice = {
  __typename?: 'Invoice';
  archived?: Maybe<Scalars['Boolean']>;
  config?: Maybe<Scalars['JSONObject']>;
  credit?: Maybe<Scalars['Float']>;
  customerDetails?: Maybe<User>;
  date_created?: Maybe<Scalars['DateTime']>;
  date_received?: Maybe<Scalars['Float']>;
  discount?: Maybe<Scalars['Float']>;
  id?: Maybe<Scalars['ID']>;
  invoiceItems?: Maybe<Array<Maybe<InvoiceItem>>>;
  invoice_id?: Maybe<Scalars['String']>;
  notes?: Maybe<Scalars['String']>;
  orderDetails?: Maybe<Order>;
  order_id?: Maybe<Scalars['ID']>;
  order_number?: Maybe<Scalars['Int']>;
  paid?: Maybe<Scalars['Float']>;
  payment_method?: Maybe<Scalars['String']>;
  payment_status?: Maybe<Scalars['String']>;
  qb_id?: Maybe<Scalars['String']>;
  signature?: Maybe<Scalars['String']>;
  signature_name?: Maybe<Scalars['String']>;
  subtotal?: Maybe<Scalars['Float']>;
  supplier_id: Scalars['ID'];
  total?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type InvoiceItem = {
  __typename?: 'InvoiceItem';
  checked_in?: Maybe<Scalars['Boolean']>;
  checked_in_quantity?: Maybe<Scalars['Int']>;
  cog_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice_id: Scalars['ID'];
  is_mispick?: Maybe<Scalars['Boolean']>;
  item_id?: Maybe<Scalars['ID']>;
  item_uom_id?: Maybe<Scalars['ID']>;
  metadata?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  price?: Maybe<Scalars['Float']>;
  qb_id?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  size?: Maybe<Scalars['String']>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<ItemUom>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  upc3?: Maybe<Scalars['ID']>;
  upc4?: Maybe<Scalars['ID']>;
};

export type InvoiceItemInput = {
  checked_in?: InputMaybe<Scalars['Boolean']>;
  checked_in_quantity?: InputMaybe<Scalars['Int']>;
  cog_price?: InputMaybe<Scalars['Float']>;
  id?: InputMaybe<Scalars['ID']>;
  invoice_id?: InputMaybe<Scalars['ID']>;
  is_mispick?: InputMaybe<Scalars['Boolean']>;
  itemUomId?: InputMaybe<Scalars['ID']>;
  item_id?: InputMaybe<Scalars['ID']>;
  name: Scalars['String'];
  price?: InputMaybe<Scalars['Float']>;
  qb_id?: InputMaybe<Scalars['String']>;
  quantity?: InputMaybe<Scalars['Int']>;
  size?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['String']>;
  upc1?: InputMaybe<Scalars['ID']>;
  upc2?: InputMaybe<Scalars['ID']>;
  upc3?: InputMaybe<Scalars['ID']>;
  upc4?: InputMaybe<Scalars['ID']>;
};

export type InvoiceItemMatch = {
  __typename?: 'InvoiceItemMatch';
  itemMatches?: Maybe<Array<Maybe<InvoiceItem>>>;
  matchStatus?: Maybe<MatchStatus>;
};

export type InvoiceWithStatus = {
  __typename?: 'InvoiceWithStatus';
  invoice?: Maybe<Invoice>;
  processing?: Maybe<Scalars['Boolean']>;
};

export type InvoicesBySupplier = {
  __typename?: 'InvoicesBySupplier';
  invoices: Array<Invoice>;
  paymentTotals: Scalars['JSONObject'];
  totalCases: Scalars['Int'];
  totalCount: Scalars['Int'];
  totalCredit: Scalars['Float'];
  totalProfit: Scalars['Float'];
};

export type Item = {
  __typename?: 'Item';
  archived?: Maybe<Scalars['Boolean']>;
  avg_cases_per_week?: Maybe<Scalars['Float']>;
  back_in_stock_date?: Maybe<Scalars['DateTime']>;
  cog_price?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  crv?: Maybe<Scalars['String']>;
  discounted_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  img_sm?: Maybe<Scalars['String']>;
  isFavorited?: Maybe<Scalars['Boolean']>;
  item_uom_id?: Maybe<Scalars['ID']>;
  last_ordered_date?: Maybe<Scalars['Float']>;
  local_item?: Maybe<Scalars['Boolean']>;
  metadata?: Maybe<Scalars['String']>;
  min_sale_price?: Maybe<Scalars['Float']>;
  moq?: Maybe<Scalars['Int']>;
  nacs_category?: Maybe<Scalars['String']>;
  nacs_subcategory?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  oos?: Maybe<Scalars['Boolean']>;
  outdated?: Maybe<Scalars['Boolean']>;
  price?: Maybe<Scalars['Float']>;
  promotions?: Maybe<Array<Maybe<Promotion>>>;
  qb_id?: Maybe<Scalars['String']>;
  qb_sync_token?: Maybe<Scalars['String']>;
  qoh?: Maybe<Scalars['Int']>;
  qty_on_hand?: Maybe<Scalars['Int']>;
  related_items?: Maybe<Array<Maybe<Item>>>;
  size?: Maybe<Scalars['String']>;
  supplier?: Maybe<Scalars['String']>;
  supplier_code?: Maybe<Scalars['String']>;
  supplier_info?: Maybe<Supplier>;
  tags?: Maybe<Array<Maybe<ItemTag>>>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<Array<Maybe<ItemUom>>>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type ItemAvailable = {
  __typename?: 'ItemAvailable';
  item_id?: Maybe<Scalars['ID']>;
  mapped?: Maybe<Scalars['Boolean']>;
  name?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  supplier?: Maybe<Scalars['String']>;
};

export type ItemAvailableInput = {
  name?: InputMaybe<Scalars['String']>;
  price?: InputMaybe<Scalars['Float']>;
  quantity?: InputMaybe<Scalars['Int']>;
  supplier?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['Int']>;
  upc?: InputMaybe<Scalars['String']>;
};

export type ItemInput = {
  back_in_stock_date?: InputMaybe<Scalars['DateTime']>;
  categoryIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  cog_price?: InputMaybe<Scalars['Float']>;
  crv?: InputMaybe<Scalars['String']>;
  discounted_price?: InputMaybe<Scalars['Float']>;
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['String']>;
  img_sm?: InputMaybe<Scalars['String']>;
  metadata?: InputMaybe<Scalars['String']>;
  min_sale_price?: InputMaybe<Scalars['Float']>;
  moq?: InputMaybe<Scalars['Int']>;
  nacs_category?: InputMaybe<Scalars['String']>;
  nacs_subcategory?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  oos?: InputMaybe<Scalars['Boolean']>;
  price?: InputMaybe<Scalars['Float']>;
  qb_id?: InputMaybe<Scalars['String']>;
  qb_sync_token?: InputMaybe<Scalars['String']>;
  qoh?: InputMaybe<Scalars['Int']>;
  qty_on_hand?: InputMaybe<Scalars['Int']>;
  size?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
  supplier_code?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['String']>;
  upc1?: InputMaybe<Scalars['ID']>;
  upc2?: InputMaybe<Scalars['ID']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
};

export enum ItemTag {
  Archived = 'ARCHIVED',
  New = 'NEW',
  Oos = 'OOS',
  Sale = 'SALE'
}

export type ItemUom = {
  __typename?: 'ItemUOM';
  archived?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  item_id: Scalars['ID'];
  item_uom_id: Scalars['ID'];
  name: Scalars['String'];
  price?: Maybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  supplier_id: Scalars['ID'];
  uom_id: Scalars['ID'];
  upc?: Maybe<Scalars['String']>;
};

export type ItemsFiltersV2 = {
  archived?: InputMaybe<Scalars['Boolean']>;
  brand?: InputMaybe<Scalars['String']>;
  category?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  section?: InputMaybe<Scalars['String']>;
  upc?: InputMaybe<Scalars['String']>;
};

export type ItemsInputV2 = {
  filters?: InputMaybe<ItemsFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
  userId?: InputMaybe<Scalars['ID']>;
};

export type ItemsOutputV2 = BaseOutput & {
  __typename?: 'ItemsOutputV2';
  items: Array<Maybe<Item>>;
  totalCount: Scalars['Int'];
};

export enum MatchStatus {
  Multiple = 'multiple',
  None = 'none',
  Single = 'single',
  Suggested = 'suggested'
}

export type Mutation = {
  __typename?: 'Mutation';
  addInvoice?: Maybe<Invoice>;
  addInvoiceItems?: Maybe<Array<Maybe<InvoiceItem>>>;
  addPushNotificationToken?: Maybe<PushNotificationToken>;
  applyPromotion: Order;
  approveUser?: Maybe<User>;
  captureTransaction?: Maybe<Scalars['String']>;
  createAccountFromBalance?: Maybe<Scalars['String']>;
  createBalanceTransaction?: Maybe<Scalars['String']>;
  createCart?: Maybe<Cart>;
  createCatalogTemplate: CatalogTemplate;
  createCredit?: Maybe<Credit>;
  createEmployee?: Maybe<Employee>;
  createGoal: Goal;
  createInvoice?: Maybe<Invoice>;
  createOrder?: Maybe<Order>;
  createPromotion: Promotion;
  createSupplier?: Maybe<Supplier>;
  createSupplierConfig: SupplierConfig;
  createUser?: Maybe<Array<Maybe<Scalars['String']>>>;
  deleteCatalogTemplate: Scalars['Boolean'];
  deleteCategory?: Maybe<Scalars['ID']>;
  deleteCredit?: Maybe<Scalars['ID']>;
  deleteGoal: Scalars['Boolean'];
  deleteItems?: Maybe<Array<Maybe<Scalars['ID']>>>;
  deleteRoute?: Maybe<Scalars['ID']>;
  deleteUsers?: Maybe<Array<Maybe<Scalars['ID']>>>;
  reconcileInvoiceWithItem?: Maybe<InvoiceItem>;
  reorderCategories: Scalars['Boolean'];
  sendReceivingReport?: Maybe<Scalars['String']>;
  startSync: Scalars['Boolean'];
  submitCreditRequests?: Maybe<Scalars['String']>;
  submitFeedback?: Maybe<Scalars['String']>;
  submitOrder?: Maybe<Order>;
  toggleFavorite: ToggleFavoriteResponse;
  updateCatalogTemplate: CatalogTemplate;
  updateCategoryOrder?: Maybe<Category>;
  updateCredit?: Maybe<Credit>;
  updateCreditRequestStatus?: Maybe<CreditRequest>;
  updateDefaultInAccount?: Maybe<Array<Maybe<Account>>>;
  updateEmployee?: Maybe<Employee>;
  updateGoal: Goal;
  updateInvoice?: Maybe<Invoice>;
  updateItemInCart?: Maybe<Cart>;
  updateOrder?: Maybe<Scalars['String']>;
  updatePromotion: Promotion;
  updateSupplierConfig: SupplierConfig;
  updateUser?: Maybe<User>;
  updateUserSuppliers?: Maybe<Array<Maybe<CutoffTime>>>;
  upsertAllCustomPrices: Scalars['Boolean'];
  upsertCategory?: Maybe<Category>;
  upsertCustomPrices?: Maybe<Array<Maybe<CustomPrice>>>;
  upsertItems?: Maybe<Array<Maybe<Item>>>;
  upsertRoute?: Maybe<Route>;
  upsertUOM?: Maybe<Array<Maybe<Uom>>>;
  upsertUOMAllCustomPrices: Scalars['Boolean'];
};


export type MutationAddInvoiceArgs = {
  addInvoiceInput: AddInvoiceInput;
};


export type MutationAddInvoiceItemsArgs = {
  addInvoiceItemsInput: AddInvoiceItemsInput;
};


export type MutationAddPushNotificationTokenArgs = {
  addPushNotificationTokenInput?: InputMaybe<AddPushNotificationTokenInput>;
};


export type MutationApplyPromotionArgs = {
  order_id: Scalars['ID'];
  promotion_id: Scalars['ID'];
  supplier_id: Scalars['ID'];
};


export type MutationApproveUserArgs = {
  id: Scalars['ID'];
};


export type MutationCaptureTransactionArgs = {
  captureTransactionInput?: InputMaybe<CaptureTransactionInput>;
};


export type MutationCreateAccountFromBalanceArgs = {
  payload: Scalars['String'];
};


export type MutationCreateBalanceTransactionArgs = {
  createBalanceTransactionInput: CreateBalanceTransactionInput;
};


export type MutationCreateCartArgs = {
  cart: CreateCartInput;
};


export type MutationCreateCatalogTemplateArgs = {
  input: CreateCatalogTemplateInput;
};


export type MutationCreateCreditArgs = {
  createCreditInput: CreateCreditInput;
};


export type MutationCreateEmployeeArgs = {
  input: CreateEmployeeInput;
};


export type MutationCreateGoalArgs = {
  createGoalInput: CreateGoalInput;
};


export type MutationCreateInvoiceArgs = {
  orderId: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type MutationCreateOrderArgs = {
  createOrderInput?: InputMaybe<CreateOrderInput>;
};


export type MutationCreatePromotionArgs = {
  input: CreatePromotionInput;
};


export type MutationCreateSupplierArgs = {
  input: CreateSupplierInput;
};


export type MutationCreateSupplierConfigArgs = {
  input: CreateSupplierConfigInput;
};


export type MutationCreateUserArgs = {
  createUserInput?: InputMaybe<CreateUserInput>;
};


export type MutationDeleteCatalogTemplateArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteCreditArgs = {
  deleteCreditInput: DeleteCreditInput;
};


export type MutationDeleteGoalArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteItemsArgs = {
  itemIds: Array<Scalars['ID']>;
};


export type MutationDeleteRouteArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteUsersArgs = {
  userIds: Array<Scalars['ID']>;
};


export type MutationReconcileInvoiceWithItemArgs = {
  reconcileInvoiceWithItemInput?: InputMaybe<ReconcileInvoiceWithItemInput>;
};


export type MutationReorderCategoriesArgs = {
  input: ReorderCategoriesInput;
};


export type MutationSendReceivingReportArgs = {
  invoiceId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};


export type MutationStartSyncArgs = {
  input: StartSyncInput;
};


export type MutationSubmitCreditRequestsArgs = {
  submitCreditRequestsInput?: InputMaybe<SubmitCreditRequestsInput>;
};


export type MutationSubmitFeedbackArgs = {
  submitFeedbackInput: SubmitFeedbackInput;
};


export type MutationSubmitOrderArgs = {
  submitOrderInput: SubmitOrderInput;
};


export type MutationToggleFavoriteArgs = {
  input: ToggleFavoriteInput;
};


export type MutationUpdateCatalogTemplateArgs = {
  input: UpdateCatalogTemplateInput;
};


export type MutationUpdateCategoryOrderArgs = {
  categoryId: Scalars['ID'];
  newOrder: Scalars['Int'];
  supplierId: Scalars['ID'];
};


export type MutationUpdateCreditArgs = {
  updateCreditInput: UpdateCreditInput;
};


export type MutationUpdateCreditRequestStatusArgs = {
  id: Scalars['ID'];
  status: Scalars['String'];
};


export type MutationUpdateDefaultInAccountArgs = {
  updateDefaultInAccountInput?: InputMaybe<UpdateDefaultInAccountInput>;
};


export type MutationUpdateEmployeeArgs = {
  input: UpdateEmployeeInput;
};


export type MutationUpdateGoalArgs = {
  updateGoalInput: UpdateGoalInput;
};


export type MutationUpdateInvoiceArgs = {
  updateInvoiceInput: UpdateInvoiceInput;
};


export type MutationUpdateItemInCartArgs = {
  updateItemInCartInput: UpdateItemInCartInput;
};


export type MutationUpdateOrderArgs = {
  updateOrderInput?: InputMaybe<UpdateOrderInput>;
};


export type MutationUpdatePromotionArgs = {
  input: UpdatePromotionInput;
};


export type MutationUpdateSupplierConfigArgs = {
  input: UpdateSupplierConfigInput;
};


export type MutationUpdateUserArgs = {
  user: UserInput;
};


export type MutationUpdateUserSuppliersArgs = {
  updateUserSuppliersInput?: InputMaybe<UpdateUserSuppliersInput>;
};


export type MutationUpsertAllCustomPricesArgs = {
  itemId: Scalars['ID'];
  overrideExistingPrices?: InputMaybe<Scalars['Boolean']>;
  price: Scalars['Float'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type MutationUpsertCategoryArgs = {
  categoryInput: CategoryInput;
  skipItems?: InputMaybe<Scalars['Boolean']>;
};


export type MutationUpsertCustomPricesArgs = {
  customPrices: Array<CustomPriceInput>;
};


export type MutationUpsertItemsArgs = {
  items: Array<ItemInput>;
  updateCategoryCustomPrices?: InputMaybe<Scalars['Boolean']>;
};


export type MutationUpsertRouteArgs = {
  routeInput: RouteInput;
};


export type MutationUpsertUomArgs = {
  input: UpsertUomInput;
};


export type MutationUpsertUomAllCustomPricesArgs = {
  itemId: Scalars['ID'];
  overrideExistingPrices?: InputMaybe<Scalars['Boolean']>;
  price: Scalars['Float'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type Order = {
  __typename?: 'Order';
  config?: Maybe<Scalars['JSONObject']>;
  customerDetails?: Maybe<User>;
  date_submitted?: Maybe<Scalars['Float']>;
  delivery_date?: Maybe<Scalars['Date']>;
  discount?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice?: Maybe<Invoice>;
  notes?: Maybe<Scalars['String']>;
  orderItems?: Maybe<Array<Maybe<CartItem>>>;
  orderName?: Maybe<Scalars['String']>;
  order_number?: Maybe<Scalars['Int']>;
  promotions?: Maybe<Array<Maybe<PromotionUsage>>>;
  sales_rep?: Maybe<SalesRep>;
  status?: Maybe<Scalars['String']>;
  subtotal: Scalars['Float'];
  supplier?: Maybe<Scalars['String']>;
  supplier_logo?: Maybe<Scalars['String']>;
  totalQuantity?: Maybe<Scalars['Int']>;
};

export type OrderBySupplier = {
  __typename?: 'OrderBySupplier';
  orderItems?: Maybe<Array<Maybe<CartItem>>>;
};

export type OrderItemInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  quantity: Scalars['Int'];
};

export type OrderItemTotal = {
  __typename?: 'OrderItemTotal';
  id: Scalars['ID'];
  nacs_category?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  next_day_presale_totals: Scalars['Int'];
  weekly_totals: Array<Scalars['Int']>;
};

export type OrderStatus = {
  __typename?: 'OrderStatus';
  delivering_date?: Maybe<Scalars['Float']>;
  delivery_date?: Maybe<Scalars['Float']>;
  id?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  order_id?: Maybe<Scalars['ID']>;
  submission_date?: Maybe<Scalars['Float']>;
  supplier_id?: Maybe<Scalars['ID']>;
};

export enum Ordering {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type OrdersBySupplier = {
  __typename?: 'OrdersBySupplier';
  orders: Array<Order>;
  totalCount: Scalars['Int'];
};

export type OrdersFiltersV2 = {
  deliveryDate?: InputMaybe<Scalars['Date']>;
  deliveryDateRange?: InputMaybe<Array<Scalars['Date']>>;
  driver?: InputMaybe<Scalars['String']>;
  employee_ids?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  lastPaidDate?: InputMaybe<Scalars['Date']>;
  paidStatus?: InputMaybe<Scalars['String']>;
  perUserLimit?: InputMaybe<Scalars['Int']>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
  userIds?: InputMaybe<Array<Scalars['String']>>;
};

export type OrdersInputV2 = {
  filters?: InputMaybe<OrdersFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type OrdersOutputV2 = BaseOutput & {
  __typename?: 'OrdersOutputV2';
  orders: Array<Maybe<Order>>;
  totalCount: Scalars['Int'];
};

export type PaginationInput = {
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
};

export type Promotion = {
  __typename?: 'Promotion';
  active: Scalars['Boolean'];
  applies_to_all_items: Scalars['Boolean'];
  applies_to_all_users: Scalars['Boolean'];
  archived: Scalars['Boolean'];
  buy_quantity?: Maybe<Scalars['Int']>;
  created_at: Scalars['DateTime'];
  discount_amount?: Maybe<Scalars['Float']>;
  discount_percentage?: Maybe<Scalars['Float']>;
  end_date: Scalars['DateTime'];
  free_quantity?: Maybe<Scalars['Int']>;
  id: Scalars['ID'];
  items?: Maybe<Array<Item>>;
  max_uses_per_customer?: Maybe<Scalars['Int']>;
  min_order_amount?: Maybe<Scalars['Float']>;
  name: Scalars['String'];
  promotion_type: PromotionType;
  start_date: Scalars['DateTime'];
  supplier_id: Scalars['ID'];
  total_usage_limit?: Maybe<Scalars['Int']>;
  updated_at: Scalars['DateTime'];
  usage_count: Scalars['Int'];
  users?: Maybe<Array<User>>;
};

export type PromotionApplication = {
  __typename?: 'PromotionApplication';
  affected_items: Array<AffectedItem>;
  applied_promotions: Array<Promotion>;
  total_discount: Scalars['Float'];
};

export enum PromotionCode {
  BuyXGetDiscount = 'BUY_X_GET_DISCOUNT',
  BuyXGetPrice = 'BUY_X_GET_PRICE',
  BuyXGetYFree = 'BUY_X_GET_Y_FREE',
  FixedDiscount = 'FIXED_DISCOUNT',
  PercentageOff = 'PERCENTAGE_OFF'
}

export type PromotionFilters = {
  active?: InputMaybe<Scalars['Boolean']>;
  dateRange?: InputMaybe<Array<Scalars['DateTime']>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  includeArchived?: InputMaybe<Scalars['Boolean']>;
  itemIds?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['ID']>;
  userIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type PromotionItemInput = {
  item_id: Scalars['ID'];
};

export type PromotionType = {
  __typename?: 'PromotionType';
  active: Scalars['Boolean'];
  code: PromotionCode;
  created_at: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  updated_at: Scalars['DateTime'];
};

export type PromotionUsage = {
  __typename?: 'PromotionUsage';
  created_at: Scalars['DateTime'];
  id: Scalars['ID'];
  order_id: Scalars['ID'];
  promotion: Promotion;
  promotion_id: Scalars['ID'];
  used_at: Scalars['DateTime'];
  user_id: Scalars['ID'];
};

export type PromotionUserInput = {
  user_id: Scalars['ID'];
};

export type PushNotificationToken = {
  __typename?: 'PushNotificationToken';
  token: Scalars['String'];
  user_id: Scalars['ID'];
};

export type Query = {
  __typename?: 'Query';
  accounts?: Maybe<Array<Maybe<Account>>>;
  actionItems?: Maybe<Array<Maybe<ActionItem>>>;
  activityLog: ActivityLogOutput;
  allCustomPricesV2: AllCustomPricesOutputV2;
  balanceLink?: Maybe<BalanceLink>;
  brandSections: Array<Maybe<Supplier>>;
  brandSpotlights?: Maybe<Array<Maybe<BrandSpotlights>>>;
  calculatePromotions: PromotionApplication;
  carts: Array<Maybe<Cart>>;
  categories: Array<Maybe<BasicCategory>>;
  categoriesBySupplier?: Maybe<Array<Maybe<Category>>>;
  creditRequests?: Maybe<Array<Maybe<CreditRequest>>>;
  credits: Array<Maybe<Credit>>;
  customUOMPrices: Array<Maybe<CustomUomPricesByItem>>;
  customerGroups?: Maybe<Array<Maybe<CustomerGroup>>>;
  customerHiddenProducts: CustomerHiddenProductsOutput;
  cutoffTimes: Array<Maybe<CutoffTime>>;
  dashboardMetrics?: Maybe<DashboardMetrics>;
  employeesV2: EmployeesOutputV2;
  expectedOrders: Array<Maybe<Order>>;
  getBestSellers: GetBestSellersOutput;
  getCatalogTemplates: Array<Maybe<CatalogTemplate>>;
  getFavorites: Array<Item>;
  getSyncingStatus: SyncStatusResponse;
  goal?: Maybe<Goal>;
  goalPeriods: Array<GoalPeriodOption>;
  goals: GoalsResponse;
  groupPricesV2: GroupPricesOutputV2;
  invoice?: Maybe<InvoiceWithStatus>;
  invoiceItemMatch?: Maybe<InvoiceItemMatch>;
  invoiceItems: Array<Maybe<InvoiceItem>>;
  invoices?: Maybe<Array<Maybe<Invoice>>>;
  invoicesBySupplier: InvoicesBySupplier;
  itemUOMs: Array<Maybe<ItemUom>>;
  items: Array<Maybe<Item>>;
  itemsAvailableDuffl?: Maybe<Array<Maybe<ItemAvailable>>>;
  itemsByFilter: Array<Maybe<Item>>;
  itemsBySupplier: Array<Maybe<Item>>;
  itemsV2: ItemsOutputV2;
  orderBySupplier?: Maybe<OrderBySupplier>;
  orderItemTotals: Array<OrderItemTotal>;
  orderStatuses?: Maybe<Array<Maybe<OrderStatus>>>;
  orders: Array<Maybe<Order>>;
  ordersBySupplier: OrdersBySupplier;
  ordersV2: OrdersOutputV2;
  promotion?: Maybe<Promotion>;
  promotionTypes: Array<PromotionType>;
  promotions: Array<Promotion>;
  pushNotificationTokens?: Maybe<Array<Maybe<PushNotificationToken>>>;
  recommendations: Array<Maybe<Recommendation>>;
  routeTotals: Array<RouteTotal>;
  routes?: Maybe<Array<Maybe<Route>>>;
  routesBySupplier?: Maybe<Array<Maybe<Route>>>;
  routesV2: Array<Maybe<Route>>;
  sections: Array<Maybe<Section>>;
  spotlights?: Maybe<Array<Maybe<Supplier>>>;
  supplierConfig: SupplierConfig;
  suppliers: Array<Maybe<Supplier>>;
  tags: Array<Maybe<Tag>>;
  uoms: Array<Maybe<Uom>>;
  uploadedOrderCsv?: Maybe<UploadedOrderCsv>;
  users: Array<Maybe<User>>;
  usersByFilter?: Maybe<UsersByFilter>;
  usersOnTodaysRoutes: Array<Maybe<User>>;
  usersV2: UsersOutputV2;
};


export type QueryAccountsArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
};


export type QueryActionItemsArgs = {
  getActionItemsInput?: InputMaybe<GetActionItemsInput>;
};


export type QueryActivityLogArgs = {
  activityLogInput: ActivityLogInput;
};


export type QueryAllCustomPricesV2Args = {
  allCustomPricesInput: AllCustomPricesInputV2;
};


export type QueryBalanceLinkArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
};


export type QueryBrandSectionsArgs = {
  getSectionsInput?: InputMaybe<GetSectionsInput>;
};


export type QueryBrandSpotlightsArgs = {
  userId?: InputMaybe<Scalars['ID']>;
};


export type QueryCalculatePromotionsArgs = {
  items: Array<OrderItemInput>;
  supplier_id: Scalars['ID'];
  user_id: Scalars['ID'];
};


export type QueryCartsArgs = {
  getCartsInput: GetCartsInput;
};


export type QueryCategoriesArgs = {
  getCategoriesInput?: InputMaybe<GetCategoriesInput>;
};


export type QueryCategoriesBySupplierArgs = {
  getCategoriesBySupplierInput?: InputMaybe<GetCategoriesBySupplierInput>;
};


export type QueryCreditRequestsArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};


export type QueryCreditsArgs = {
  getCreditsInput?: InputMaybe<GetCreditsInput>;
};


export type QueryCustomUomPricesArgs = {
  itemId: Scalars['ID'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type QueryCustomerGroupsArgs = {
  supplierId?: InputMaybe<Scalars['ID']>;
};


export type QueryCustomerHiddenProductsArgs = {
  customerHiddenProductsInput: CustomerHiddenProductsInput;
};


export type QueryCutoffTimesArgs = {
  getCutoffTimesInput?: InputMaybe<GetCutoffTimesInput>;
};


export type QueryDashboardMetricsArgs = {
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  serviceType?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
};


export type QueryEmployeesV2Args = {
  employeesInput: EmployeesInputV2;
};


export type QueryExpectedOrdersArgs = {
  date?: InputMaybe<Scalars['Date']>;
  supplierId: Scalars['ID'];
};


export type QueryGetBestSellersArgs = {
  getBestSellersInput: GetBestSellersInput;
};


export type QueryGetCatalogTemplatesArgs = {
  getCatalogTemplatesInput: GetCatalogTemplatesInput;
};


export type QueryGetFavoritesArgs = {
  input: GetFavoritesInput;
};


export type QueryGetSyncingStatusArgs = {
  supplierId: Scalars['ID'];
};


export type QueryGoalArgs = {
  id: Scalars['ID'];
};


export type QueryGoalPeriodsArgs = {
  goalId: Scalars['ID'];
};


export type QueryGoalsArgs = {
  goalsInput: GoalsInput;
};


export type QueryGroupPricesV2Args = {
  groupPricesInput: GroupPricesInputV2;
};


export type QueryInvoiceArgs = {
  orderId: Scalars['ID'];
};


export type QueryInvoiceItemMatchArgs = {
  getInvoiceItemMatchInput?: InputMaybe<GetInvoiceItemMatchInput>;
};


export type QueryInvoiceItemsArgs = {
  invoiceId?: InputMaybe<Scalars['ID']>;
  orderId?: InputMaybe<Scalars['ID']>;
};


export type QueryInvoicesArgs = {
  getInvoicesInput?: InputMaybe<GetInvoicesInput>;
};


export type QueryInvoicesBySupplierArgs = {
  getInvoicesBySupplierInput?: InputMaybe<GetInvoicesBySupplierInput>;
};


export type QueryItemUoMsArgs = {
  itemId: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type QueryItemsArgs = {
  getItemsInput?: InputMaybe<GetItemsInput>;
};


export type QueryItemsAvailableDufflArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
  items?: InputMaybe<Array<InputMaybe<ItemAvailableInput>>>;
  message?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
};


export type QueryItemsByFilterArgs = {
  getItemsByFilterInput?: InputMaybe<GetItemsByFilterInput>;
};


export type QueryItemsBySupplierArgs = {
  getItemsBySupplierInput?: InputMaybe<GetItemsBySupplierInput>;
};


export type QueryItemsV2Args = {
  itemsInput: ItemsInputV2;
};


export type QueryOrderBySupplierArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};


export type QueryOrderItemTotalsArgs = {
  getOrderItemTotalsInput?: InputMaybe<GetOrderItemTotalsInput>;
};


export type QueryOrderStatusesArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
};


export type QueryOrdersArgs = {
  getOrdersInput?: InputMaybe<GetOrdersInput>;
};


export type QueryOrdersBySupplierArgs = {
  getOrdersBySupplierInput?: InputMaybe<GetOrdersBySupplierInput>;
};


export type QueryOrdersV2Args = {
  ordersInput: OrdersInputV2;
};


export type QueryPromotionArgs = {
  id: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type QueryPromotionsArgs = {
  input?: InputMaybe<GetPromotionsInput>;
};


export type QueryPushNotificationTokensArgs = {
  getPushNotificationTokensInput?: InputMaybe<GetPushNotificationTokensInput>;
};


export type QueryRecommendationsArgs = {
  getRecommendationsInput?: InputMaybe<GetRecommendationsInput>;
};


export type QueryRouteTotalsArgs = {
  getRouteTotalsInput?: InputMaybe<GetRouteTotalsInput>;
};


export type QueryRoutesArgs = {
  getRoutesInput?: InputMaybe<GetRoutesInput>;
};


export type QueryRoutesBySupplierArgs = {
  getRoutesBySupplierInput?: InputMaybe<GetRoutesBySupplierInput>;
};


export type QueryRoutesV2Args = {
  routesInput: RoutesInputV2;
};


export type QuerySectionsArgs = {
  getSectionsInput?: InputMaybe<GetSectionsInput>;
};


export type QuerySpotlightsArgs = {
  userId?: InputMaybe<Scalars['ID']>;
};


export type QuerySupplierConfigArgs = {
  supplierId: Scalars['ID'];
};


export type QuerySuppliersArgs = {
  getSuppliersInput?: InputMaybe<GetSuppliersInput>;
};


export type QueryTagsArgs = {
  getTagsInput?: InputMaybe<GetTagsInput>;
};


export type QueryUomsArgs = {
  supplierId: Scalars['ID'];
};


export type QueryUploadedOrderCsvArgs = {
  embedId?: InputMaybe<Scalars['ID']>;
  sheetId?: InputMaybe<Scalars['ID']>;
};


export type QueryUsersArgs = {
  getUsersInput?: InputMaybe<GetUsersInput>;
};


export type QueryUsersByFilterArgs = {
  getUsersByFilterInput?: InputMaybe<GetUsersByFilterInput>;
};


export type QueryUsersOnTodaysRoutesArgs = {
  supplierId: Scalars['ID'];
};


export type QueryUsersV2Args = {
  usersInput: UsersInputV2;
};

export type Recommendation = {
  __typename?: 'Recommendation';
  id: Scalars['ID'];
  is_trending?: Maybe<Scalars['Boolean']>;
  item?: Maybe<Item>;
  item_id: Scalars['ID'];
  num_store?: Maybe<Scalars['Int']>;
  quantity?: Maybe<Scalars['Int']>;
  user_id: Scalars['ID'];
};

export type ReconcileInvoiceWithItemInput = {
  checked_in?: InputMaybe<Scalars['Boolean']>;
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_item_id?: InputMaybe<Scalars['ID']>;
  is_mispick?: InputMaybe<Scalars['Boolean']>;
  quantity?: InputMaybe<Scalars['Int']>;
};

export type ReorderCategoriesInput = {
  categoryOrders: Array<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type Role = {
  __typename?: 'Role';
  description?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
};

export type Route = {
  __typename?: 'Route';
  color: Scalars['String'];
  config?: Maybe<Scalars['JSONObject']>;
  day_of_week: Scalars['String'];
  driver?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type RouteInput = {
  color?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  day_of_week?: InputMaybe<Scalars['String']>;
  driver?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  supplier_id?: InputMaybe<Scalars['ID']>;
};

export type RouteTotal = {
  __typename?: 'RouteTotal';
  id: Scalars['ID'];
  name: Scalars['String'];
  weekly_totals: Array<Scalars['Int']>;
};

export type RoutesFiltersV2 = {
  routeId?: InputMaybe<Scalars['ID']>;
};

export type RoutesInputV2 = {
  filters?: InputMaybe<RoutesFiltersV2>;
  supplierId: Scalars['ID'];
};

export type SalesByRoute = {
  __typename?: 'SalesByRoute';
  number_of_stores: Scalars['Int'];
  rank: Scalars['Int'];
  route_name: Scalars['String'];
  total_sales_value: Scalars['Float'];
};

export type SalesRep = {
  __typename?: 'SalesRep';
  email?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  source: Scalars['String'];
};

export type Section = {
  __typename?: 'Section';
  image?: Maybe<Scalars['String']>;
  items?: Maybe<Array<Maybe<Item>>>;
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type SortBy = {
  field: Scalars['String'];
  ordering: Ordering;
};

export type StartSyncInput = {
  supplierId: Scalars['ID'];
  types: Array<SyncType>;
};

export type SubCart = {
  __typename?: 'SubCart';
  cartItems?: Maybe<Array<Maybe<CartItem>>>;
  discount?: Maybe<Scalars['Float']>;
  minimum?: Maybe<Scalars['Int']>;
  supplier?: Maybe<Scalars['String']>;
};

export type SubmitCreditRequestsInput = {
  creditRequests?: InputMaybe<Array<InputMaybe<CreditRequestInput>>>;
  orderId?: InputMaybe<Scalars['ID']>;
  supplier: Scalars['String'];
  userId: Scalars['ID'];
};

export type SubmitFeedbackInput = {
  issues?: InputMaybe<Scalars['String']>;
  thumbsUp: Scalars['Boolean'];
  userId: Scalars['ID'];
};

export type SubmitOrderInput = {
  cartId: Scalars['ID'];
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  discount?: InputMaybe<Scalars['Float']>;
  isCredit?: InputMaybe<Scalars['Boolean']>;
  notes?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
  userId: Scalars['ID'];
};

export type Supplier = {
  __typename?: 'Supplier';
  address?: Maybe<Scalars['String']>;
  config?: Maybe<Scalars['JSONObject']>;
  email?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  itemsPreview?: Maybe<Array<Maybe<Item>>>;
  logo?: Maybe<Scalars['String']>;
  minimum?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  need_signup?: Maybe<Scalars['Boolean']>;
  orderCount?: Maybe<Scalars['Int']>;
  phone_number?: Maybe<Scalars['String']>;
  qb_realm_id?: Maybe<Scalars['String']>;
  spotlight_image?: Maybe<Scalars['String']>;
};

export type SupplierConfig = {
  __typename?: 'SupplierConfig';
  allow_image_upload?: Maybe<Scalars['Boolean']>;
  auto_set_delivery_date?: Maybe<Scalars['Boolean']>;
  default_dashboard_duration?: Maybe<Scalars['String']>;
  enable_bounced_check_tracking?: Maybe<Scalars['Boolean']>;
  enable_catalog_sharing?: Maybe<Scalars['Boolean']>;
  enable_minimum_pricing?: Maybe<Scalars['Boolean']>;
  exclude_canceled_orders?: Maybe<Scalars['Boolean']>;
  filter_orders_by_sales_rep?: Maybe<Scalars['Boolean']>;
  invoice_scale_width?: Maybe<Scalars['Float']>;
  is_dsd?: Maybe<Scalars['Boolean']>;
  pavilions_address?: Maybe<Scalars['String']>;
  pavilions_display_name?: Maybe<Scalars['String']>;
  qb_credit_memo_date_filter?: Maybe<Scalars['String']>;
  requires_delivery_date?: Maybe<Scalars['Boolean']>;
  send_back_in_stock_notification?: Maybe<Scalars['Boolean']>;
  send_daily_back_in_stock_notification?: Maybe<Scalars['Boolean']>;
  send_order_notifications?: Maybe<Scalars['Boolean']>;
  show_goals_tracking_feature?: Maybe<Scalars['Boolean']>;
  show_open_carts?: Maybe<Scalars['Boolean']>;
  show_order_tabs?: Maybe<Scalars['Boolean']>;
  show_profit_info?: Maybe<Scalars['Boolean']>;
  skip_qb_sync?: Maybe<Scalars['Boolean']>;
  sort_invoice_items?: Maybe<Scalars['Boolean']>;
  spotlight_ids?: Maybe<Array<Maybe<Scalars['Int']>>>;
  use_alltime_avg_calculation?: Maybe<Scalars['Boolean']>;
  use_custom_driver_field?: Maybe<Scalars['Boolean']>;
};

export enum SyncStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  InProgress = 'IN_PROGRESS'
}

export type SyncStatusItem = {
  __typename?: 'SyncStatusItem';
  createdAt?: Maybe<Scalars['String']>;
  durationMs?: Maybe<Scalars['Int']>;
  endTime?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  lastSynced?: Maybe<Scalars['String']>;
  startTime?: Maybe<Scalars['String']>;
  status: Scalars['String'];
  type: SyncType;
  updatedAt?: Maybe<Scalars['String']>;
};

export type SyncStatusResponse = {
  __typename?: 'SyncStatusResponse';
  statuses: Array<SyncStatusItem>;
  supplierId: Scalars['ID'];
};

export enum SyncType {
  Customers = 'CUSTOMERS',
  Invoices = 'INVOICES',
  Items = 'ITEMS'
}

export type Tag = {
  __typename?: 'Tag';
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type ToggleFavoriteInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  itemId: Scalars['Int'];
  userId: Scalars['Int'];
};

export type ToggleFavoriteResponse = {
  __typename?: 'ToggleFavoriteResponse';
  isFavorited: Scalars['Boolean'];
  message?: Maybe<Scalars['String']>;
  success: Scalars['Boolean'];
};

export type Uom = {
  __typename?: 'UOM';
  archived?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type UomInput = {
  name: Scalars['String'];
  price?: InputMaybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  upc?: InputMaybe<Scalars['String']>;
};

export type UpdateCatalogTemplateInput = {
  config?: InputMaybe<CatalogTemplateConfigInput>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
};

export type UpdateCreditInput = {
  archived?: InputMaybe<Scalars['Boolean']>;
  credit_items?: InputMaybe<Array<InputMaybe<CreditItemInput>>>;
  id: Scalars['Int'];
  images?: InputMaybe<Array<Scalars['String']>>;
  invoice_id?: InputMaybe<Scalars['Int']>;
  status?: InputMaybe<CreditStatus>;
  supplier_id: Scalars['Int'];
  user_id?: InputMaybe<Scalars['Int']>;
};

export type UpdateDefaultInAccountInput = {
  accountId: Scalars['ID'];
  isDefault?: InputMaybe<Scalars['Boolean']>;
  userId: Scalars['ID'];
};

export type UpdateEmployeeInput = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  archived?: InputMaybe<Scalars['Boolean']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  lastLogin?: InputMaybe<Scalars['DateTime']>;
  name?: InputMaybe<Scalars['String']>;
  password?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  roleIds?: InputMaybe<Array<Scalars['ID']>>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  supplierId: Scalars['ID'];
};

export type UpdateGoalAssignmentInput = {
  employee_id?: InputMaybe<Scalars['ID']>;
  id?: InputMaybe<Scalars['ID']>;
  target_amount: Scalars['Float'];
};

export type UpdateGoalInput = {
  assignments?: InputMaybe<Array<UpdateGoalAssignmentInput>>;
  end_date?: InputMaybe<Scalars['Date']>;
  id: Scalars['ID'];
  is_active?: InputMaybe<Scalars['Boolean']>;
  name?: InputMaybe<Scalars['String']>;
  period?: InputMaybe<GoalPeriod>;
  start_date?: InputMaybe<Scalars['Date']>;
  target_amount?: InputMaybe<Scalars['Float']>;
  type?: InputMaybe<GoalType>;
};

export type UpdateInvoiceInput = {
  archived?: InputMaybe<Scalars['Boolean']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  credit?: InputMaybe<Scalars['Float']>;
  date_created?: InputMaybe<Scalars['DateTime']>;
  discount?: InputMaybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice_id?: InputMaybe<Scalars['String']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
  notes?: InputMaybe<Scalars['String']>;
  order_id?: InputMaybe<Scalars['ID']>;
  order_number?: InputMaybe<Scalars['Int']>;
  paid?: InputMaybe<Scalars['Float']>;
  payment_method?: InputMaybe<Scalars['String']>;
  payment_status?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  return_items?: InputMaybe<Scalars['Boolean']>;
  signature?: InputMaybe<Scalars['String']>;
  signature_name?: InputMaybe<Scalars['String']>;
  subtotal?: InputMaybe<Scalars['Float']>;
  supplier_id?: InputMaybe<Scalars['ID']>;
  total?: InputMaybe<Scalars['Float']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_id?: InputMaybe<Scalars['ID']>;
};

export type UpdateItemInCartInput = {
  cartId: Scalars['ID'];
  customPrice?: InputMaybe<Scalars['Float']>;
  itemId: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  notes?: InputMaybe<Scalars['String']>;
  quantity: Scalars['Int'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type UpdateOrderInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  invoice?: InputMaybe<UpdateInvoiceInput>;
  netTermsDays?: InputMaybe<Scalars['Int']>;
  notes?: InputMaybe<Scalars['String']>;
  orderId: Scalars['ID'];
  orderItems?: InputMaybe<Array<InputMaybe<UpdateOrderItemInput>>>;
  orderName?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<Scalars['String']>;
  subtotal?: InputMaybe<Scalars['Float']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId: Scalars['ID'];
};

export type UpdateOrderItemInput = {
  id: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  notes?: InputMaybe<Scalars['String']>;
  price_purchased_at: Scalars['Float'];
  quantity: Scalars['Int'];
};

export type UpdatePromotionInput = {
  active?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_items?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_users?: InputMaybe<Scalars['Boolean']>;
  archived?: InputMaybe<Scalars['Boolean']>;
  buy_quantity?: InputMaybe<Scalars['Int']>;
  discount_amount?: InputMaybe<Scalars['Float']>;
  discount_percentage?: InputMaybe<Scalars['Float']>;
  end_date?: InputMaybe<Scalars['DateTime']>;
  free_quantity?: InputMaybe<Scalars['Int']>;
  id: Scalars['ID'];
  items?: InputMaybe<Array<PromotionItemInput>>;
  max_uses_per_customer?: InputMaybe<Scalars['Int']>;
  min_order_amount?: InputMaybe<Scalars['Float']>;
  name?: InputMaybe<Scalars['String']>;
  promotion_type?: InputMaybe<Scalars['ID']>;
  start_date?: InputMaybe<Scalars['DateTime']>;
  supplier_id: Scalars['ID'];
  total_usage_limit?: InputMaybe<Scalars['Int']>;
  users?: InputMaybe<Array<PromotionUserInput>>;
};

export type UpdateSupplierConfigInput = {
  show_order_tabs?: InputMaybe<Scalars['Boolean']>;
  supplierId: Scalars['ID'];
};

export type UpdateUserSuppliersInput = {
  suppliers?: InputMaybe<Array<InputMaybe<UserSupplierInput>>>;
};

export type UploadedOrderCsv = {
  __typename?: 'UploadedOrderCsv';
  download_url?: Maybe<Scalars['String']>;
};

export type UpsertUomInput = {
  item_id: Scalars['ID'];
  supplier_id: Scalars['ID'];
  uoms: Array<UomInput>;
};

export type User = {
  __typename?: 'User';
  address?: Maybe<Scalars['String']>;
  approved?: Maybe<Scalars['Boolean']>;
  archived?: Maybe<Scalars['Boolean']>;
  config?: Maybe<Scalars['JSONObject']>;
  contact_email?: Maybe<Scalars['String']>;
  created_at?: Maybe<Scalars['DateTime']>;
  created_by?: Maybe<Scalars['String']>;
  custom_prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
  custom_uom_prices?: Maybe<Array<Maybe<CustomUomPricesByItem>>>;
  delivery_window?: Maybe<DeliveryWindow>;
  driver?: Maybe<Scalars['Boolean']>;
  ein?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  last_order_date?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  net_terms_days?: Maybe<Scalars['Int']>;
  open_cart?: Maybe<Cart>;
  phone_number?: Maybe<Scalars['String']>;
  qb_id?: Maybe<Scalars['String']>;
  route_id?: Maybe<Scalars['ID']>;
  store_group?: Maybe<Scalars['String']>;
  supplier_beta?: Maybe<Scalars['Boolean']>;
  suppliers?: Maybe<Array<Maybe<Supplier>>>;
  unpaid_balance?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  user_name?: Maybe<Scalars['String']>;
};

export type UserCustomPrices = {
  __typename?: 'UserCustomPrices';
  prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
  userId: Scalars['ID'];
};

export type UserInput = {
  address?: InputMaybe<Scalars['String']>;
  approved?: InputMaybe<Scalars['Boolean']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  contact_email?: InputMaybe<Scalars['String']>;
  custom_prices?: InputMaybe<Array<InputMaybe<CustomPriceWithoutUserIdInput>>>;
  custom_uom_prices?: InputMaybe<Array<InputMaybe<CustomUomPriceInput>>>;
  delivery_window?: InputMaybe<DeliveryWindowInput>;
  email?: InputMaybe<Scalars['String']>;
  hidden_products?: InputMaybe<Array<Scalars['ID']>>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  net_terms_days?: InputMaybe<Scalars['Int']>;
  phone_number?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  route_id?: InputMaybe<Scalars['ID']>;
  store_group?: InputMaybe<Scalars['String']>;
  supplier_beta?: InputMaybe<Scalars['Boolean']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_name?: InputMaybe<Scalars['String']>;
};

export type UserSupplierInput = {
  cutoffDay?: InputMaybe<Scalars['String']>;
  cutoffTime?: InputMaybe<Scalars['String']>;
  daysToDelivery?: InputMaybe<Scalars['Int']>;
  deliveryDay?: InputMaybe<Scalars['String']>;
  deliveryTime?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['String']>;
};

export type UsersByFilter = {
  __typename?: 'UsersByFilter';
  totalCount?: Maybe<Scalars['Float']>;
  users?: Maybe<Array<Maybe<User>>>;
};

export type UsersFiltersV2 = {
  active?: InputMaybe<Scalars['Boolean']>;
  drivers?: InputMaybe<Array<Scalars['String']>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  includeUnassigned?: InputMaybe<Scalars['Boolean']>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  searchTerm?: InputMaybe<Scalars['String']>;
};

export type UsersInputV2 = {
  filters?: InputMaybe<UsersFiltersV2>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type UsersOutputV2 = BaseOutput & {
  __typename?: 'UsersOutputV2';
  totalCount: Scalars['Int'];
  users: Array<Maybe<User>>;
};

export type AddPushNotificationTokenMutationVariables = Exact<{
  addPushNotificationTokenInput?: InputMaybe<AddPushNotificationTokenInput>;
}>;


export type AddPushNotificationTokenMutation = { __typename?: 'Mutation', addPushNotificationToken?: { __typename?: 'PushNotificationToken', token: string, user_id: string } | null };

export type CreateInvoiceMutationVariables = Exact<{
  orderId: Scalars['ID'];
  supplierId: Scalars['ID'];
}>;


export type CreateInvoiceMutation = { __typename?: 'Mutation', createInvoice?: { __typename?: 'Invoice', id?: string | null } | null };

export type CreateUserMutationVariables = Exact<{
  createUserInput?: InputMaybe<CreateUserInput>;
}>;


export type CreateUserMutation = { __typename?: 'Mutation', createUser?: Array<string | null> | null };

export type GetAccountsQueryVariables = Exact<{
  businessId?: InputMaybe<Scalars['ID']>;
}>;


export type GetAccountsQuery = { __typename?: 'Query', accounts?: Array<{ __typename?: 'Account', id?: string | null, type?: string | null, is_default?: boolean | null, balanceBankAccount?: { __typename?: 'BalanceBankAccount', institutionName?: string | null, accountName?: string | null, accountNumberMask?: string | null } | null, balanceCreditCard?: { __typename?: 'BalanceCreditCard', brand?: string | null, expiredMonth?: string | null, expiredYear?: string | null, last4?: string | null } | null } | null> | null };

export type GetActionItemsQueryVariables = Exact<{
  getActionItemsInput?: InputMaybe<GetActionItemsInput>;
}>;


export type GetActionItemsQuery = { __typename?: 'Query', actionItems?: Array<{ __typename?: 'ActionItem', description?: string | null, invoiceItem?: { __typename?: 'InvoiceItem', checked_in?: boolean | null, checked_in_quantity?: number | null, id: string, invoice_id: string, is_mispick?: boolean | null, name: string, price?: number | null, quantity?: number | null, size?: string | null, unit_size?: string | null, upc1?: string | null, upc2?: string | null, upc3?: string | null, upc4?: string | null } | null } | null> | null };

export type GetBalanceLinkQueryVariables = Exact<{
  businessId?: InputMaybe<Scalars['ID']>;
}>;


export type GetBalanceLinkQuery = { __typename?: 'Query', balanceLink?: { __typename?: 'BalanceLink', link?: string | null } | null };

export type GetBrandSectionsQueryVariables = Exact<{
  getSectionsInput?: InputMaybe<GetSectionsInput>;
}>;


export type GetBrandSectionsQuery = { __typename?: 'Query', brandSections: Array<{ __typename?: 'Supplier', id: string, logo?: string | null, name: string, spotlight_image?: string | null, itemsPreview?: Array<{ __typename?: 'Item', id: string, image?: string | null, supplier_code?: string | null, name: string, price?: number | null, discounted_price?: number | null, nacs_category?: string | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, supplier?: string | null, last_ordered_date?: number | null, avg_cases_per_week?: number | null, oos?: boolean | null, created_at?: any | null, metadata?: string | null, moq?: number | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> | null } | null> };

export type GetBrandSpotlightsQueryVariables = Exact<{
  userId?: InputMaybe<Scalars['ID']>;
}>;


export type GetBrandSpotlightsQuery = { __typename?: 'Query', brandSpotlights?: Array<{ __typename?: 'BrandSpotlights', id: string, name: string, spotlight_image?: string | null } | null> | null };

export type GetCartsQueryVariables = Exact<{
  getCartsInput: GetCartsInput;
}>;


export type GetCartsQuery = { __typename?: 'Query', carts: Array<{ __typename?: 'Cart', id: string, cartItems?: Array<{ __typename?: 'CartItem', id: string, image?: string | null, item_id: string, nacs_category?: string | null, nacs_subcategory?: string | null, name: string, oos?: boolean | null, price?: number | null, discounted_price?: number | null, custom_price?: number | null, quantity?: number | null, supplier?: string | null, supplier_code?: string | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, upc2?: string | null, metadata?: string | null, item_uom_id?: string | null, crv?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> | null, subCarts?: Array<{ __typename?: 'SubCart', supplier?: string | null, cartItems?: Array<{ __typename?: 'CartItem', id: string, image?: string | null, item_id: string, nacs_category?: string | null, nacs_subcategory?: string | null, name: string, oos?: boolean | null, price?: number | null, discounted_price?: number | null, custom_price?: number | null, quantity?: number | null, supplier?: string | null, supplier_code?: string | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, upc2?: string | null, metadata?: string | null, crv?: string | null, item_uom_id?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> | null } | null> | null } | null> };

export type GetCategoriesQueryVariables = Exact<{
  getCategoriesInput?: InputMaybe<GetCategoriesInput>;
}>;


export type GetCategoriesQuery = { __typename?: 'Query', categories: Array<{ __typename?: 'BasicCategory', image?: string | null, name?: string | null, value?: string | null } | null> };

export type GetCategoriesBySupplierQueryVariables = Exact<{
  getCategoriesBySupplierInput?: InputMaybe<GetCategoriesBySupplierInput>;
}>;


export type GetCategoriesBySupplierQuery = { __typename?: 'Query', categoriesBySupplier?: Array<{ __typename?: 'Category', id: string, image?: string | null, name: string, ordering?: number | null, items?: Array<{ __typename?: 'Item', id: string } | null> | null } | null> | null };

export type GetCutoffTimesQueryVariables = Exact<{
  getCutoffTimesInput?: InputMaybe<GetCutoffTimesInput>;
}>;


export type GetCutoffTimesQuery = { __typename?: 'Query', cutoffTimes: Array<{ __typename?: 'CutoffTime', id?: string | null, supplier: string, cutoffDay?: string | null, cutoffTime?: string | null, deliveryDay?: string | null, deliveryTime?: string | null, daysToDelivery?: number | null, supplierInfo?: { __typename?: 'Supplier', id: string, logo?: string | null, name: string, need_signup?: boolean | null } | null } | null> };

export type GetExpectedOrdersQueryVariables = Exact<{
  supplierId: Scalars['ID'];
}>;


export type GetExpectedOrdersQuery = { __typename?: 'Query', expectedOrders: Array<{ __typename?: 'Order', id: string, status?: string | null, delivery_date?: any | null, customerDetails?: { __typename?: 'User', id: string, config?: any | null } | null } | null> };

export type GetFavoritesQueryVariables = Exact<{
  input: GetFavoritesInput;
}>;


export type GetFavoritesQuery = { __typename?: 'Query', getFavorites: Array<{ __typename?: 'Item', id: string, name: string, price?: number | null, image?: string | null }> };

export type CustomerHiddenProductsQueryVariables = Exact<{
  customerHiddenProductsInput: CustomerHiddenProductsInput;
}>;


export type CustomerHiddenProductsQuery = { __typename?: 'Query', customerHiddenProducts: { __typename?: 'CustomerHiddenProductsOutput', items: Array<{ __typename?: 'CustomerHiddenProduct', itemId: string } | null> } };

export type GetInvoiceItemMatchQueryVariables = Exact<{
  getInvoiceItemMatchInput?: InputMaybe<GetInvoiceItemMatchInput>;
}>;


export type GetInvoiceItemMatchQuery = { __typename?: 'Query', invoiceItemMatch?: { __typename?: 'InvoiceItemMatch', matchStatus?: MatchStatus | null, itemMatches?: Array<{ __typename?: 'InvoiceItem', checked_in?: boolean | null, checked_in_quantity?: number | null, id: string, invoice_id: string, name: string, is_mispick?: boolean | null, price?: number | null, quantity?: number | null, size?: string | null, unit_size?: string | null, upc1?: string | null, upc2?: string | null, upc3?: string | null, upc4?: string | null } | null> | null } | null };

export type GetInvoiceItemsQueryVariables = Exact<{
  orderId?: InputMaybe<Scalars['ID']>;
  invoiceId?: InputMaybe<Scalars['ID']>;
}>;


export type GetInvoiceItemsQuery = { __typename?: 'Query', invoiceItems: Array<{ __typename?: 'InvoiceItem', upc1?: string | null, upc2?: string | null, upc3?: string | null, upc4?: string | null, checked_in?: boolean | null, checked_in_quantity?: number | null, id: string, invoice_id: string, is_mispick?: boolean | null, name: string, price?: number | null, quantity?: number | null, size?: string | null, unit_size?: string | null, item_id?: string | null, item_uom_id?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> };

export type GetInvoiceQueryVariables = Exact<{
  orderId: Scalars['ID'];
}>;


export type GetInvoiceQuery = { __typename?: 'Query', invoice?: { __typename?: 'InvoiceWithStatus', processing?: boolean | null, invoice?: { __typename?: 'Invoice', date_received?: number | null, id?: string | null, invoice_id?: string | null, order_id?: string | null, supplier_id: string, total?: number | null } | null } | null };

export type GetItemsQueryVariables = Exact<{
  getItemsInput?: InputMaybe<GetItemsInput>;
}>;


export type GetItemsQuery = { __typename?: 'Query', items: Array<{ __typename?: 'Item', id: string, name: string, unit_size?: string | null, size?: string | null, price?: number | null, discounted_price?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, supplier?: string | null, image?: string | null, qoh?: number | null, qty_on_hand?: number | null, last_ordered_date?: number | null, avg_cases_per_week?: number | null, oos?: boolean | null, created_at?: any | null, metadata?: string | null, back_in_stock_date?: any | null, moq?: number | null, crv?: string | null, min_sale_price?: number | null, isFavorited?: boolean | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null, supplier_info?: { __typename?: 'Supplier', id: string, name: string, need_signup?: boolean | null } | null, related_items?: Array<{ __typename?: 'Item', id: string, name: string, unit_size?: string | null, price?: number | null, discounted_price?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, supplier?: string | null, image?: string | null, last_ordered_date?: number | null, qoh?: number | null, qty_on_hand?: number | null, oos?: boolean | null, created_at?: any | null, isFavorited?: boolean | null, back_in_stock_date?: any | null } | null> | null } | null> };

export type GetItemsByFilterQueryVariables = Exact<{
  getItemsByFilterInput?: InputMaybe<GetItemsByFilterInput>;
}>;


export type GetItemsByFilterQuery = { __typename?: 'Query', itemsByFilter: Array<{ __typename?: 'Item', id: string, name: string, unit_size?: string | null, price?: number | null, discounted_price?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, last_ordered_date?: number | null, avg_cases_per_week?: number | null, oos?: boolean | null, created_at?: any | null, qoh?: number | null, metadata?: string | null, back_in_stock_date?: any | null } | null> };

export type GetItemsBySupplierQueryVariables = Exact<{
  getItemsBySupplierInput?: InputMaybe<GetItemsBySupplierInput>;
}>;


export type GetItemsBySupplierQuery = { __typename?: 'Query', itemsBySupplier: Array<{ __typename?: 'Item', id: string, name: string, unit_size?: string | null, size?: string | null, price?: number | null, discounted_price?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, last_ordered_date?: number | null, avg_cases_per_week?: number | null, supplier?: string | null, qoh?: number | null, qty_on_hand?: number | null, oos?: boolean | null, created_at?: any | null, metadata?: string | null, moq?: number | null, min_sale_price?: number | null, isFavorited?: boolean | null, back_in_stock_date?: any | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> };

export type GetItemsV2QueryVariables = Exact<{
  itemsInputV2: ItemsInputV2;
}>;


export type GetItemsV2Query = { __typename?: 'Query', itemsV2: { __typename?: 'ItemsOutputV2', totalCount: number, items: Array<{ __typename?: 'Item', id: string, name: string, unit_size?: string | null, qoh?: number | null, price?: number | null, discounted_price?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, supplier_code?: string | null, supplier?: string | null, last_ordered_date?: number | null, avg_cases_per_week?: number | null, size?: string | null, tags?: Array<ItemTag | null> | null, oos?: boolean | null, local_item?: boolean | null, outdated?: boolean | null, created_at?: any | null, archived?: boolean | null, qb_id?: string | null, qb_sync_token?: string | null, updated_at?: any | null, metadata?: string | null, crv?: string | null, qty_on_hand?: number | null, cog_price?: number | null, moq?: number | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> } };

export type GetOrderBySupplierQueryVariables = Exact<{
  orderId?: InputMaybe<Scalars['ID']>;
  supplierId?: InputMaybe<Scalars['ID']>;
}>;


export type GetOrderBySupplierQuery = { __typename?: 'Query', orderBySupplier?: { __typename?: 'OrderBySupplier', orderItems?: Array<{ __typename?: 'CartItem', id: string, name: string, supplier?: string | null, unit_size?: string | null, price?: number | null, discounted_price?: number | null, price_purchased_at?: number | null, upc1?: string | null, upc2?: string | null, item_uom_id?: string | null, nacs_subcategory?: string | null, nacs_category?: string | null, quantity?: number | null, item_id: string, image?: string | null, supplier_code?: string | null, qoh?: number | null, metadata?: string | null, crv?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> | null } | null };

export type GetOrderStatusesQueryVariables = Exact<{
  orderId?: InputMaybe<Scalars['ID']>;
}>;


export type GetOrderStatusesQuery = { __typename?: 'Query', orderStatuses?: Array<{ __typename?: 'OrderStatus', id?: string | null, order_id?: string | null, supplier_id?: string | null, delivery_date?: number | null, delivering_date?: number | null, submission_date?: number | null, name?: string | null } | null> | null };

export type GetOrdersQueryVariables = Exact<{
  getOrdersInput?: InputMaybe<GetOrdersInput>;
}>;


export type GetOrdersQuery = { __typename?: 'Query', orders: Array<{ __typename?: 'Order', id: string, order_number?: number | null, status?: string | null, subtotal: number, date_submitted?: number | null, orderName?: string | null, delivery_date?: any | null, supplier?: string | null, supplier_logo?: string | null, orderItems?: Array<{ __typename?: 'CartItem', id: string, name: string, unit_size?: string | null, price?: number | null, discounted_price?: number | null, price_purchased_at?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, quantity?: number | null, item_id: string, image?: string | null, oos?: boolean | null, qoh?: number | null, metadata?: string | null, supplier?: string | null } | null> | null, invoice?: { __typename?: 'Invoice', id?: string | null, discount?: number | null, signature?: string | null, signature_name?: string | null, paid?: number | null, credit?: number | null, payment_method?: string | null, payment_status?: string | null, notes?: string | null, subtotal?: number | null, total?: number | null } | null } | null> };

export type GetOrdersBySupplierQueryVariables = Exact<{
  getOrdersBySupplierInput?: InputMaybe<GetOrdersBySupplierInput>;
}>;


export type GetOrdersBySupplierQuery = { __typename?: 'Query', ordersBySupplier: { __typename?: 'OrdersBySupplier', orders: Array<{ __typename?: 'Order', id: string, order_number?: number | null, status?: string | null, subtotal: number, date_submitted?: number | null, orderName?: string | null, config?: any | null, delivery_date?: any | null, supplier?: string | null, supplier_logo?: string | null, customerDetails?: { __typename?: 'User', id: string, name?: string | null } | null, orderItems?: Array<{ __typename?: 'CartItem', id: string, name: string, unit_size?: string | null, price?: number | null, discounted_price?: number | null, price_purchased_at?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, quantity?: number | null, item_id: string, image?: string | null, oos?: boolean | null, qoh?: number | null, metadata?: string | null, supplier?: string | null, archived?: boolean | null, crv?: string | null } | null> | null, invoice?: { __typename?: 'Invoice', id?: string | null, discount?: number | null, signature?: string | null, signature_name?: string | null, paid?: number | null, credit?: number | null, payment_method?: string | null, notes?: string | null, subtotal?: number | null, total?: number | null, config?: any | null } | null }> } };

export type GetOrdersV2QueryVariables = Exact<{
  ordersV2Input: OrdersInputV2;
}>;


export type GetOrdersV2Query = { __typename?: 'Query', ordersV2: { __typename?: 'OrdersOutputV2', orders: Array<{ __typename?: 'Order', id: string, status?: string | null, subtotal: number, date_submitted?: number | null, orderName?: string | null, config?: any | null, delivery_date?: any | null, supplier?: string | null, supplier_logo?: string | null, customerDetails?: { __typename?: 'User', id: string, name?: string | null } | null, invoice?: { __typename?: 'Invoice', id?: string | null, discount?: number | null, paid?: number | null, credit?: number | null, payment_method?: string | null, payment_status?: string | null, subtotal?: number | null, total?: number | null, config?: any | null } | null } | null> } };

export type GetRoutesQueryVariables = Exact<{
  getRoutesBySupplierInput?: InputMaybe<GetRoutesBySupplierInput>;
}>;


export type GetRoutesQuery = { __typename?: 'Query', routesBySupplier?: Array<{ __typename?: 'Route', id: string, name: string, driver?: string | null, day_of_week: string, color: string } | null> | null };

export type GetSectionsQueryVariables = Exact<{
  getSectionsInput?: InputMaybe<GetSectionsInput>;
}>;


export type GetSectionsQuery = { __typename?: 'Query', sections: Array<{ __typename?: 'Section', value?: string | null, name?: string | null, items?: Array<{ __typename?: 'Item', id: string, name: string, price?: number | null, discounted_price?: number | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, supplier?: string | null, last_ordered_date?: number | null, oos?: boolean | null, created_at?: any | null, metadata?: string | null, avg_cases_per_week?: number | null, moq?: number | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> | null } | null> };

export type GetSupplierConfigQueryVariables = Exact<{
  supplierId: Scalars['ID'];
}>;


export type GetSupplierConfigQuery = { __typename?: 'Query', supplierConfig: { __typename?: 'SupplierConfig', is_dsd?: boolean | null, send_order_notifications?: boolean | null, invoice_scale_width?: number | null, allow_image_upload?: boolean | null, show_order_tabs?: boolean | null, auto_set_delivery_date?: boolean | null, use_alltime_avg_calculation?: boolean | null, exclude_canceled_orders?: boolean | null, skip_qb_sync?: boolean | null, requires_delivery_date?: boolean | null, spotlight_ids?: Array<number | null> | null, qb_credit_memo_date_filter?: string | null, pavilions_display_name?: string | null, pavilions_address?: string | null, enable_minimum_pricing?: boolean | null, enable_catalog_sharing?: boolean | null } };

export type GetSupplierConfigByIdQueryVariables = Exact<{
  supplierId: Scalars['ID'];
}>;


export type GetSupplierConfigByIdQuery = { __typename?: 'Query', supplierConfig: { __typename?: 'SupplierConfig', enable_bounced_check_tracking?: boolean | null, show_goals_tracking_feature?: boolean | null } };

export type GetUserGoalsQueryVariables = Exact<{
  goalsInput: GoalsInput;
}>;


export type GetUserGoalsQuery = { __typename?: 'Query', goals: { __typename?: 'GoalsResponse', totalCount: number, goals: Array<{ __typename?: 'Goal', id: string, supplier_id: string, name: string, type: GoalType, period: GoalPeriod, target_amount: number, start_date: any, end_date?: any | null, is_active: boolean, status: GoalStatus, created_at: any, updated_at: any, available_periods: Array<{ __typename?: 'GoalPeriodOption', label: string, value: string }>, assignments: Array<{ __typename?: 'GoalAssignment', id: string, goal_id: string, employee_id?: string | null, target_amount: number, current_progress: number, percentage_complete: number, created_at: any, updated_at: any, employee?: { __typename?: 'Employee', id: string, name: string, email: string } | null }> }> } };

export type GetUsersQueryVariables = Exact<{
  getUsersInput?: InputMaybe<GetUsersInput>;
}>;


export type GetUsersQuery = { __typename?: 'Query', users: Array<{ __typename?: 'User', id: string, name?: string | null, user_name?: string | null, supplier_beta?: boolean | null, approved?: boolean | null, driver?: boolean | null, address?: string | null, store_group?: string | null, route_id?: string | null, config?: any | null, net_terms_days?: number | null, suppliers?: Array<{ __typename?: 'Supplier', id: string, logo?: string | null, name: string, minimum?: number | null, phone_number?: string | null, address?: string | null, config?: any | null, orderCount?: number | null } | null> | null, delivery_window?: { __typename?: 'DeliveryWindow', days_of_week?: Array<string | null> | null, start_time?: string | null, end_time?: string | null } | null, custom_prices?: Array<{ __typename?: 'CustomPriceWithoutUserId', item_id: string, price: number } | null> | null, custom_uom_prices?: Array<{ __typename?: 'CustomUOMPricesByItem', item_id: string, uom_prices: Array<{ __typename?: 'CustomUOMPrice', item_uom_id?: string | null, user_id: string, price: number, uom_name: string, uom_id?: string | null }> } | null> | null } | null> };

export type GetUsersOnTodaysRoutesQueryVariables = Exact<{
  supplierId: Scalars['ID'];
}>;


export type GetUsersOnTodaysRoutesQuery = { __typename?: 'Query', usersOnTodaysRoutes: Array<{ __typename?: 'User', id: string, name?: string | null, user_name?: string | null, email?: string | null, driver?: boolean | null, route_id?: string | null, approved?: boolean | null } | null> };

export type ReconcileInvoiceWithItemMutationVariables = Exact<{
  reconcileInvoiceWithItemInput?: InputMaybe<ReconcileInvoiceWithItemInput>;
}>;


export type ReconcileInvoiceWithItemMutation = { __typename?: 'Mutation', reconcileInvoiceWithItem?: { __typename?: 'InvoiceItem', checked_in?: boolean | null, checked_in_quantity?: number | null, id: string, invoice_id: string, is_mispick?: boolean | null, name: string, quantity?: number | null, size?: string | null, price?: number | null, unit_size?: string | null, upc1?: string | null, upc4?: string | null, upc2?: string | null, upc3?: string | null } | null };

export type SendReceivingReportMutationVariables = Exact<{
  userId?: InputMaybe<Scalars['ID']>;
  invoiceId?: InputMaybe<Scalars['ID']>;
}>;


export type SendReceivingReportMutation = { __typename?: 'Mutation', sendReceivingReport?: string | null };

export type SubmitCreditRequestsMutationVariables = Exact<{
  submitCreditRequestsInput?: InputMaybe<SubmitCreditRequestsInput>;
}>;


export type SubmitCreditRequestsMutation = { __typename?: 'Mutation', submitCreditRequests?: string | null };

export type SubmitFeedbackMutationVariables = Exact<{
  submitFeedbackInput: SubmitFeedbackInput;
}>;


export type SubmitFeedbackMutation = { __typename?: 'Mutation', submitFeedback?: string | null };

export type SubmitOrderMutationVariables = Exact<{
  submitOrderInput: SubmitOrderInput;
}>;


export type SubmitOrderMutation = { __typename?: 'Mutation', submitOrder?: { __typename?: 'Order', id: string, status?: string | null, subtotal: number, order_number?: number | null } | null };

export type ToggleFavoriteMutationVariables = Exact<{
  input: ToggleFavoriteInput;
}>;


export type ToggleFavoriteMutation = { __typename?: 'Mutation', toggleFavorite: { __typename?: 'ToggleFavoriteResponse', success: boolean, isFavorited: boolean, message?: string | null } };

export type UpdateDefaultInAccountMutationVariables = Exact<{
  updateDefaultInAccountInput?: InputMaybe<UpdateDefaultInAccountInput>;
}>;


export type UpdateDefaultInAccountMutation = { __typename?: 'Mutation', updateDefaultInAccount?: Array<{ __typename?: 'Account', id?: string | null, is_default?: boolean | null } | null> | null };

export type UpdateEmployeeMutationVariables = Exact<{
  input: UpdateEmployeeInput;
}>;


export type UpdateEmployeeMutation = { __typename?: 'Mutation', updateEmployee?: { __typename?: 'Employee', id: string, name: string, email: string, phone?: string | null, app_access: boolean, dashboard_access: boolean, archived: boolean, roles?: Array<{ __typename?: 'Role', id: string, name: string }> | null } | null };

export type UpdateInvoiceMutationVariables = Exact<{
  updateInvoiceInput: UpdateInvoiceInput;
}>;


export type UpdateInvoiceMutation = { __typename?: 'Mutation', updateInvoice?: { __typename?: 'Invoice', id?: string | null } | null };

export type UpdateItemInCartMutationVariables = Exact<{
  updateItemInCartInput: UpdateItemInCartInput;
}>;


export type UpdateItemInCartMutation = { __typename?: 'Mutation', updateItemInCart?: { __typename?: 'Cart', id: string, cartItems?: Array<{ __typename?: 'CartItem', id: string, image?: string | null, item_id: string, item_uom_id?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, name: string, oos?: boolean | null, price?: number | null, discounted_price?: number | null, price_purchased_at?: number | null, custom_price?: number | null, quantity?: number | null, supplier?: string | null, supplier_code?: string | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, upc2?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> | null, subCarts?: Array<{ __typename?: 'SubCart', supplier?: string | null, cartItems?: Array<{ __typename?: 'CartItem', id: string, image?: string | null, item_id: string, item_uom_id?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, name: string, oos?: boolean | null, price?: number | null, discounted_price?: number | null, custom_price?: number | null, quantity?: number | null, supplier?: string | null, supplier_code?: string | null, unit_size?: string | null, qoh?: number | null, upc1?: string | null, upc2?: string | null, uoms?: { __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null } | null> | null } | null> | null } | null };

export type UpdateOrderMutationVariables = Exact<{
  updateOrderInput?: InputMaybe<UpdateOrderInput>;
}>;


export type UpdateOrderMutation = { __typename?: 'Mutation', updateOrder?: string | null };

export type UpdateUserSuppliersMutationVariables = Exact<{
  updateUserSuppliersInput?: InputMaybe<UpdateUserSuppliersInput>;
}>;


export type UpdateUserSuppliersMutation = { __typename?: 'Mutation', updateUserSuppliers?: Array<{ __typename?: 'CutoffTime', cutoffDay?: string | null, cutoffTime?: string | null, daysToDelivery?: number | null, deliveryDay?: string | null, deliveryTime?: string | null, id?: string | null, supplier: string, supplierInfo?: { __typename?: 'Supplier', id: string, logo?: string | null, name: string, need_signup?: boolean | null, spotlight_image?: string | null } | null } | null> | null };

export type GetTagsQueryVariables = Exact<{
  getTagsInput?: InputMaybe<GetTagsInput>;
}>;


export type GetTagsQuery = { __typename?: 'Query', tags: Array<{ __typename?: 'Tag', name?: string | null, value?: string | null } | null> };

export type HomeScreenQueryVariables = Exact<{
  userId?: InputMaybe<Scalars['ID']>;
  getCategoriesInput?: InputMaybe<GetCategoriesInput>;
  getSectionsInput?: InputMaybe<GetSectionsInput>;
  getRecommendationsInput?: InputMaybe<GetRecommendationsInput>;
}>;


export type HomeScreenQuery = { __typename?: 'Query', categories: Array<{ __typename?: 'BasicCategory', image?: string | null, name?: string | null, value?: string | null } | null>, brandSpotlights?: Array<{ __typename?: 'BrandSpotlights', id: string, name: string, spotlight_image?: string | null } | null> | null, spotlights?: Array<{ __typename?: 'Supplier', id: string, logo?: string | null, name: string, spotlight_image?: string | null } | null> | null, sections: Array<{ __typename?: 'Section', value?: string | null, name?: string | null, items?: Array<{ __typename?: 'Item', id: string, name: string, price?: number | null, discounted_price?: number | null, unit_size?: string | null, qoh?: number | null, qty_on_hand?: number | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, supplier?: string | null, oos?: boolean | null, back_in_stock_date?: any | null, created_at?: any | null, metadata?: string | null, crv?: string | null, avg_cases_per_week?: number | null, moq?: number | null, item_uom_id?: string | null, isFavorited?: boolean | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> | null } | null>, brandSections: Array<{ __typename?: 'Supplier', id: string, logo?: string | null, name: string, spotlight_image?: string | null, itemsPreview?: Array<{ __typename?: 'Item', id: string, image?: string | null, supplier_code?: string | null, name: string, price?: number | null, discounted_price?: number | null, nacs_category?: string | null, unit_size?: string | null, qoh?: number | null, qty_on_hand?: number | null, upc1?: string | null, last_ordered_date?: number | null, oos?: boolean | null, back_in_stock_date?: any | null, created_at?: any | null, avg_cases_per_week?: number | null, moq?: number | null, item_uom_id?: string | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null> | null } | null>, recommendations: Array<{ __typename?: 'Recommendation', num_store?: number | null, quantity?: number | null, item?: { __typename?: 'Item', id: string, name: string, price?: number | null, discounted_price?: number | null, unit_size?: string | null, upc1?: string | null, upc2?: string | null, nacs_category?: string | null, nacs_subcategory?: string | null, image?: string | null, supplier?: string | null, last_ordered_date?: number | null, oos?: boolean | null, back_in_stock_date?: any | null, created_at?: any | null, moq?: number | null, item_uom_id?: string | null, uoms?: Array<{ __typename?: 'ItemUOM', id: string, name: string, supplier_id: string, uom_id: string, quantity: number, item_id: string, price?: number | null, upc?: string | null, archived?: boolean | null } | null> | null } | null } | null> };

export type GetCatalogTemplatesQueryVariables = Exact<{
  getCatalogTemplatesInput: GetCatalogTemplatesInput;
}>;


export type GetCatalogTemplatesQuery = { __typename?: 'Query', getCatalogTemplates: Array<{ __typename?: 'CatalogTemplate', id: string, config: any, name: string, created_at: any, updated_at: any } | null> };

export type GetOrdersInfoQueryVariables = Exact<{
  ordersV2Input: OrdersInputV2;
}>;


export type GetOrdersInfoQuery = { __typename?: 'Query', ordersV2: { __typename?: 'OrdersOutputV2', orders: Array<{ __typename?: 'Order', id: string, status?: string | null, subtotal: number, config?: any | null, delivery_date?: any | null, customerDetails?: { __typename?: 'User', id: string, name?: string | null } | null, invoice?: { __typename?: 'Invoice', id?: string | null, payment_status?: string | null, paid?: number | null, subtotal?: number | null, total?: number | null, config?: any | null } | null } | null> } };


export const AddPushNotificationTokenDocument = gql`
    mutation AddPushNotificationToken($addPushNotificationTokenInput: AddPushNotificationTokenInput) {
  addPushNotificationToken(
    addPushNotificationTokenInput: $addPushNotificationTokenInput
  ) {
    token
    user_id
  }
}
    `;
export type AddPushNotificationTokenMutationFn = Apollo.MutationFunction<AddPushNotificationTokenMutation, AddPushNotificationTokenMutationVariables>;

/**
 * __useAddPushNotificationTokenMutation__
 *
 * To run a mutation, you first call `useAddPushNotificationTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddPushNotificationTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addPushNotificationTokenMutation, { data, loading, error }] = useAddPushNotificationTokenMutation({
 *   variables: {
 *      addPushNotificationTokenInput: // value for 'addPushNotificationTokenInput'
 *   },
 * });
 */
export function useAddPushNotificationTokenMutation(baseOptions?: Apollo.MutationHookOptions<AddPushNotificationTokenMutation, AddPushNotificationTokenMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddPushNotificationTokenMutation, AddPushNotificationTokenMutationVariables>(AddPushNotificationTokenDocument, options);
      }
export type AddPushNotificationTokenMutationHookResult = ReturnType<typeof useAddPushNotificationTokenMutation>;
export type AddPushNotificationTokenMutationResult = Apollo.MutationResult<AddPushNotificationTokenMutation>;
export type AddPushNotificationTokenMutationOptions = Apollo.BaseMutationOptions<AddPushNotificationTokenMutation, AddPushNotificationTokenMutationVariables>;
export const CreateInvoiceDocument = gql`
    mutation CreateInvoice($orderId: ID!, $supplierId: ID!) {
  createInvoice(orderId: $orderId, supplierId: $supplierId) {
    id
  }
}
    `;
export type CreateInvoiceMutationFn = Apollo.MutationFunction<CreateInvoiceMutation, CreateInvoiceMutationVariables>;

/**
 * __useCreateInvoiceMutation__
 *
 * To run a mutation, you first call `useCreateInvoiceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateInvoiceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createInvoiceMutation, { data, loading, error }] = useCreateInvoiceMutation({
 *   variables: {
 *      orderId: // value for 'orderId'
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useCreateInvoiceMutation(baseOptions?: Apollo.MutationHookOptions<CreateInvoiceMutation, CreateInvoiceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateInvoiceMutation, CreateInvoiceMutationVariables>(CreateInvoiceDocument, options);
      }
export type CreateInvoiceMutationHookResult = ReturnType<typeof useCreateInvoiceMutation>;
export type CreateInvoiceMutationResult = Apollo.MutationResult<CreateInvoiceMutation>;
export type CreateInvoiceMutationOptions = Apollo.BaseMutationOptions<CreateInvoiceMutation, CreateInvoiceMutationVariables>;
export const CreateUserDocument = gql`
    mutation CreateUser($createUserInput: CreateUserInput) {
  createUser(createUserInput: $createUserInput)
}
    `;
export type CreateUserMutationFn = Apollo.MutationFunction<CreateUserMutation, CreateUserMutationVariables>;

/**
 * __useCreateUserMutation__
 *
 * To run a mutation, you first call `useCreateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createUserMutation, { data, loading, error }] = useCreateUserMutation({
 *   variables: {
 *      createUserInput: // value for 'createUserInput'
 *   },
 * });
 */
export function useCreateUserMutation(baseOptions?: Apollo.MutationHookOptions<CreateUserMutation, CreateUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateUserMutation, CreateUserMutationVariables>(CreateUserDocument, options);
      }
export type CreateUserMutationHookResult = ReturnType<typeof useCreateUserMutation>;
export type CreateUserMutationResult = Apollo.MutationResult<CreateUserMutation>;
export type CreateUserMutationOptions = Apollo.BaseMutationOptions<CreateUserMutation, CreateUserMutationVariables>;
export const GetAccountsDocument = gql`
    query GetAccounts($businessId: ID) {
  accounts(businessId: $businessId) {
    id
    balanceBankAccount {
      institutionName
      accountName
      accountNumberMask
    }
    balanceCreditCard {
      brand
      expiredMonth
      expiredYear
      last4
    }
    type
    is_default
  }
}
    `;

/**
 * __useGetAccountsQuery__
 *
 * To run a query within a React component, call `useGetAccountsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAccountsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAccountsQuery({
 *   variables: {
 *      businessId: // value for 'businessId'
 *   },
 * });
 */
export function useGetAccountsQuery(baseOptions?: Apollo.QueryHookOptions<GetAccountsQuery, GetAccountsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAccountsQuery, GetAccountsQueryVariables>(GetAccountsDocument, options);
      }
export function useGetAccountsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAccountsQuery, GetAccountsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAccountsQuery, GetAccountsQueryVariables>(GetAccountsDocument, options);
        }
export type GetAccountsQueryHookResult = ReturnType<typeof useGetAccountsQuery>;
export type GetAccountsLazyQueryHookResult = ReturnType<typeof useGetAccountsLazyQuery>;
export type GetAccountsQueryResult = Apollo.QueryResult<GetAccountsQuery, GetAccountsQueryVariables>;
export const GetActionItemsDocument = gql`
    query GetActionItems($getActionItemsInput: GetActionItemsInput) {
  actionItems(getActionItemsInput: $getActionItemsInput) {
    description
    invoiceItem {
      checked_in
      checked_in_quantity
      id
      invoice_id
      is_mispick
      name
      price
      quantity
      size
      unit_size
      upc1
      upc2
      upc3
      upc4
    }
  }
}
    `;

/**
 * __useGetActionItemsQuery__
 *
 * To run a query within a React component, call `useGetActionItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetActionItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetActionItemsQuery({
 *   variables: {
 *      getActionItemsInput: // value for 'getActionItemsInput'
 *   },
 * });
 */
export function useGetActionItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetActionItemsQuery, GetActionItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetActionItemsQuery, GetActionItemsQueryVariables>(GetActionItemsDocument, options);
      }
export function useGetActionItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetActionItemsQuery, GetActionItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetActionItemsQuery, GetActionItemsQueryVariables>(GetActionItemsDocument, options);
        }
export type GetActionItemsQueryHookResult = ReturnType<typeof useGetActionItemsQuery>;
export type GetActionItemsLazyQueryHookResult = ReturnType<typeof useGetActionItemsLazyQuery>;
export type GetActionItemsQueryResult = Apollo.QueryResult<GetActionItemsQuery, GetActionItemsQueryVariables>;
export const GetBalanceLinkDocument = gql`
    query GetBalanceLink($businessId: ID) {
  balanceLink(businessId: $businessId) {
    link
  }
}
    `;

/**
 * __useGetBalanceLinkQuery__
 *
 * To run a query within a React component, call `useGetBalanceLinkQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBalanceLinkQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBalanceLinkQuery({
 *   variables: {
 *      businessId: // value for 'businessId'
 *   },
 * });
 */
export function useGetBalanceLinkQuery(baseOptions?: Apollo.QueryHookOptions<GetBalanceLinkQuery, GetBalanceLinkQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetBalanceLinkQuery, GetBalanceLinkQueryVariables>(GetBalanceLinkDocument, options);
      }
export function useGetBalanceLinkLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetBalanceLinkQuery, GetBalanceLinkQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetBalanceLinkQuery, GetBalanceLinkQueryVariables>(GetBalanceLinkDocument, options);
        }
export type GetBalanceLinkQueryHookResult = ReturnType<typeof useGetBalanceLinkQuery>;
export type GetBalanceLinkLazyQueryHookResult = ReturnType<typeof useGetBalanceLinkLazyQuery>;
export type GetBalanceLinkQueryResult = Apollo.QueryResult<GetBalanceLinkQuery, GetBalanceLinkQueryVariables>;
export const GetBrandSectionsDocument = gql`
    query GetBrandSections($getSectionsInput: GetSectionsInput) {
  brandSections(getSectionsInput: $getSectionsInput) {
    itemsPreview {
      id
      image
      supplier_code
      name
      price
      discounted_price
      nacs_category
      unit_size
      qoh
      upc1
      supplier
      last_ordered_date
      avg_cases_per_week
      oos
      created_at
      metadata
      moq
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
    id
    logo
    name
    spotlight_image
  }
}
    `;

/**
 * __useGetBrandSectionsQuery__
 *
 * To run a query within a React component, call `useGetBrandSectionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBrandSectionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBrandSectionsQuery({
 *   variables: {
 *      getSectionsInput: // value for 'getSectionsInput'
 *   },
 * });
 */
export function useGetBrandSectionsQuery(baseOptions?: Apollo.QueryHookOptions<GetBrandSectionsQuery, GetBrandSectionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetBrandSectionsQuery, GetBrandSectionsQueryVariables>(GetBrandSectionsDocument, options);
      }
export function useGetBrandSectionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetBrandSectionsQuery, GetBrandSectionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetBrandSectionsQuery, GetBrandSectionsQueryVariables>(GetBrandSectionsDocument, options);
        }
export type GetBrandSectionsQueryHookResult = ReturnType<typeof useGetBrandSectionsQuery>;
export type GetBrandSectionsLazyQueryHookResult = ReturnType<typeof useGetBrandSectionsLazyQuery>;
export type GetBrandSectionsQueryResult = Apollo.QueryResult<GetBrandSectionsQuery, GetBrandSectionsQueryVariables>;
export const GetBrandSpotlightsDocument = gql`
    query GetBrandSpotlights($userId: ID) {
  brandSpotlights(userId: $userId) {
    id
    name
    spotlight_image
  }
}
    `;

/**
 * __useGetBrandSpotlightsQuery__
 *
 * To run a query within a React component, call `useGetBrandSpotlightsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBrandSpotlightsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBrandSpotlightsQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useGetBrandSpotlightsQuery(baseOptions?: Apollo.QueryHookOptions<GetBrandSpotlightsQuery, GetBrandSpotlightsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetBrandSpotlightsQuery, GetBrandSpotlightsQueryVariables>(GetBrandSpotlightsDocument, options);
      }
export function useGetBrandSpotlightsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetBrandSpotlightsQuery, GetBrandSpotlightsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetBrandSpotlightsQuery, GetBrandSpotlightsQueryVariables>(GetBrandSpotlightsDocument, options);
        }
export type GetBrandSpotlightsQueryHookResult = ReturnType<typeof useGetBrandSpotlightsQuery>;
export type GetBrandSpotlightsLazyQueryHookResult = ReturnType<typeof useGetBrandSpotlightsLazyQuery>;
export type GetBrandSpotlightsQueryResult = Apollo.QueryResult<GetBrandSpotlightsQuery, GetBrandSpotlightsQueryVariables>;
export const GetCartsDocument = gql`
    query GetCarts($getCartsInput: GetCartsInput!) {
  carts(getCartsInput: $getCartsInput) {
    id
    cartItems {
      id
      image
      item_id
      nacs_category
      nacs_subcategory
      name
      oos
      price
      discounted_price
      custom_price
      quantity
      supplier
      supplier_code
      unit_size
      qoh
      upc1
      upc2
      metadata
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
      crv
    }
    subCarts {
      cartItems {
        id
        image
        item_id
        nacs_category
        nacs_subcategory
        name
        oos
        price
        discounted_price
        custom_price
        quantity
        supplier
        supplier_code
        unit_size
        qoh
        upc1
        upc2
        metadata
        crv
        item_uom_id
        uoms {
          id
          name
          supplier_id
          uom_id
          quantity
          item_id
          price
          upc
          archived
        }
      }
      supplier
    }
  }
}
    `;

/**
 * __useGetCartsQuery__
 *
 * To run a query within a React component, call `useGetCartsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCartsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCartsQuery({
 *   variables: {
 *      getCartsInput: // value for 'getCartsInput'
 *   },
 * });
 */
export function useGetCartsQuery(baseOptions: Apollo.QueryHookOptions<GetCartsQuery, GetCartsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCartsQuery, GetCartsQueryVariables>(GetCartsDocument, options);
      }
export function useGetCartsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCartsQuery, GetCartsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCartsQuery, GetCartsQueryVariables>(GetCartsDocument, options);
        }
export type GetCartsQueryHookResult = ReturnType<typeof useGetCartsQuery>;
export type GetCartsLazyQueryHookResult = ReturnType<typeof useGetCartsLazyQuery>;
export type GetCartsQueryResult = Apollo.QueryResult<GetCartsQuery, GetCartsQueryVariables>;
export const GetCategoriesDocument = gql`
    query GetCategories($getCategoriesInput: GetCategoriesInput) {
  categories(getCategoriesInput: $getCategoriesInput) {
    image
    name
    value
  }
}
    `;

/**
 * __useGetCategoriesQuery__
 *
 * To run a query within a React component, call `useGetCategoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCategoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCategoriesQuery({
 *   variables: {
 *      getCategoriesInput: // value for 'getCategoriesInput'
 *   },
 * });
 */
export function useGetCategoriesQuery(baseOptions?: Apollo.QueryHookOptions<GetCategoriesQuery, GetCategoriesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCategoriesQuery, GetCategoriesQueryVariables>(GetCategoriesDocument, options);
      }
export function useGetCategoriesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCategoriesQuery, GetCategoriesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCategoriesQuery, GetCategoriesQueryVariables>(GetCategoriesDocument, options);
        }
export type GetCategoriesQueryHookResult = ReturnType<typeof useGetCategoriesQuery>;
export type GetCategoriesLazyQueryHookResult = ReturnType<typeof useGetCategoriesLazyQuery>;
export type GetCategoriesQueryResult = Apollo.QueryResult<GetCategoriesQuery, GetCategoriesQueryVariables>;
export const GetCategoriesBySupplierDocument = gql`
    query GetCategoriesBySupplier($getCategoriesBySupplierInput: GetCategoriesBySupplierInput) {
  categoriesBySupplier(
    getCategoriesBySupplierInput: $getCategoriesBySupplierInput
  ) {
    id
    image
    name
    items {
      id
    }
    ordering
  }
}
    `;

/**
 * __useGetCategoriesBySupplierQuery__
 *
 * To run a query within a React component, call `useGetCategoriesBySupplierQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCategoriesBySupplierQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCategoriesBySupplierQuery({
 *   variables: {
 *      getCategoriesBySupplierInput: // value for 'getCategoriesBySupplierInput'
 *   },
 * });
 */
export function useGetCategoriesBySupplierQuery(baseOptions?: Apollo.QueryHookOptions<GetCategoriesBySupplierQuery, GetCategoriesBySupplierQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCategoriesBySupplierQuery, GetCategoriesBySupplierQueryVariables>(GetCategoriesBySupplierDocument, options);
      }
export function useGetCategoriesBySupplierLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCategoriesBySupplierQuery, GetCategoriesBySupplierQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCategoriesBySupplierQuery, GetCategoriesBySupplierQueryVariables>(GetCategoriesBySupplierDocument, options);
        }
export type GetCategoriesBySupplierQueryHookResult = ReturnType<typeof useGetCategoriesBySupplierQuery>;
export type GetCategoriesBySupplierLazyQueryHookResult = ReturnType<typeof useGetCategoriesBySupplierLazyQuery>;
export type GetCategoriesBySupplierQueryResult = Apollo.QueryResult<GetCategoriesBySupplierQuery, GetCategoriesBySupplierQueryVariables>;
export const GetCutoffTimesDocument = gql`
    query GetCutoffTimes($getCutoffTimesInput: GetCutoffTimesInput) {
  cutoffTimes(getCutoffTimesInput: $getCutoffTimesInput) {
    id
    supplier
    cutoffDay
    cutoffTime
    deliveryDay
    deliveryTime
    daysToDelivery
    supplierInfo {
      id
      logo
      name
      need_signup
    }
  }
}
    `;

/**
 * __useGetCutoffTimesQuery__
 *
 * To run a query within a React component, call `useGetCutoffTimesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCutoffTimesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCutoffTimesQuery({
 *   variables: {
 *      getCutoffTimesInput: // value for 'getCutoffTimesInput'
 *   },
 * });
 */
export function useGetCutoffTimesQuery(baseOptions?: Apollo.QueryHookOptions<GetCutoffTimesQuery, GetCutoffTimesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCutoffTimesQuery, GetCutoffTimesQueryVariables>(GetCutoffTimesDocument, options);
      }
export function useGetCutoffTimesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCutoffTimesQuery, GetCutoffTimesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCutoffTimesQuery, GetCutoffTimesQueryVariables>(GetCutoffTimesDocument, options);
        }
export type GetCutoffTimesQueryHookResult = ReturnType<typeof useGetCutoffTimesQuery>;
export type GetCutoffTimesLazyQueryHookResult = ReturnType<typeof useGetCutoffTimesLazyQuery>;
export type GetCutoffTimesQueryResult = Apollo.QueryResult<GetCutoffTimesQuery, GetCutoffTimesQueryVariables>;
export const GetExpectedOrdersDocument = gql`
    query GetExpectedOrders($supplierId: ID!) {
  expectedOrders(supplierId: $supplierId) {
    id
    customerDetails {
      id
      config
    }
    status
    delivery_date
  }
}
    `;

/**
 * __useGetExpectedOrdersQuery__
 *
 * To run a query within a React component, call `useGetExpectedOrdersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetExpectedOrdersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetExpectedOrdersQuery({
 *   variables: {
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useGetExpectedOrdersQuery(baseOptions: Apollo.QueryHookOptions<GetExpectedOrdersQuery, GetExpectedOrdersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetExpectedOrdersQuery, GetExpectedOrdersQueryVariables>(GetExpectedOrdersDocument, options);
      }
export function useGetExpectedOrdersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetExpectedOrdersQuery, GetExpectedOrdersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetExpectedOrdersQuery, GetExpectedOrdersQueryVariables>(GetExpectedOrdersDocument, options);
        }
export type GetExpectedOrdersQueryHookResult = ReturnType<typeof useGetExpectedOrdersQuery>;
export type GetExpectedOrdersLazyQueryHookResult = ReturnType<typeof useGetExpectedOrdersLazyQuery>;
export type GetExpectedOrdersQueryResult = Apollo.QueryResult<GetExpectedOrdersQuery, GetExpectedOrdersQueryVariables>;
export const GetFavoritesDocument = gql`
    query GetFavorites($input: GetFavoritesInput!) {
  getFavorites(input: $input) {
    id
    name
    price
    image
  }
}
    `;

/**
 * __useGetFavoritesQuery__
 *
 * To run a query within a React component, call `useGetFavoritesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFavoritesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFavoritesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetFavoritesQuery(baseOptions: Apollo.QueryHookOptions<GetFavoritesQuery, GetFavoritesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetFavoritesQuery, GetFavoritesQueryVariables>(GetFavoritesDocument, options);
      }
export function useGetFavoritesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetFavoritesQuery, GetFavoritesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetFavoritesQuery, GetFavoritesQueryVariables>(GetFavoritesDocument, options);
        }
export type GetFavoritesQueryHookResult = ReturnType<typeof useGetFavoritesQuery>;
export type GetFavoritesLazyQueryHookResult = ReturnType<typeof useGetFavoritesLazyQuery>;
export type GetFavoritesQueryResult = Apollo.QueryResult<GetFavoritesQuery, GetFavoritesQueryVariables>;
export const CustomerHiddenProductsDocument = gql`
    query CustomerHiddenProducts($customerHiddenProductsInput: CustomerHiddenProductsInput!) {
  customerHiddenProducts(
    customerHiddenProductsInput: $customerHiddenProductsInput
  ) {
    items {
      itemId
    }
  }
}
    `;

/**
 * __useCustomerHiddenProductsQuery__
 *
 * To run a query within a React component, call `useCustomerHiddenProductsQuery` and pass it any options that fit your needs.
 * When your component renders, `useCustomerHiddenProductsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCustomerHiddenProductsQuery({
 *   variables: {
 *      customerHiddenProductsInput: // value for 'customerHiddenProductsInput'
 *   },
 * });
 */
export function useCustomerHiddenProductsQuery(baseOptions: Apollo.QueryHookOptions<CustomerHiddenProductsQuery, CustomerHiddenProductsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<CustomerHiddenProductsQuery, CustomerHiddenProductsQueryVariables>(CustomerHiddenProductsDocument, options);
      }
export function useCustomerHiddenProductsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<CustomerHiddenProductsQuery, CustomerHiddenProductsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<CustomerHiddenProductsQuery, CustomerHiddenProductsQueryVariables>(CustomerHiddenProductsDocument, options);
        }
export type CustomerHiddenProductsQueryHookResult = ReturnType<typeof useCustomerHiddenProductsQuery>;
export type CustomerHiddenProductsLazyQueryHookResult = ReturnType<typeof useCustomerHiddenProductsLazyQuery>;
export type CustomerHiddenProductsQueryResult = Apollo.QueryResult<CustomerHiddenProductsQuery, CustomerHiddenProductsQueryVariables>;
export const GetInvoiceItemMatchDocument = gql`
    query GetInvoiceItemMatch($getInvoiceItemMatchInput: GetInvoiceItemMatchInput) {
  invoiceItemMatch(getInvoiceItemMatchInput: $getInvoiceItemMatchInput) {
    itemMatches {
      checked_in
      checked_in_quantity
      id
      invoice_id
      name
      is_mispick
      price
      quantity
      size
      unit_size
      upc1
      upc2
      upc3
      upc4
    }
    matchStatus
  }
}
    `;

/**
 * __useGetInvoiceItemMatchQuery__
 *
 * To run a query within a React component, call `useGetInvoiceItemMatchQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInvoiceItemMatchQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInvoiceItemMatchQuery({
 *   variables: {
 *      getInvoiceItemMatchInput: // value for 'getInvoiceItemMatchInput'
 *   },
 * });
 */
export function useGetInvoiceItemMatchQuery(baseOptions?: Apollo.QueryHookOptions<GetInvoiceItemMatchQuery, GetInvoiceItemMatchQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInvoiceItemMatchQuery, GetInvoiceItemMatchQueryVariables>(GetInvoiceItemMatchDocument, options);
      }
export function useGetInvoiceItemMatchLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInvoiceItemMatchQuery, GetInvoiceItemMatchQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInvoiceItemMatchQuery, GetInvoiceItemMatchQueryVariables>(GetInvoiceItemMatchDocument, options);
        }
export type GetInvoiceItemMatchQueryHookResult = ReturnType<typeof useGetInvoiceItemMatchQuery>;
export type GetInvoiceItemMatchLazyQueryHookResult = ReturnType<typeof useGetInvoiceItemMatchLazyQuery>;
export type GetInvoiceItemMatchQueryResult = Apollo.QueryResult<GetInvoiceItemMatchQuery, GetInvoiceItemMatchQueryVariables>;
export const GetInvoiceItemsDocument = gql`
    query GetInvoiceItems($orderId: ID, $invoiceId: ID) {
  invoiceItems(orderId: $orderId, invoiceId: $invoiceId) {
    upc1
    upc2
    upc3
    upc4
    checked_in
    checked_in_quantity
    id
    invoice_id
    is_mispick
    name
    price
    quantity
    size
    unit_size
    item_id
    item_uom_id
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
  }
}
    `;

/**
 * __useGetInvoiceItemsQuery__
 *
 * To run a query within a React component, call `useGetInvoiceItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInvoiceItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInvoiceItemsQuery({
 *   variables: {
 *      orderId: // value for 'orderId'
 *      invoiceId: // value for 'invoiceId'
 *   },
 * });
 */
export function useGetInvoiceItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetInvoiceItemsQuery, GetInvoiceItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInvoiceItemsQuery, GetInvoiceItemsQueryVariables>(GetInvoiceItemsDocument, options);
      }
export function useGetInvoiceItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInvoiceItemsQuery, GetInvoiceItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInvoiceItemsQuery, GetInvoiceItemsQueryVariables>(GetInvoiceItemsDocument, options);
        }
export type GetInvoiceItemsQueryHookResult = ReturnType<typeof useGetInvoiceItemsQuery>;
export type GetInvoiceItemsLazyQueryHookResult = ReturnType<typeof useGetInvoiceItemsLazyQuery>;
export type GetInvoiceItemsQueryResult = Apollo.QueryResult<GetInvoiceItemsQuery, GetInvoiceItemsQueryVariables>;
export const GetInvoiceDocument = gql`
    query GetInvoice($orderId: ID!) {
  invoice(orderId: $orderId) {
    invoice {
      date_received
      id
      invoice_id
      order_id
      supplier_id
      total
    }
    processing
  }
}
    `;

/**
 * __useGetInvoiceQuery__
 *
 * To run a query within a React component, call `useGetInvoiceQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInvoiceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInvoiceQuery({
 *   variables: {
 *      orderId: // value for 'orderId'
 *   },
 * });
 */
export function useGetInvoiceQuery(baseOptions: Apollo.QueryHookOptions<GetInvoiceQuery, GetInvoiceQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInvoiceQuery, GetInvoiceQueryVariables>(GetInvoiceDocument, options);
      }
export function useGetInvoiceLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInvoiceQuery, GetInvoiceQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInvoiceQuery, GetInvoiceQueryVariables>(GetInvoiceDocument, options);
        }
export type GetInvoiceQueryHookResult = ReturnType<typeof useGetInvoiceQuery>;
export type GetInvoiceLazyQueryHookResult = ReturnType<typeof useGetInvoiceLazyQuery>;
export type GetInvoiceQueryResult = Apollo.QueryResult<GetInvoiceQuery, GetInvoiceQueryVariables>;
export const GetItemsDocument = gql`
    query GetItems($getItemsInput: GetItemsInput) {
  items(getItemsInput: $getItemsInput) {
    id
    name
    unit_size
    size
    price
    discounted_price
    upc1
    upc2
    nacs_category
    nacs_subcategory
    supplier
    image
    qoh
    qty_on_hand
    last_ordered_date
    avg_cases_per_week
    oos
    created_at
    metadata
    back_in_stock_date
    moq
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    crv
    min_sale_price
    isFavorited
    supplier_info {
      id
      name
      need_signup
    }
    related_items {
      id
      name
      unit_size
      price
      discounted_price
      upc1
      upc2
      nacs_category
      nacs_subcategory
      supplier
      image
      last_ordered_date
      qoh
      qty_on_hand
      oos
      created_at
      isFavorited
      back_in_stock_date
    }
  }
}
    `;

/**
 * __useGetItemsQuery__
 *
 * To run a query within a React component, call `useGetItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemsQuery({
 *   variables: {
 *      getItemsInput: // value for 'getItemsInput'
 *   },
 * });
 */
export function useGetItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetItemsQuery, GetItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemsQuery, GetItemsQueryVariables>(GetItemsDocument, options);
      }
export function useGetItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemsQuery, GetItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemsQuery, GetItemsQueryVariables>(GetItemsDocument, options);
        }
export type GetItemsQueryHookResult = ReturnType<typeof useGetItemsQuery>;
export type GetItemsLazyQueryHookResult = ReturnType<typeof useGetItemsLazyQuery>;
export type GetItemsQueryResult = Apollo.QueryResult<GetItemsQuery, GetItemsQueryVariables>;
export const GetItemsByFilterDocument = gql`
    query GetItemsByFilter($getItemsByFilterInput: GetItemsByFilterInput) {
  itemsByFilter(getItemsByFilterInput: $getItemsByFilterInput) {
    id
    name
    unit_size
    price
    discounted_price
    upc1
    upc2
    nacs_category
    nacs_subcategory
    image
    last_ordered_date
    avg_cases_per_week
    oos
    created_at
    qoh
    metadata
    back_in_stock_date
  }
}
    `;

/**
 * __useGetItemsByFilterQuery__
 *
 * To run a query within a React component, call `useGetItemsByFilterQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemsByFilterQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemsByFilterQuery({
 *   variables: {
 *      getItemsByFilterInput: // value for 'getItemsByFilterInput'
 *   },
 * });
 */
export function useGetItemsByFilterQuery(baseOptions?: Apollo.QueryHookOptions<GetItemsByFilterQuery, GetItemsByFilterQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemsByFilterQuery, GetItemsByFilterQueryVariables>(GetItemsByFilterDocument, options);
      }
export function useGetItemsByFilterLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemsByFilterQuery, GetItemsByFilterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemsByFilterQuery, GetItemsByFilterQueryVariables>(GetItemsByFilterDocument, options);
        }
export type GetItemsByFilterQueryHookResult = ReturnType<typeof useGetItemsByFilterQuery>;
export type GetItemsByFilterLazyQueryHookResult = ReturnType<typeof useGetItemsByFilterLazyQuery>;
export type GetItemsByFilterQueryResult = Apollo.QueryResult<GetItemsByFilterQuery, GetItemsByFilterQueryVariables>;
export const GetItemsBySupplierDocument = gql`
    query GetItemsBySupplier($getItemsBySupplierInput: GetItemsBySupplierInput) {
  itemsBySupplier(getItemsBySupplierInput: $getItemsBySupplierInput) {
    id
    name
    unit_size
    size
    price
    discounted_price
    upc1
    upc2
    nacs_category
    nacs_subcategory
    image
    last_ordered_date
    avg_cases_per_week
    supplier
    qoh
    qty_on_hand
    oos
    created_at
    metadata
    moq
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    min_sale_price
    isFavorited
    back_in_stock_date
  }
}
    `;

/**
 * __useGetItemsBySupplierQuery__
 *
 * To run a query within a React component, call `useGetItemsBySupplierQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemsBySupplierQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemsBySupplierQuery({
 *   variables: {
 *      getItemsBySupplierInput: // value for 'getItemsBySupplierInput'
 *   },
 * });
 */
export function useGetItemsBySupplierQuery(baseOptions?: Apollo.QueryHookOptions<GetItemsBySupplierQuery, GetItemsBySupplierQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemsBySupplierQuery, GetItemsBySupplierQueryVariables>(GetItemsBySupplierDocument, options);
      }
export function useGetItemsBySupplierLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemsBySupplierQuery, GetItemsBySupplierQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemsBySupplierQuery, GetItemsBySupplierQueryVariables>(GetItemsBySupplierDocument, options);
        }
export type GetItemsBySupplierQueryHookResult = ReturnType<typeof useGetItemsBySupplierQuery>;
export type GetItemsBySupplierLazyQueryHookResult = ReturnType<typeof useGetItemsBySupplierLazyQuery>;
export type GetItemsBySupplierQueryResult = Apollo.QueryResult<GetItemsBySupplierQuery, GetItemsBySupplierQueryVariables>;
export const GetItemsV2Document = gql`
    query GetItemsV2($itemsInputV2: ItemsInputV2!) {
  itemsV2(itemsInput: $itemsInputV2) {
    totalCount
    items {
      id
      name
      unit_size
      qoh
      price
      discounted_price
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier_code
      supplier
      last_ordered_date
      avg_cases_per_week
      size
      tags
      oos
      local_item
      outdated
      created_at
      archived
      qb_id
      qb_sync_token
      updated_at
      metadata
      crv
      qty_on_hand
      cog_price
      moq
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
  }
}
    `;

/**
 * __useGetItemsV2Query__
 *
 * To run a query within a React component, call `useGetItemsV2Query` and pass it any options that fit your needs.
 * When your component renders, `useGetItemsV2Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemsV2Query({
 *   variables: {
 *      itemsInputV2: // value for 'itemsInputV2'
 *   },
 * });
 */
export function useGetItemsV2Query(baseOptions: Apollo.QueryHookOptions<GetItemsV2Query, GetItemsV2QueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemsV2Query, GetItemsV2QueryVariables>(GetItemsV2Document, options);
      }
export function useGetItemsV2LazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemsV2Query, GetItemsV2QueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemsV2Query, GetItemsV2QueryVariables>(GetItemsV2Document, options);
        }
export type GetItemsV2QueryHookResult = ReturnType<typeof useGetItemsV2Query>;
export type GetItemsV2LazyQueryHookResult = ReturnType<typeof useGetItemsV2LazyQuery>;
export type GetItemsV2QueryResult = Apollo.QueryResult<GetItemsV2Query, GetItemsV2QueryVariables>;
export const GetOrderBySupplierDocument = gql`
    query GetOrderBySupplier($orderId: ID, $supplierId: ID) {
  orderBySupplier(orderId: $orderId, supplierId: $supplierId) {
    orderItems {
      id
      name
      supplier
      unit_size
      price
      discounted_price
      price_purchased_at
      upc1
      upc2
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
      nacs_subcategory
      nacs_category
      quantity
      item_id
      image
      supplier_code
      qoh
      metadata
      crv
    }
  }
}
    `;

/**
 * __useGetOrderBySupplierQuery__
 *
 * To run a query within a React component, call `useGetOrderBySupplierQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrderBySupplierQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrderBySupplierQuery({
 *   variables: {
 *      orderId: // value for 'orderId'
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useGetOrderBySupplierQuery(baseOptions?: Apollo.QueryHookOptions<GetOrderBySupplierQuery, GetOrderBySupplierQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrderBySupplierQuery, GetOrderBySupplierQueryVariables>(GetOrderBySupplierDocument, options);
      }
export function useGetOrderBySupplierLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrderBySupplierQuery, GetOrderBySupplierQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrderBySupplierQuery, GetOrderBySupplierQueryVariables>(GetOrderBySupplierDocument, options);
        }
export type GetOrderBySupplierQueryHookResult = ReturnType<typeof useGetOrderBySupplierQuery>;
export type GetOrderBySupplierLazyQueryHookResult = ReturnType<typeof useGetOrderBySupplierLazyQuery>;
export type GetOrderBySupplierQueryResult = Apollo.QueryResult<GetOrderBySupplierQuery, GetOrderBySupplierQueryVariables>;
export const GetOrderStatusesDocument = gql`
    query GetOrderStatuses($orderId: ID) {
  orderStatuses(orderId: $orderId) {
    id
    order_id
    supplier_id
    delivery_date
    delivering_date
    submission_date
    name
  }
}
    `;

/**
 * __useGetOrderStatusesQuery__
 *
 * To run a query within a React component, call `useGetOrderStatusesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrderStatusesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrderStatusesQuery({
 *   variables: {
 *      orderId: // value for 'orderId'
 *   },
 * });
 */
export function useGetOrderStatusesQuery(baseOptions?: Apollo.QueryHookOptions<GetOrderStatusesQuery, GetOrderStatusesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrderStatusesQuery, GetOrderStatusesQueryVariables>(GetOrderStatusesDocument, options);
      }
export function useGetOrderStatusesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrderStatusesQuery, GetOrderStatusesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrderStatusesQuery, GetOrderStatusesQueryVariables>(GetOrderStatusesDocument, options);
        }
export type GetOrderStatusesQueryHookResult = ReturnType<typeof useGetOrderStatusesQuery>;
export type GetOrderStatusesLazyQueryHookResult = ReturnType<typeof useGetOrderStatusesLazyQuery>;
export type GetOrderStatusesQueryResult = Apollo.QueryResult<GetOrderStatusesQuery, GetOrderStatusesQueryVariables>;
export const GetOrdersDocument = gql`
    query GetOrders($getOrdersInput: GetOrdersInput) {
  orders(getOrdersInput: $getOrdersInput) {
    id
    order_number
    status
    subtotal
    date_submitted
    orderName
    orderItems {
      id
      name
      unit_size
      price
      discounted_price
      price_purchased_at
      upc1
      upc2
      nacs_category
      nacs_subcategory
      quantity
      item_id
      image
      oos
      qoh
      metadata
      supplier
    }
    delivery_date
    supplier
    supplier_logo
    invoice {
      id
      discount
      signature
      signature_name
      paid
      credit
      payment_method
      payment_status
      notes
      subtotal
      total
    }
  }
}
    `;

/**
 * __useGetOrdersQuery__
 *
 * To run a query within a React component, call `useGetOrdersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrdersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrdersQuery({
 *   variables: {
 *      getOrdersInput: // value for 'getOrdersInput'
 *   },
 * });
 */
export function useGetOrdersQuery(baseOptions?: Apollo.QueryHookOptions<GetOrdersQuery, GetOrdersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrdersQuery, GetOrdersQueryVariables>(GetOrdersDocument, options);
      }
export function useGetOrdersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrdersQuery, GetOrdersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrdersQuery, GetOrdersQueryVariables>(GetOrdersDocument, options);
        }
export type GetOrdersQueryHookResult = ReturnType<typeof useGetOrdersQuery>;
export type GetOrdersLazyQueryHookResult = ReturnType<typeof useGetOrdersLazyQuery>;
export type GetOrdersQueryResult = Apollo.QueryResult<GetOrdersQuery, GetOrdersQueryVariables>;
export const GetOrdersBySupplierDocument = gql`
    query GetOrdersBySupplier($getOrdersBySupplierInput: GetOrdersBySupplierInput) {
  ordersBySupplier(getOrdersBySupplierInput: $getOrdersBySupplierInput) {
    orders {
      customerDetails {
        id
        name
      }
      id
      order_number
      status
      subtotal
      date_submitted
      orderName
      config
      orderItems {
        id
        name
        unit_size
        price
        discounted_price
        price_purchased_at
        upc1
        upc2
        nacs_category
        nacs_subcategory
        quantity
        item_id
        image
        oos
        qoh
        metadata
        supplier
        archived
        crv
      }
      delivery_date
      supplier
      supplier_logo
      invoice {
        id
        discount
        signature
        signature_name
        paid
        credit
        payment_method
        notes
        subtotal
        total
        config
      }
    }
  }
}
    `;

/**
 * __useGetOrdersBySupplierQuery__
 *
 * To run a query within a React component, call `useGetOrdersBySupplierQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrdersBySupplierQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrdersBySupplierQuery({
 *   variables: {
 *      getOrdersBySupplierInput: // value for 'getOrdersBySupplierInput'
 *   },
 * });
 */
export function useGetOrdersBySupplierQuery(baseOptions?: Apollo.QueryHookOptions<GetOrdersBySupplierQuery, GetOrdersBySupplierQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrdersBySupplierQuery, GetOrdersBySupplierQueryVariables>(GetOrdersBySupplierDocument, options);
      }
export function useGetOrdersBySupplierLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrdersBySupplierQuery, GetOrdersBySupplierQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrdersBySupplierQuery, GetOrdersBySupplierQueryVariables>(GetOrdersBySupplierDocument, options);
        }
export type GetOrdersBySupplierQueryHookResult = ReturnType<typeof useGetOrdersBySupplierQuery>;
export type GetOrdersBySupplierLazyQueryHookResult = ReturnType<typeof useGetOrdersBySupplierLazyQuery>;
export type GetOrdersBySupplierQueryResult = Apollo.QueryResult<GetOrdersBySupplierQuery, GetOrdersBySupplierQueryVariables>;
export const GetOrdersV2Document = gql`
    query GetOrdersV2($ordersV2Input: OrdersInputV2!) {
  ordersV2(ordersInput: $ordersV2Input) {
    orders {
      customerDetails {
        id
        name
      }
      id
      status
      subtotal
      date_submitted
      orderName
      config
      delivery_date
      supplier
      supplier_logo
      invoice {
        id
        discount
        paid
        credit
        payment_method
        payment_status
        subtotal
        total
        config
      }
    }
  }
}
    `;

/**
 * __useGetOrdersV2Query__
 *
 * To run a query within a React component, call `useGetOrdersV2Query` and pass it any options that fit your needs.
 * When your component renders, `useGetOrdersV2Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrdersV2Query({
 *   variables: {
 *      ordersV2Input: // value for 'ordersV2Input'
 *   },
 * });
 */
export function useGetOrdersV2Query(baseOptions: Apollo.QueryHookOptions<GetOrdersV2Query, GetOrdersV2QueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrdersV2Query, GetOrdersV2QueryVariables>(GetOrdersV2Document, options);
      }
export function useGetOrdersV2LazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrdersV2Query, GetOrdersV2QueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrdersV2Query, GetOrdersV2QueryVariables>(GetOrdersV2Document, options);
        }
export type GetOrdersV2QueryHookResult = ReturnType<typeof useGetOrdersV2Query>;
export type GetOrdersV2LazyQueryHookResult = ReturnType<typeof useGetOrdersV2LazyQuery>;
export type GetOrdersV2QueryResult = Apollo.QueryResult<GetOrdersV2Query, GetOrdersV2QueryVariables>;
export const GetRoutesDocument = gql`
    query GetRoutes($getRoutesBySupplierInput: GetRoutesBySupplierInput) {
  routesBySupplier(getRoutesBySupplierInput: $getRoutesBySupplierInput) {
    id
    name
    driver
    day_of_week
    color
  }
}
    `;

/**
 * __useGetRoutesQuery__
 *
 * To run a query within a React component, call `useGetRoutesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRoutesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRoutesQuery({
 *   variables: {
 *      getRoutesBySupplierInput: // value for 'getRoutesBySupplierInput'
 *   },
 * });
 */
export function useGetRoutesQuery(baseOptions?: Apollo.QueryHookOptions<GetRoutesQuery, GetRoutesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRoutesQuery, GetRoutesQueryVariables>(GetRoutesDocument, options);
      }
export function useGetRoutesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRoutesQuery, GetRoutesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRoutesQuery, GetRoutesQueryVariables>(GetRoutesDocument, options);
        }
export type GetRoutesQueryHookResult = ReturnType<typeof useGetRoutesQuery>;
export type GetRoutesLazyQueryHookResult = ReturnType<typeof useGetRoutesLazyQuery>;
export type GetRoutesQueryResult = Apollo.QueryResult<GetRoutesQuery, GetRoutesQueryVariables>;
export const GetSectionsDocument = gql`
    query GetSections($getSectionsInput: GetSectionsInput) {
  sections(getSectionsInput: $getSectionsInput) {
    value
    name
    items {
      id
      name
      price
      discounted_price
      unit_size
      qoh
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      last_ordered_date
      oos
      created_at
      metadata
      avg_cases_per_week
      moq
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
  }
}
    `;

/**
 * __useGetSectionsQuery__
 *
 * To run a query within a React component, call `useGetSectionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSectionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSectionsQuery({
 *   variables: {
 *      getSectionsInput: // value for 'getSectionsInput'
 *   },
 * });
 */
export function useGetSectionsQuery(baseOptions?: Apollo.QueryHookOptions<GetSectionsQuery, GetSectionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSectionsQuery, GetSectionsQueryVariables>(GetSectionsDocument, options);
      }
export function useGetSectionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSectionsQuery, GetSectionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSectionsQuery, GetSectionsQueryVariables>(GetSectionsDocument, options);
        }
export type GetSectionsQueryHookResult = ReturnType<typeof useGetSectionsQuery>;
export type GetSectionsLazyQueryHookResult = ReturnType<typeof useGetSectionsLazyQuery>;
export type GetSectionsQueryResult = Apollo.QueryResult<GetSectionsQuery, GetSectionsQueryVariables>;
export const GetSupplierConfigDocument = gql`
    query GetSupplierConfig($supplierId: ID!) {
  supplierConfig(supplierId: $supplierId) {
    is_dsd
    send_order_notifications
    invoice_scale_width
    allow_image_upload
    show_order_tabs
    auto_set_delivery_date
    use_alltime_avg_calculation
    exclude_canceled_orders
    skip_qb_sync
    requires_delivery_date
    spotlight_ids
    qb_credit_memo_date_filter
    pavilions_display_name
    pavilions_address
    enable_minimum_pricing
    enable_catalog_sharing
  }
}
    `;

/**
 * __useGetSupplierConfigQuery__
 *
 * To run a query within a React component, call `useGetSupplierConfigQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSupplierConfigQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSupplierConfigQuery({
 *   variables: {
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useGetSupplierConfigQuery(baseOptions: Apollo.QueryHookOptions<GetSupplierConfigQuery, GetSupplierConfigQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSupplierConfigQuery, GetSupplierConfigQueryVariables>(GetSupplierConfigDocument, options);
      }
export function useGetSupplierConfigLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSupplierConfigQuery, GetSupplierConfigQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSupplierConfigQuery, GetSupplierConfigQueryVariables>(GetSupplierConfigDocument, options);
        }
export type GetSupplierConfigQueryHookResult = ReturnType<typeof useGetSupplierConfigQuery>;
export type GetSupplierConfigLazyQueryHookResult = ReturnType<typeof useGetSupplierConfigLazyQuery>;
export type GetSupplierConfigQueryResult = Apollo.QueryResult<GetSupplierConfigQuery, GetSupplierConfigQueryVariables>;
export const GetSupplierConfigByIdDocument = gql`
    query GetSupplierConfigById($supplierId: ID!) {
  supplierConfig(supplierId: $supplierId) {
    enable_bounced_check_tracking
    show_goals_tracking_feature
  }
}
    `;

/**
 * __useGetSupplierConfigByIdQuery__
 *
 * To run a query within a React component, call `useGetSupplierConfigByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSupplierConfigByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSupplierConfigByIdQuery({
 *   variables: {
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useGetSupplierConfigByIdQuery(baseOptions: Apollo.QueryHookOptions<GetSupplierConfigByIdQuery, GetSupplierConfigByIdQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSupplierConfigByIdQuery, GetSupplierConfigByIdQueryVariables>(GetSupplierConfigByIdDocument, options);
      }
export function useGetSupplierConfigByIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSupplierConfigByIdQuery, GetSupplierConfigByIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSupplierConfigByIdQuery, GetSupplierConfigByIdQueryVariables>(GetSupplierConfigByIdDocument, options);
        }
export type GetSupplierConfigByIdQueryHookResult = ReturnType<typeof useGetSupplierConfigByIdQuery>;
export type GetSupplierConfigByIdLazyQueryHookResult = ReturnType<typeof useGetSupplierConfigByIdLazyQuery>;
export type GetSupplierConfigByIdQueryResult = Apollo.QueryResult<GetSupplierConfigByIdQuery, GetSupplierConfigByIdQueryVariables>;
export const GetUserGoalsDocument = gql`
    query GetUserGoals($goalsInput: GoalsInput!) {
  goals(goalsInput: $goalsInput) {
    goals {
      id
      supplier_id
      name
      type
      period
      target_amount
      start_date
      end_date
      is_active
      status
      available_periods {
        label
        value
      }
      created_at
      updated_at
      assignments {
        id
        goal_id
        employee_id
        employee {
          id
          name
          email
        }
        target_amount
        current_progress
        percentage_complete
        created_at
        updated_at
      }
    }
    totalCount
  }
}
    `;

/**
 * __useGetUserGoalsQuery__
 *
 * To run a query within a React component, call `useGetUserGoalsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserGoalsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserGoalsQuery({
 *   variables: {
 *      goalsInput: // value for 'goalsInput'
 *   },
 * });
 */
export function useGetUserGoalsQuery(baseOptions: Apollo.QueryHookOptions<GetUserGoalsQuery, GetUserGoalsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUserGoalsQuery, GetUserGoalsQueryVariables>(GetUserGoalsDocument, options);
      }
export function useGetUserGoalsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUserGoalsQuery, GetUserGoalsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUserGoalsQuery, GetUserGoalsQueryVariables>(GetUserGoalsDocument, options);
        }
export type GetUserGoalsQueryHookResult = ReturnType<typeof useGetUserGoalsQuery>;
export type GetUserGoalsLazyQueryHookResult = ReturnType<typeof useGetUserGoalsLazyQuery>;
export type GetUserGoalsQueryResult = Apollo.QueryResult<GetUserGoalsQuery, GetUserGoalsQueryVariables>;
export const GetUsersDocument = gql`
    query GetUsers($getUsersInput: GetUsersInput) {
  users(getUsersInput: $getUsersInput) {
    id
    name
    suppliers {
      id
      logo
      name
      minimum
      phone_number
      address
      config
      orderCount
    }
    user_name
    supplier_beta
    approved
    driver
    address
    delivery_window {
      days_of_week
      start_time
      end_time
    }
    custom_prices {
      item_id
      price
    }
    custom_uom_prices {
      item_id
      uom_prices {
        item_uom_id
        user_id
        price
        uom_name
        uom_id
      }
    }
    store_group
    route_id
    config
    net_terms_days
  }
}
    `;

/**
 * __useGetUsersQuery__
 *
 * To run a query within a React component, call `useGetUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersQuery({
 *   variables: {
 *      getUsersInput: // value for 'getUsersInput'
 *   },
 * });
 */
export function useGetUsersQuery(baseOptions?: Apollo.QueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
      }
export function useGetUsersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
        }
export type GetUsersQueryHookResult = ReturnType<typeof useGetUsersQuery>;
export type GetUsersLazyQueryHookResult = ReturnType<typeof useGetUsersLazyQuery>;
export type GetUsersQueryResult = Apollo.QueryResult<GetUsersQuery, GetUsersQueryVariables>;
export const GetUsersOnTodaysRoutesDocument = gql`
    query GetUsersOnTodaysRoutes($supplierId: ID!) {
  usersOnTodaysRoutes(supplierId: $supplierId) {
    id
    name
    user_name
    email
    driver
    route_id
    approved
  }
}
    `;

/**
 * __useGetUsersOnTodaysRoutesQuery__
 *
 * To run a query within a React component, call `useGetUsersOnTodaysRoutesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersOnTodaysRoutesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersOnTodaysRoutesQuery({
 *   variables: {
 *      supplierId: // value for 'supplierId'
 *   },
 * });
 */
export function useGetUsersOnTodaysRoutesQuery(baseOptions: Apollo.QueryHookOptions<GetUsersOnTodaysRoutesQuery, GetUsersOnTodaysRoutesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUsersOnTodaysRoutesQuery, GetUsersOnTodaysRoutesQueryVariables>(GetUsersOnTodaysRoutesDocument, options);
      }
export function useGetUsersOnTodaysRoutesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUsersOnTodaysRoutesQuery, GetUsersOnTodaysRoutesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUsersOnTodaysRoutesQuery, GetUsersOnTodaysRoutesQueryVariables>(GetUsersOnTodaysRoutesDocument, options);
        }
export type GetUsersOnTodaysRoutesQueryHookResult = ReturnType<typeof useGetUsersOnTodaysRoutesQuery>;
export type GetUsersOnTodaysRoutesLazyQueryHookResult = ReturnType<typeof useGetUsersOnTodaysRoutesLazyQuery>;
export type GetUsersOnTodaysRoutesQueryResult = Apollo.QueryResult<GetUsersOnTodaysRoutesQuery, GetUsersOnTodaysRoutesQueryVariables>;
export const ReconcileInvoiceWithItemDocument = gql`
    mutation ReconcileInvoiceWithItem($reconcileInvoiceWithItemInput: ReconcileInvoiceWithItemInput) {
  reconcileInvoiceWithItem(
    reconcileInvoiceWithItemInput: $reconcileInvoiceWithItemInput
  ) {
    checked_in
    checked_in_quantity
    id
    invoice_id
    is_mispick
    name
    quantity
    size
    price
    unit_size
    upc1
    upc4
    upc2
    upc3
  }
}
    `;
export type ReconcileInvoiceWithItemMutationFn = Apollo.MutationFunction<ReconcileInvoiceWithItemMutation, ReconcileInvoiceWithItemMutationVariables>;

/**
 * __useReconcileInvoiceWithItemMutation__
 *
 * To run a mutation, you first call `useReconcileInvoiceWithItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useReconcileInvoiceWithItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [reconcileInvoiceWithItemMutation, { data, loading, error }] = useReconcileInvoiceWithItemMutation({
 *   variables: {
 *      reconcileInvoiceWithItemInput: // value for 'reconcileInvoiceWithItemInput'
 *   },
 * });
 */
export function useReconcileInvoiceWithItemMutation(baseOptions?: Apollo.MutationHookOptions<ReconcileInvoiceWithItemMutation, ReconcileInvoiceWithItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ReconcileInvoiceWithItemMutation, ReconcileInvoiceWithItemMutationVariables>(ReconcileInvoiceWithItemDocument, options);
      }
export type ReconcileInvoiceWithItemMutationHookResult = ReturnType<typeof useReconcileInvoiceWithItemMutation>;
export type ReconcileInvoiceWithItemMutationResult = Apollo.MutationResult<ReconcileInvoiceWithItemMutation>;
export type ReconcileInvoiceWithItemMutationOptions = Apollo.BaseMutationOptions<ReconcileInvoiceWithItemMutation, ReconcileInvoiceWithItemMutationVariables>;
export const SendReceivingReportDocument = gql`
    mutation SendReceivingReport($userId: ID, $invoiceId: ID) {
  sendReceivingReport(userId: $userId, invoiceId: $invoiceId)
}
    `;
export type SendReceivingReportMutationFn = Apollo.MutationFunction<SendReceivingReportMutation, SendReceivingReportMutationVariables>;

/**
 * __useSendReceivingReportMutation__
 *
 * To run a mutation, you first call `useSendReceivingReportMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendReceivingReportMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendReceivingReportMutation, { data, loading, error }] = useSendReceivingReportMutation({
 *   variables: {
 *      userId: // value for 'userId'
 *      invoiceId: // value for 'invoiceId'
 *   },
 * });
 */
export function useSendReceivingReportMutation(baseOptions?: Apollo.MutationHookOptions<SendReceivingReportMutation, SendReceivingReportMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SendReceivingReportMutation, SendReceivingReportMutationVariables>(SendReceivingReportDocument, options);
      }
export type SendReceivingReportMutationHookResult = ReturnType<typeof useSendReceivingReportMutation>;
export type SendReceivingReportMutationResult = Apollo.MutationResult<SendReceivingReportMutation>;
export type SendReceivingReportMutationOptions = Apollo.BaseMutationOptions<SendReceivingReportMutation, SendReceivingReportMutationVariables>;
export const SubmitCreditRequestsDocument = gql`
    mutation SubmitCreditRequests($submitCreditRequestsInput: SubmitCreditRequestsInput) {
  submitCreditRequests(submitCreditRequestsInput: $submitCreditRequestsInput)
}
    `;
export type SubmitCreditRequestsMutationFn = Apollo.MutationFunction<SubmitCreditRequestsMutation, SubmitCreditRequestsMutationVariables>;

/**
 * __useSubmitCreditRequestsMutation__
 *
 * To run a mutation, you first call `useSubmitCreditRequestsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitCreditRequestsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitCreditRequestsMutation, { data, loading, error }] = useSubmitCreditRequestsMutation({
 *   variables: {
 *      submitCreditRequestsInput: // value for 'submitCreditRequestsInput'
 *   },
 * });
 */
export function useSubmitCreditRequestsMutation(baseOptions?: Apollo.MutationHookOptions<SubmitCreditRequestsMutation, SubmitCreditRequestsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SubmitCreditRequestsMutation, SubmitCreditRequestsMutationVariables>(SubmitCreditRequestsDocument, options);
      }
export type SubmitCreditRequestsMutationHookResult = ReturnType<typeof useSubmitCreditRequestsMutation>;
export type SubmitCreditRequestsMutationResult = Apollo.MutationResult<SubmitCreditRequestsMutation>;
export type SubmitCreditRequestsMutationOptions = Apollo.BaseMutationOptions<SubmitCreditRequestsMutation, SubmitCreditRequestsMutationVariables>;
export const SubmitFeedbackDocument = gql`
    mutation SubmitFeedback($submitFeedbackInput: SubmitFeedbackInput!) {
  submitFeedback(submitFeedbackInput: $submitFeedbackInput)
}
    `;
export type SubmitFeedbackMutationFn = Apollo.MutationFunction<SubmitFeedbackMutation, SubmitFeedbackMutationVariables>;

/**
 * __useSubmitFeedbackMutation__
 *
 * To run a mutation, you first call `useSubmitFeedbackMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitFeedbackMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitFeedbackMutation, { data, loading, error }] = useSubmitFeedbackMutation({
 *   variables: {
 *      submitFeedbackInput: // value for 'submitFeedbackInput'
 *   },
 * });
 */
export function useSubmitFeedbackMutation(baseOptions?: Apollo.MutationHookOptions<SubmitFeedbackMutation, SubmitFeedbackMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SubmitFeedbackMutation, SubmitFeedbackMutationVariables>(SubmitFeedbackDocument, options);
      }
export type SubmitFeedbackMutationHookResult = ReturnType<typeof useSubmitFeedbackMutation>;
export type SubmitFeedbackMutationResult = Apollo.MutationResult<SubmitFeedbackMutation>;
export type SubmitFeedbackMutationOptions = Apollo.BaseMutationOptions<SubmitFeedbackMutation, SubmitFeedbackMutationVariables>;
export const SubmitOrderDocument = gql`
    mutation SubmitOrder($submitOrderInput: SubmitOrderInput!) {
  submitOrder(submitOrderInput: $submitOrderInput) {
    id
    status
    subtotal
    order_number
  }
}
    `;
export type SubmitOrderMutationFn = Apollo.MutationFunction<SubmitOrderMutation, SubmitOrderMutationVariables>;

/**
 * __useSubmitOrderMutation__
 *
 * To run a mutation, you first call `useSubmitOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitOrderMutation, { data, loading, error }] = useSubmitOrderMutation({
 *   variables: {
 *      submitOrderInput: // value for 'submitOrderInput'
 *   },
 * });
 */
export function useSubmitOrderMutation(baseOptions?: Apollo.MutationHookOptions<SubmitOrderMutation, SubmitOrderMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SubmitOrderMutation, SubmitOrderMutationVariables>(SubmitOrderDocument, options);
      }
export type SubmitOrderMutationHookResult = ReturnType<typeof useSubmitOrderMutation>;
export type SubmitOrderMutationResult = Apollo.MutationResult<SubmitOrderMutation>;
export type SubmitOrderMutationOptions = Apollo.BaseMutationOptions<SubmitOrderMutation, SubmitOrderMutationVariables>;
export const ToggleFavoriteDocument = gql`
    mutation ToggleFavorite($input: ToggleFavoriteInput!) {
  toggleFavorite(input: $input) {
    success
    isFavorited
    message
  }
}
    `;
export type ToggleFavoriteMutationFn = Apollo.MutationFunction<ToggleFavoriteMutation, ToggleFavoriteMutationVariables>;

/**
 * __useToggleFavoriteMutation__
 *
 * To run a mutation, you first call `useToggleFavoriteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useToggleFavoriteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [toggleFavoriteMutation, { data, loading, error }] = useToggleFavoriteMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useToggleFavoriteMutation(baseOptions?: Apollo.MutationHookOptions<ToggleFavoriteMutation, ToggleFavoriteMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ToggleFavoriteMutation, ToggleFavoriteMutationVariables>(ToggleFavoriteDocument, options);
      }
export type ToggleFavoriteMutationHookResult = ReturnType<typeof useToggleFavoriteMutation>;
export type ToggleFavoriteMutationResult = Apollo.MutationResult<ToggleFavoriteMutation>;
export type ToggleFavoriteMutationOptions = Apollo.BaseMutationOptions<ToggleFavoriteMutation, ToggleFavoriteMutationVariables>;
export const UpdateDefaultInAccountDocument = gql`
    mutation UpdateDefaultInAccount($updateDefaultInAccountInput: UpdateDefaultInAccountInput) {
  updateDefaultInAccount(
    updateDefaultInAccountInput: $updateDefaultInAccountInput
  ) {
    id
    is_default
  }
}
    `;
export type UpdateDefaultInAccountMutationFn = Apollo.MutationFunction<UpdateDefaultInAccountMutation, UpdateDefaultInAccountMutationVariables>;

/**
 * __useUpdateDefaultInAccountMutation__
 *
 * To run a mutation, you first call `useUpdateDefaultInAccountMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateDefaultInAccountMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateDefaultInAccountMutation, { data, loading, error }] = useUpdateDefaultInAccountMutation({
 *   variables: {
 *      updateDefaultInAccountInput: // value for 'updateDefaultInAccountInput'
 *   },
 * });
 */
export function useUpdateDefaultInAccountMutation(baseOptions?: Apollo.MutationHookOptions<UpdateDefaultInAccountMutation, UpdateDefaultInAccountMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateDefaultInAccountMutation, UpdateDefaultInAccountMutationVariables>(UpdateDefaultInAccountDocument, options);
      }
export type UpdateDefaultInAccountMutationHookResult = ReturnType<typeof useUpdateDefaultInAccountMutation>;
export type UpdateDefaultInAccountMutationResult = Apollo.MutationResult<UpdateDefaultInAccountMutation>;
export type UpdateDefaultInAccountMutationOptions = Apollo.BaseMutationOptions<UpdateDefaultInAccountMutation, UpdateDefaultInAccountMutationVariables>;
export const UpdateEmployeeDocument = gql`
    mutation UpdateEmployee($input: UpdateEmployeeInput!) {
  updateEmployee(input: $input) {
    id
    name
    email
    phone
    app_access
    dashboard_access
    archived
    roles {
      id
      name
    }
  }
}
    `;
export type UpdateEmployeeMutationFn = Apollo.MutationFunction<UpdateEmployeeMutation, UpdateEmployeeMutationVariables>;

/**
 * __useUpdateEmployeeMutation__
 *
 * To run a mutation, you first call `useUpdateEmployeeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateEmployeeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateEmployeeMutation, { data, loading, error }] = useUpdateEmployeeMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateEmployeeMutation(baseOptions?: Apollo.MutationHookOptions<UpdateEmployeeMutation, UpdateEmployeeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateEmployeeMutation, UpdateEmployeeMutationVariables>(UpdateEmployeeDocument, options);
      }
export type UpdateEmployeeMutationHookResult = ReturnType<typeof useUpdateEmployeeMutation>;
export type UpdateEmployeeMutationResult = Apollo.MutationResult<UpdateEmployeeMutation>;
export type UpdateEmployeeMutationOptions = Apollo.BaseMutationOptions<UpdateEmployeeMutation, UpdateEmployeeMutationVariables>;
export const UpdateInvoiceDocument = gql`
    mutation UpdateInvoice($updateInvoiceInput: UpdateInvoiceInput!) {
  updateInvoice(updateInvoiceInput: $updateInvoiceInput) {
    id
  }
}
    `;
export type UpdateInvoiceMutationFn = Apollo.MutationFunction<UpdateInvoiceMutation, UpdateInvoiceMutationVariables>;

/**
 * __useUpdateInvoiceMutation__
 *
 * To run a mutation, you first call `useUpdateInvoiceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateInvoiceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateInvoiceMutation, { data, loading, error }] = useUpdateInvoiceMutation({
 *   variables: {
 *      updateInvoiceInput: // value for 'updateInvoiceInput'
 *   },
 * });
 */
export function useUpdateInvoiceMutation(baseOptions?: Apollo.MutationHookOptions<UpdateInvoiceMutation, UpdateInvoiceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateInvoiceMutation, UpdateInvoiceMutationVariables>(UpdateInvoiceDocument, options);
      }
export type UpdateInvoiceMutationHookResult = ReturnType<typeof useUpdateInvoiceMutation>;
export type UpdateInvoiceMutationResult = Apollo.MutationResult<UpdateInvoiceMutation>;
export type UpdateInvoiceMutationOptions = Apollo.BaseMutationOptions<UpdateInvoiceMutation, UpdateInvoiceMutationVariables>;
export const UpdateItemInCartDocument = gql`
    mutation UpdateItemInCart($updateItemInCartInput: UpdateItemInCartInput!) {
  updateItemInCart(updateItemInCartInput: $updateItemInCartInput) {
    id
    cartItems {
      id
      image
      item_id
      item_uom_id
      nacs_category
      nacs_subcategory
      name
      oos
      price
      discounted_price
      price_purchased_at
      custom_price
      quantity
      supplier
      supplier_code
      unit_size
      qoh
      upc1
      upc2
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
    subCarts {
      cartItems {
        id
        image
        item_id
        item_uom_id
        nacs_category
        nacs_subcategory
        name
        oos
        price
        discounted_price
        custom_price
        quantity
        supplier
        supplier_code
        unit_size
        qoh
        upc1
        upc2
        uoms {
          id
          name
          supplier_id
          uom_id
          quantity
          item_id
          price
          upc
          archived
        }
      }
      supplier
    }
  }
}
    `;
export type UpdateItemInCartMutationFn = Apollo.MutationFunction<UpdateItemInCartMutation, UpdateItemInCartMutationVariables>;

/**
 * __useUpdateItemInCartMutation__
 *
 * To run a mutation, you first call `useUpdateItemInCartMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateItemInCartMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateItemInCartMutation, { data, loading, error }] = useUpdateItemInCartMutation({
 *   variables: {
 *      updateItemInCartInput: // value for 'updateItemInCartInput'
 *   },
 * });
 */
export function useUpdateItemInCartMutation(baseOptions?: Apollo.MutationHookOptions<UpdateItemInCartMutation, UpdateItemInCartMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateItemInCartMutation, UpdateItemInCartMutationVariables>(UpdateItemInCartDocument, options);
      }
export type UpdateItemInCartMutationHookResult = ReturnType<typeof useUpdateItemInCartMutation>;
export type UpdateItemInCartMutationResult = Apollo.MutationResult<UpdateItemInCartMutation>;
export type UpdateItemInCartMutationOptions = Apollo.BaseMutationOptions<UpdateItemInCartMutation, UpdateItemInCartMutationVariables>;
export const UpdateOrderDocument = gql`
    mutation UpdateOrder($updateOrderInput: UpdateOrderInput) {
  updateOrder(updateOrderInput: $updateOrderInput)
}
    `;
export type UpdateOrderMutationFn = Apollo.MutationFunction<UpdateOrderMutation, UpdateOrderMutationVariables>;

/**
 * __useUpdateOrderMutation__
 *
 * To run a mutation, you first call `useUpdateOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateOrderMutation, { data, loading, error }] = useUpdateOrderMutation({
 *   variables: {
 *      updateOrderInput: // value for 'updateOrderInput'
 *   },
 * });
 */
export function useUpdateOrderMutation(baseOptions?: Apollo.MutationHookOptions<UpdateOrderMutation, UpdateOrderMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateOrderMutation, UpdateOrderMutationVariables>(UpdateOrderDocument, options);
      }
export type UpdateOrderMutationHookResult = ReturnType<typeof useUpdateOrderMutation>;
export type UpdateOrderMutationResult = Apollo.MutationResult<UpdateOrderMutation>;
export type UpdateOrderMutationOptions = Apollo.BaseMutationOptions<UpdateOrderMutation, UpdateOrderMutationVariables>;
export const UpdateUserSuppliersDocument = gql`
    mutation UpdateUserSuppliers($updateUserSuppliersInput: UpdateUserSuppliersInput) {
  updateUserSuppliers(updateUserSuppliersInput: $updateUserSuppliersInput) {
    cutoffDay
    cutoffTime
    daysToDelivery
    deliveryDay
    deliveryTime
    id
    supplier
    supplierInfo {
      id
      logo
      name
      need_signup
      spotlight_image
    }
  }
}
    `;
export type UpdateUserSuppliersMutationFn = Apollo.MutationFunction<UpdateUserSuppliersMutation, UpdateUserSuppliersMutationVariables>;

/**
 * __useUpdateUserSuppliersMutation__
 *
 * To run a mutation, you first call `useUpdateUserSuppliersMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserSuppliersMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserSuppliersMutation, { data, loading, error }] = useUpdateUserSuppliersMutation({
 *   variables: {
 *      updateUserSuppliersInput: // value for 'updateUserSuppliersInput'
 *   },
 * });
 */
export function useUpdateUserSuppliersMutation(baseOptions?: Apollo.MutationHookOptions<UpdateUserSuppliersMutation, UpdateUserSuppliersMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateUserSuppliersMutation, UpdateUserSuppliersMutationVariables>(UpdateUserSuppliersDocument, options);
      }
export type UpdateUserSuppliersMutationHookResult = ReturnType<typeof useUpdateUserSuppliersMutation>;
export type UpdateUserSuppliersMutationResult = Apollo.MutationResult<UpdateUserSuppliersMutation>;
export type UpdateUserSuppliersMutationOptions = Apollo.BaseMutationOptions<UpdateUserSuppliersMutation, UpdateUserSuppliersMutationVariables>;
export const GetTagsDocument = gql`
    query GetTags($getTagsInput: GetTagsInput) {
  tags(getTagsInput: $getTagsInput) {
    name
    value
  }
}
    `;

/**
 * __useGetTagsQuery__
 *
 * To run a query within a React component, call `useGetTagsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTagsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTagsQuery({
 *   variables: {
 *      getTagsInput: // value for 'getTagsInput'
 *   },
 * });
 */
export function useGetTagsQuery(baseOptions?: Apollo.QueryHookOptions<GetTagsQuery, GetTagsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTagsQuery, GetTagsQueryVariables>(GetTagsDocument, options);
      }
export function useGetTagsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTagsQuery, GetTagsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTagsQuery, GetTagsQueryVariables>(GetTagsDocument, options);
        }
export type GetTagsQueryHookResult = ReturnType<typeof useGetTagsQuery>;
export type GetTagsLazyQueryHookResult = ReturnType<typeof useGetTagsLazyQuery>;
export type GetTagsQueryResult = Apollo.QueryResult<GetTagsQuery, GetTagsQueryVariables>;
export const HomeScreenDocument = gql`
    query HomeScreen($userId: ID, $getCategoriesInput: GetCategoriesInput, $getSectionsInput: GetSectionsInput, $getRecommendationsInput: GetRecommendationsInput) {
  categories(getCategoriesInput: $getCategoriesInput) {
    image
    name
    value
  }
  brandSpotlights(userId: $userId) {
    id
    name
    spotlight_image
  }
  spotlights(userId: $userId) {
    id
    logo
    name
    spotlight_image
  }
  sections(getSectionsInput: $getSectionsInput) {
    value
    name
    items {
      id
      name
      price
      discounted_price
      unit_size
      qoh
      qty_on_hand
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      oos
      back_in_stock_date
      created_at
      metadata
      crv
      avg_cases_per_week
      moq
      item_uom_id
      isFavorited
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
  }
  brandSections(getSectionsInput: $getSectionsInput) {
    itemsPreview {
      id
      image
      supplier_code
      name
      price
      discounted_price
      nacs_category
      unit_size
      qoh
      qty_on_hand
      upc1
      last_ordered_date
      oos
      back_in_stock_date
      created_at
      avg_cases_per_week
      moq
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
    id
    logo
    name
    spotlight_image
  }
  recommendations(getRecommendationsInput: $getRecommendationsInput) {
    item {
      id
      name
      price
      discounted_price
      unit_size
      upc1
      upc2
      nacs_category
      nacs_subcategory
      image
      supplier
      last_ordered_date
      oos
      back_in_stock_date
      created_at
      moq
      item_uom_id
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }
    num_store
    quantity
  }
}
    `;

/**
 * __useHomeScreenQuery__
 *
 * To run a query within a React component, call `useHomeScreenQuery` and pass it any options that fit your needs.
 * When your component renders, `useHomeScreenQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useHomeScreenQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *      getCategoriesInput: // value for 'getCategoriesInput'
 *      getSectionsInput: // value for 'getSectionsInput'
 *      getRecommendationsInput: // value for 'getRecommendationsInput'
 *   },
 * });
 */
export function useHomeScreenQuery(baseOptions?: Apollo.QueryHookOptions<HomeScreenQuery, HomeScreenQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<HomeScreenQuery, HomeScreenQueryVariables>(HomeScreenDocument, options);
      }
export function useHomeScreenLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<HomeScreenQuery, HomeScreenQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<HomeScreenQuery, HomeScreenQueryVariables>(HomeScreenDocument, options);
        }
export type HomeScreenQueryHookResult = ReturnType<typeof useHomeScreenQuery>;
export type HomeScreenLazyQueryHookResult = ReturnType<typeof useHomeScreenLazyQuery>;
export type HomeScreenQueryResult = Apollo.QueryResult<HomeScreenQuery, HomeScreenQueryVariables>;
export const GetCatalogTemplatesDocument = gql`
    query GetCatalogTemplates($getCatalogTemplatesInput: GetCatalogTemplatesInput!) {
  getCatalogTemplates(getCatalogTemplatesInput: $getCatalogTemplatesInput) {
    id
    config
    name
    created_at
    updated_at
  }
}
    `;

/**
 * __useGetCatalogTemplatesQuery__
 *
 * To run a query within a React component, call `useGetCatalogTemplatesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCatalogTemplatesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCatalogTemplatesQuery({
 *   variables: {
 *      getCatalogTemplatesInput: // value for 'getCatalogTemplatesInput'
 *   },
 * });
 */
export function useGetCatalogTemplatesQuery(baseOptions: Apollo.QueryHookOptions<GetCatalogTemplatesQuery, GetCatalogTemplatesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCatalogTemplatesQuery, GetCatalogTemplatesQueryVariables>(GetCatalogTemplatesDocument, options);
      }
export function useGetCatalogTemplatesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCatalogTemplatesQuery, GetCatalogTemplatesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCatalogTemplatesQuery, GetCatalogTemplatesQueryVariables>(GetCatalogTemplatesDocument, options);
        }
export type GetCatalogTemplatesQueryHookResult = ReturnType<typeof useGetCatalogTemplatesQuery>;
export type GetCatalogTemplatesLazyQueryHookResult = ReturnType<typeof useGetCatalogTemplatesLazyQuery>;
export type GetCatalogTemplatesQueryResult = Apollo.QueryResult<GetCatalogTemplatesQuery, GetCatalogTemplatesQueryVariables>;
export const GetOrdersInfoDocument = gql`
    query GetOrdersInfo($ordersV2Input: OrdersInputV2!) {
  ordersV2(ordersInput: $ordersV2Input) {
    orders {
      customerDetails {
        id
        name
      }
      id
      status
      subtotal
      config
      delivery_date
      invoice {
        id
        payment_status
        paid
        subtotal
        total
        config
      }
    }
  }
}
    `;

/**
 * __useGetOrdersInfoQuery__
 *
 * To run a query within a React component, call `useGetOrdersInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrdersInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrdersInfoQuery({
 *   variables: {
 *      ordersV2Input: // value for 'ordersV2Input'
 *   },
 * });
 */
export function useGetOrdersInfoQuery(baseOptions: Apollo.QueryHookOptions<GetOrdersInfoQuery, GetOrdersInfoQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOrdersInfoQuery, GetOrdersInfoQueryVariables>(GetOrdersInfoDocument, options);
      }
export function useGetOrdersInfoLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOrdersInfoQuery, GetOrdersInfoQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOrdersInfoQuery, GetOrdersInfoQueryVariables>(GetOrdersInfoDocument, options);
        }
export type GetOrdersInfoQueryHookResult = ReturnType<typeof useGetOrdersInfoQuery>;
export type GetOrdersInfoLazyQueryHookResult = ReturnType<typeof useGetOrdersInfoLazyQuery>;
export type GetOrdersInfoQueryResult = Apollo.QueryResult<GetOrdersInfoQuery, GetOrdersInfoQueryVariables>;