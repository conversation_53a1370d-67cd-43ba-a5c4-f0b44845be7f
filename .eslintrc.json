{"root": true, "env": {"react-native/react-native": true}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-native"], "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:react-native/all", "prettier"], "rules": {"react/prop-types": ["error", {"ignore": ["navigation", "route"]}], "react-native/no-color-literals": "off", "react-native/no-inline-styles": "off"}, "settings": {"react": {"version": "detect"}}}