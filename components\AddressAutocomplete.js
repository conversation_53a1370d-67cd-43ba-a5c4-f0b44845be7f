import React, { useState } from "react";
import { TextInput, FlatList, View, TouchableOpacity } from "react-native";
import Text from "../components/Text"


export default function AddressAutocomplete({style, onAddressSelected, backendUrl }) {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);

  const fetchSuggestions = async (text) => {
    setQuery(text);
    if (text === '') {
      setSuggestions([]);
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/api/addressAutocomplete?suggest=${text}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.predictions);
      } else {
        console.error("Failed to fetch suggestions:", response.status);
      }
    } catch (error) {
      console.error("Error fetching autocomplete results:", error);
    }
  };

  const fetchFullAddress = async (text) => {
    if (text === '') {
      return;
    }
    try {
      const response = await fetch(`${backendUrl}/api/addressAutocomplete?fullAddress=${text}`);
      if (response.ok) {
        const data = await response.json();
        console.log("full Address: ", data.fullAddress)
        return data.fullAddress;
      } else {
        console.error("Failed to fetch suggestions:", response.status);
      }
    } catch (error) {
      console.error("Error fetching autocomplete results:", error);
    }
  }; 

  const handleSelect = async (addressID) => {
    const result = await fetchFullAddress(addressID);
    setQuery(result);
    setSuggestions([]);
    if (onAddressSelected) {
      onAddressSelected(result);
    }
  };

  return (
    <View>
      <TextInput
        value={query}
        onChangeText={fetchSuggestions}
        placeholder="Example: 123 Park Ave"
        style={style}
      />
      <FlatList
        data={suggestions}
        keyExtractor={(item, index) => `${item}-${index}`}
        renderItem={({ item }) => (
          <TouchableOpacity onPress={() => handleSelect(item.placeID)}>
            <Text style={{ padding: 10 }}>{item.text}</Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}
