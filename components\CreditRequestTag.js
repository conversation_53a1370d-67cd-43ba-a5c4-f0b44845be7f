import React from "react";
import { View, Text } from "react-native";
import { useTheme } from "@react-navigation/native";
import useGlobalStyles from "../lib/useGlobalStyles";

const CreditRequestTag = ({ reason }) => {
  const globalStyles = useGlobalStyles();
  const theme = useTheme();
  const getReasonText = () => {
    switch (reason) {
      case "mispick":
        return "Mispick";
      case "damaged":
        return "Damaged";
      case "expired":
        return "Expired";
      default:
        return "";
    }
  };
  return (
    <View
      style={{
        borderRadius: 50,
        backgroundColor: theme.appColors.backdropLight,
        width: 70,
        alignItems: "center",
        paddingVertical: 4,
        marginRight: 5,
      }}
    >
      <Text
        style={[globalStyles.titleLarge, { color: theme.appColors.orangeText }]}
      >
        {getReasonText()}
      </Text>
    </View>
  );
};

export default CreditRequestTag;
