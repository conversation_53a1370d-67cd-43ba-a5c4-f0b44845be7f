import { useContext, useMemo } from "react";
import {
  FlatList,
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Image,
  PixelRatio,
} from "react-native";
import { useTheme } from "@react-navigation/native";

import { UserContext } from "../../context/userContext";
import {
  useGetCategoriesQuery,
  useGetSectionsQuery,
  useGetCategoriesBySupplierQuery,
} from "../../generated/graphql";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { createStackNavigator } from "@react-navigation/stack";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import SearchResultsScreen from "../searchResultsScreen/searchResultsScreen";
import ItemsSection from "../../components/ItemsSection";
import SelectItemsScreen from "../selectItemsScreen/selectItemsScreen";
import { cgSnacksCategories } from "../../lib/constants";
import { useTranslation } from "react-i18next";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    list: {
      backgroundColor: theme.appColors.backdropLight,
    },
  });
  return styles;
};

const CategoryScreenComponent = ({ navigation, route }) => {
  const { t } = useTranslation();
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const globalStyles = useGlobalStyles();

  function stringToGradientColors(index) {
    // Array of modern base colors
    const baseColors = [
      {
        dark: "#FE9D02",
        light: "#FED897",
      },
      {
        dark: "#BB0F12",
        light: "#FD725F",
      },
      {
        dark: "#A72092",
        light: "#FE83EB",
      },
      {
        dark: "#337BFD",
        light: "#BAD2FF",
      },
      {
        dark: "#010101",
        light: "#636363",
      },
    ];

    const color = baseColors[index % baseColors.length];
    // Return an array of two colors for the gradient, from darker to lighter
    return color.dark;
  }

  const { loading, error, data, fetchMore } = useGetCategoriesQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getCategoriesInput: {
        userId: user.id,
        pagination: {
          offset: 0,
          limit: 10,
        },
      },
    },
  });

  const {
    loading: getCategoriesLoading,
    error: getCategoriesError,
    data: getCategoriesData,
  } = useGetCategoriesBySupplierQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getCategoriesBySupplierInput: {
        supplierId: user.suppliers[0].id,
        fillItemsData: false,
      },
    },
  });

  const {
    loading: getSectionsLoading,
    error: getSectionsError,
    data: getSectionsData,
  } = useGetSectionsQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getSectionsInput: {
        userId: user.id,
        pagination: {
          offset: 0,
          limit: 10,
        },
      },
    },
  });

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  if (getCategoriesLoading && !getCategoriesData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (getCategoriesError)
    return (
      <LoadingErrorStatus
        message={getCategoriesError.message}
        errorStatus={true}
      />
    );

  if (getSectionsLoading && !getSectionsData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (getSectionsError)
    return (
      <LoadingErrorStatus
        message={getSectionsError.message}
        errorStatus={true}
      />
    );

  const sortCategories = (categories) => {
    // Create a map for efficient position lookup
    const orderMap = new Map(
      cgSnacksCategories.map((name, index) => [name, index])
    );

    return categories.sort((a, b) => {
      const aIndex = orderMap.get(a.name) ?? Number.MAX_SAFE_INTEGER;
      const bIndex = orderMap.get(b.name) ?? Number.MAX_SAFE_INTEGER;
      return aIndex - bIndex;
    });
  };

  const getCategories = (user, getCategoriesData, data) => {
    if (user.suppliers[0].id === "31") {
      const categoriesWithValue = getCategoriesData.categoriesBySupplier
        .map((c) => ({ ...c, value: c.name }))
        .concat([
          {
            name: t("common.all"),
            value: null,
            image:
              "https://attain-images-owen.s3.us-east-2.amazonaws.com/cg-all-items.png",
            ordering: -1,
          },
        ]);
      return sortCategories(categoriesWithValue);
    }

    return user.supplier_beta
      ? getCategoriesData.categoriesBySupplier.length > 0
        ? getCategoriesData.categoriesBySupplier
            .map((c) => ({ ...c, value: c.name }))
            .concat([{ name: t("common.all"), value: null, ordering: -1 }])
        : data.categories
      : data.categories;
  };
  const categories = getCategories(user, getCategoriesData, data).sort(
    (a, b) => {
      if (a?.ordering && b?.ordering) {
        return a.ordering - b.ordering;
      }
      return a.name.localeCompare(b.name);
    }
  );

  const section = getSectionsData.sections.filter(
    (section) => section.value === "order_again"
  );

  return (
    // <SafeAreaView style={globalStyles.container}>
    <FlatList
      data={categories}
      numColumns={2}
      ListHeaderComponentStyle={{ paddingTop: 5 }}
      ListHeaderComponent={
        <View>
          {section[0] && (
            <View>
              <ItemsSection
                variant="dark"
                items={section[0].items}
                title={t("common.based_on_order_history")}
                userApproved={true}
                to={"SelectItems"}
                params={{ section: section[0].value, title: section[0].name }}
                customPrices={user.custom_prices}
              />
            </View>
          )}
          <View style={{ marginTop: 25, marginBottom: 20 }}>
            <Text
              style={[
                globalStyles.text,
                globalStyles.headlineMedium,
                { paddingLeft: 30 },
              ]}
            >
              {t("title.browse_by_category")}
            </Text>
          </View>
        </View>
      }
      columnWrapperStyle={{
        justifyContent: "space-between",
        marginBottom: 10,
        paddingHorizontal: 15,
      }}
      renderItem={({ item, index }) => (
        <TouchableOpacity
          style={{
            width: "48%",
            borderRadius: 10,
          }}
          onPress={() =>
            navigation.navigate("SelectItems", {
              category: item,
              title: item.name,
            })
          }
        >
          {/* <LinearGradient
              colors={stringToGradientColors(index)}
              style={{ flex: 1, borderRadius: 10, padding: 10 }}
            > */}

          <View
            style={{
              backgroundColor: "white",
              borderRadius: 15,
              shadowColor: "black",
              shadowOffset: {
                height: 2,
                width: 2,
              },
              shadowOpacity: 0.15,
              shadowRadius: 3,
              flex: 1,
              padding: 12,
              justifyContent: "center",
            }}
          >
            <Text
              style={[
                globalStyles.headlineLarge,
                globalStyles.text,
                { textAlign: "center", paddingTop: 10 },
              ]}
            >
              {item.name}
            </Text>
            <Image
              style={{
                height: 70,
                marginTop: 15,
                resizeMode: "contain",
              }}
              source={{ uri: item.image }}
            ></Image>
          </View>
          {/* </LinearGradient> */}
        </TouchableOpacity>
      )}
      // onEndReached={() => {
      //   fetchMore({
      //     variables: {
      //       getItemsBySupplierInput: {
      //         userId: user.id,
      //         supplierId: id,
      //         pagination: {
      //           offset: data.itemsBySupplier.length,
      //           limit: 20,
      //         },
      //       },
      //     },
      //     updateQuery: (previousResult, { fetchMoreResult }) => {
      //       // Don't do anything if there weren't any new items
      //       if (
      //         !fetchMoreResult ||
      //         fetchMoreResult.itemsBySupplier.length === 0
      //       ) {
      //         return previousResult;
      //       }
      //       return {
      //         // Append the new feed results to the old one
      //         itemsBySupplier: previousResult.itemsBySupplier.concat(
      //           fetchMoreResult.itemsBySupplier
      //         ),
      //       };
      //     },
      //   });
      // }}
      // onEndReachedThreshold={0.5}
      style={styles.list}
    />
    // </SafeAreaView>
  );
};

const CategoryStack = createStackNavigator();
const fontScale = PixelRatio.getFontScale();
export default function CategoryScreen({ route }) {
  return (
    <CategoryStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 25 / fontScale,
        },
      })}
    >
      <CategoryStack.Screen
        name="CategoryScreen"
        options={{ title: "Browse" }}
        initialParams={{ supplier: route.params.supplier }}
        component={CategoryScreenComponent}
      />
      <CategoryStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{ title: "Item Details" }}
      />
      <CategoryStack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{ title: "Search Results" }}
      />
      <CategoryStack.Screen
        name="SelectItems"
        component={SelectItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
    </CategoryStack.Navigator>
  );
}
