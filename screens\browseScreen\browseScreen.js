import { useContext } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  PixelRatio,
  StatusBar,
  ScrollView,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import { createStackNavigator } from "@react-navigation/stack";

import { useGetBrandSectionsQuery } from "../../generated/graphql";
import SelectItemsScreen from "../selectItemsScreen/selectItemsScreen";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import BrowseSupplierScreen from "../browseSupplierScreen/browseSupplierScreen";
import ScannerScreen from "../scannerScreen/scannerScreen";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";

import { UserContext } from "../../context/userContext";
import useGlobalStyles from "../../lib/useGlobalStyles";
import ItemsSection from "../../components/ItemsSection";
import Promo from "../../components/Promo";
const fontScale = PixelRatio.getFontScale();
const categoryColors = [];
function BrowseScreenComponent({ navigation }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  const {
    loading: getBrandSectionsLoading,
    error: getBrandSectionsError,
    data: getBrandSectionsData,
  } = useGetBrandSectionsQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      getSectionsInput: {
        userId: user.id,
        pagination: {
          offset: 0,
          limit: 20,
        },
      },
    },
  });

  if (getBrandSectionsLoading && !getBrandSectionsData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (getBrandSectionsError)
    return (
      <LoadingErrorStatus
        message={getBrandSectionsError.message}
        errorStatus={true}
      />
    );

  return (
    <SafeAreaView style={globalStyles.container}>
      <StatusBar
        backgroundColor={theme.appColors.primary}
        barStyle="light-content"
      />
      <View style={globalStyles.navBar}>
        <Text style={[globalStyles.headlineLarge, globalStyles.textNeutral]}>
          Discover Products
        </Text>
        <Text style={[globalStyles.labelLarge, globalStyles.textNeutral]}>
          Sell these popular products consumers across the country love
        </Text>
      </View>
      <ScrollView>
        {getBrandSectionsData.brandSections.map((brandSection) => {
          return (
            <ItemsSection
              key={brandSection.name}
              to={"BrowseSupplier"}
              params={{ supplier: brandSection }}
              variant="light"
              title={brandSection.name}
              titleContent={
                <View
                  style={{
                    display: "flex",
                    alignItems: "center",
                    flexDirection: "row",
                  }}
                >
                  <Image
                    style={{ width: 30, height: 30, marginRight: 10 }}
                    source={{ uri: brandSection.logo }}
                  />
                  <Text
                    style={[globalStyles.headlineMedium, globalStyles.text]}
                  >
                    {brandSection.name}
                  </Text>
                </View>
              }
              items={brandSection.itemsPreview}
              userApproved={user.approved}
              customPrices={user.custom_prices}
            />
          );
        })}
      </ScrollView>
    </SafeAreaView>
  );
}

const BrowseStack = createStackNavigator();
export default function BrowseScreen() {
  return (
    <BrowseStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 25 / fontScale,
        },
      })}
    >
      <BrowseStack.Screen
        name="BrowseScreen"
        options={{ headerShown: false, title: "Browse" }}
        component={BrowseScreenComponent}
      />
      <BrowseStack.Screen
        name="SelectItems"
        component={SelectItemsScreen}
        options={({ route }) => ({ title: route.params.title })}
      />
      <BrowseStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
        options={{ title: "Item Details" }}
      />
      <BrowseStack.Screen
        name="BrowseSupplier"
        component={BrowseSupplierScreen}
        options={{ headerShown: false }}
      />
    </BrowseStack.Navigator>
  );
}
