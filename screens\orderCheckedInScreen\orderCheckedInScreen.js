import { useState, useContext, useRef } from "react";
import { StyleSheet, View, TouchableOpacity, FlatList } from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { Ionicons } from "@expo/vector-icons";
import Text from "../../components/Text";
import {
  useGetActionItemsQuery,
  useSendReceivingReportMutation,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";

import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import Header from "../../components/Header";
import Button from "../../components/Button";

const styles = StyleSheet.create({});

export default function OrderCheckedInScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const globalStyles = useGlobalStyles();

  const { invoiceId } = route.params;
  const [sendReceivingReport] = useSendReceivingReportMutation();

  const [text, setText] = useState("Send Report");
  const sendReport = async () => {
    try {
      const status = await sendReceivingReport({
        variables: {
          userId: user.id,
          invoiceId: invoiceId,
        },
      });
      if (status.data.sendReceivingReport === "success") {
        setText("Report will be sent within 24 hrs");
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <Header goBack={navigation.goBack} title="Report" />
      <View style={[globalStyles.lightContainer, { padding: 20 }]}>
        <Text style={globalStyles.text}>
          You're all done with receiving. Send report or go back to order
          details.
        </Text>
      </View>
      <View
        style={{
          position: "absolute",
          bottom: 50,
          marginHorizontal: 30,
        }}
      >
        <View
          style={{
            justifyContent: "center",
            flexDirection: "row",
            marginBottom: 10,
          }}
        >
          <Button variant="light" size="lg" onPress={sendReport}>
            <Text>{text}</Text>
          </Button>
        </View>
        <View
          style={{
            justifyContent: "center",
            flexDirection: "row",
          }}
        >
          <Button variant="dark" size="lg" onPress={() => navigation.pop(2)}>
            <Text>Done</Text>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}
