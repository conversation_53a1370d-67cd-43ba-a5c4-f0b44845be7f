query GetOrders($getOrdersInput: GetOrdersInput) {
  orders(getOrdersInput: $getOrdersInput) {
    id
    order_number
    status
    subtotal
    date_submitted
    orderName
    orderItems {
      id
      name
      unit_size
      price
      discounted_price
      price_purchased_at
      upc1
      upc2
      nacs_category
      nacs_subcategory
      quantity
      item_id
      image
      oos
      qoh
      metadata
      supplier
    }
    delivery_date
    supplier
    supplier_logo
    invoice {
      id
      discount
      signature
      signature_name
      paid
      credit
      payment_method
      payment_status
      notes
      subtotal
      total
    }
  }
}
