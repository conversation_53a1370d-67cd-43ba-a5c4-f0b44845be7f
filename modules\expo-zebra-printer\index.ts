// Import the native module. On web, it will be resolved to ExpoZebraPrinter.web.ts
// and on native platforms to ExpoZebraPrinter.ts
import ExpoZebraPrinterModule from "./src/ExpoZebraPrinterModule";
import { Device } from "./src/ExpoZebraPrinter.types";

export function isBluetoothEnabled(): boolean {
  return ExpoZebraPrinterModule.isBluetoothEnabled();
}

export async function getDiscoveredDevices(): Promise<Array<Device>> {
  return await ExpoZebraPrinterModule.getDiscoveredDevices();
}

export async function getDiscoveredExternalAccessories(): Promise<
  Array<Device>
> {
  return await ExpoZebraPrinterModule.getDiscoveredExternalAccessories();
}

export async function connect(uuid: string): Promise<string> {
  return await ExpoZebraPrinterModule.connect(uuid);
}

export async function connectAccessory(
  accessoryID: string,
  force?: boolean
): Promise<string> {
  return await ExpoZebraPrinterModule.connectAccessory(accessoryID, !!force);
}

export async function sendZPL(zpl: string): Promise<void> {
  return await ExpoZebraPrinterModule.sendZPL(zpl);
}

export async function sendZPLToAccessory(zpl: string): Promise<void> {
  return await ExpoZebraPrinterModule.sendZPLToAccessory(zpl);
}

export default {
  isBluetoothEnabled,
  getDiscoveredDevices,
  getDiscoveredExternalAccessories,
  connect,
  connectAccessory,
  sendZPL,
  sendZPLToAccessory,
};
