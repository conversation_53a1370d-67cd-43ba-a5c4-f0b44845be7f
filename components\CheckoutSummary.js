import { StyleSheet, Text, Image, View } from "react-native";
import ChangeBankButton from "./ChangeBankButton";
import { Icon } from "react-native-elements";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo, useState } from "react";
import { TextInput } from "react-native-gesture-handler";
import DatePicker from "react-native-date-picker";
import Button from "./Button";
import dayjs from "dayjs";
import formatDate from "../lib/formatDate";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    amount: {
      alignItems: "center",
      flexDirection: "row",
      marginLeft: 50,
    },
    boldSecondaryText: {
      color: theme.appColors.primary,
      fontSize: 15,
      marginLeft: 0,
    },
    container: {
      backgroundColor: theme.appColors.backdropLight,
      paddingBottom: 10,
    },
    deliveryDateContainer: {
      marginTop: 10,
    },
    orderNotesInput: {
      backgroundColor: "white",
      borderRadius: 10,
      borderStyle: "solid",
      borderWidth: 1,
      height: 35,
      marginTop: 10,
      padding: 10,
    },
    orderNotesText: {
      color: theme.appColors.primary,
      fontSize: 20,
      fontWeight: "600",
      marginTop: 20,
    },
    orderSummary: {
      alignItems: "center",
      flexDirection: "row",
      marginVertical: 10,
    },
    orderSummaryText: {
      color: theme.appColors.primary,
      fontSize: 20,
      fontWeight: "700",
      marginTop: 10,
    },
    paymentMethodText: {
      color: theme.appColors.primary,
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 10,
    },
    safeContainer: {
      paddingHorizontal: 20,
    },
    store: {
      alignItems: "center",
      flexDirection: "row",
    },
    storeContainer: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "center",
    },
    storeImage: {
      height: 30,
      marginRight: 5,
      width: 30,
    },
  });
  return styles;
};

export default function CheckoutSummary({
  store,
  imageUri,
  account,
  bankOnPress,
  orderNotes,
  setOrderNotes,
  singleSupplier,
  deliveryDate,
  setDeliveryDate,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const [deliveryDatePickerOpen, setDeliveryDatePickerOpen] = useState(false);

  const getAccountName = (account) => {
    if (account.type == "cc") {
      return (
        account.balanceCreditCard.brand + "*" + account.balanceCreditCard.last4
      );
    } else {
      return (
        account.balanceBankAccount.institutionName +
        "*" +
        account.balanceBankAccount.accountNumberMask
      );
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.safeContainer}>
        {!singleSupplier ? (
          <>
            <Text style={styles.orderSummaryText}>Order Summary</Text>
            <View style={styles.orderSummary}>
              <View style={styles.storeContainer}>
                <Image
                  style={styles.storeImage}
                  source={{
                    uri: imageUri,
                  }}
                />
                <Text style={styles.boldSecondaryText}>{store}</Text>
              </View>
            </View>
            <Text style={styles.paymentMethodText}>Payment Method</Text>
            {/* {account ? (
          <ChangeBankButton
            mainText={getAccountName(account)}
            buttonText={"Change"}
            onPress={bankOnPress}
          />
        ) : (
          <ChangeBankButton
            mainText={"Add a bank account"}
            buttonText={"Add"}
            onPress={bankOnPress}
            />
          )} */}
            <Text style={styles.boldSecondaryText}>Cash/Check On Delivery</Text>
          </>
        ) : store === "Whitestone Foods" ? (
          <View style={styles.deliveryDateContainer}>
            <Button onPress={() => setDeliveryDatePickerOpen(true)}>
              <Text>{`Selected Delivery Date: ${
                deliveryDate ? formatDate(deliveryDate) : "N/A"
              }`}</Text>
            </Button>
            <DatePicker
              modal
              date={deliveryDate ? dayjs(deliveryDate).toDate() : new Date()}
              onDateChange={setDeliveryDate}
              mode="date"
              open={deliveryDatePickerOpen}
              onConfirm={(date) => {
                setDeliveryDate(date);
                setDeliveryDatePickerOpen(false);
              }}
              onCancel={() => setDeliveryDatePickerOpen(false)}
            />
          </View>
        ) : null}
        {/* <Text style={styles.orderNotesText}>Notes</Text> */}
        <TextInput
          style={styles.orderNotesInput}
          placeholderTextColor="gray"
          multiline
          placeholder="(Optional) Any Additional Notes"
          onChangeText={(text) => setOrderNotes(text)}
          autoCapitalize="sentences"
          value={orderNotes}
          autoCorrect={true}
        ></TextInput>
      </View>
    </View>
  );
}
