{"common": {"welcome": "Welcome to", "loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "all": "All", "confirm": "Confirm", "owner": "Owner", "routes": "Routes", "drivers": "Drivers", "users": "Users", "all_routes": "All Routes", "all_drivers": "All Drivers", "based_on_order_history": "Based on your order history", "delivery_window": "Delivery Window"}, "order": {"orders": "Orders", "myOrders": "My Orders", "orderDetails": "Order Details", "orderStatus": "Order Status", "orderDate": "Order Date", "submitted": "Submitted", "in_transit": "In Transit", "delivered": "Delivered", "canceled": "Canceled"}, "cart": {"myCart": "My Cart", "checkout": "Checkout", "empty": "Your cart is empty", "total": "Total", "continue_checkout": "Continue Checkout", "your_cart": "Your Cart"}, "item": {"ordered_on": "Ordered on", "new": "New", "sale": "Sale", "out_of_stock": "Out of Stock", "back_in_stock": "Back in Stock", "quantity_below_moq": "Quantity is below MOQ", "quantity_must_be_at_least_moq": "Quantity must be at least MOQ", "cart_is_less_than_moq": "Cart is below MOQ"}, "settings": {"switch_language": "Switch Language", "switch_user": "Switch User", "contact_us": "Contact Us", "log_out": "Log Out", "language": "Language", "share_catalog": "Share Catalog", "see_goals": "See Goals", "sign_up_store": "Sign Up Store"}, "signupStore": {"title": "Sign Up New Store", "storeName": "Store Name", "storeNamePlaceholder": "Example: Joe's Convenience Store", "storeNameError": "Store name is required", "ownerName": "Owner Name", "ownerNamePlaceholder": "Store owner's full name", "ownerNameError": "Owner name is required", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Store phone number", "phoneNumberError": "Valid phone number required", "address": "Store Address", "addressPlaceholder": "Example: 123 Park Ave", "addressError": "Store address is required", "route": "Select Route (Optional)", "routePlaceholder": "Choose a route", "routeError": "Route is required", "submit": "Sign Up Store", "success": "Store Successfully Added!", "successMessage": "The store has been added and will appear in your route.", "error": "Sign Up Error", "errorMessage": "Something went wrong. Please try again or contact support.", "submitting": "Creating store..."}, "user": {"address": "Address", "phone_number": "Phone Number", "name": "Name", "user_selected": "User Selected", "click_to_select": "Click to Select Store"}, "bluetooth": {"enable_bluetooth": "Enable Bluetooth", "enable_bluetooth_in_settings": "Enable Bluetooth in Settings", "scan_and_select_printer": "Scan and Select Printer", "checking_bluetooth_status": "Checking Bluetooth Status...", "bluetooth_loading": "Bluetooth Loading...", "grant_bluetooth_permissions": "Grant Bluetooth Permissions"}, "search": {"search_users": "Search Users", "search_products_and_suppliers": "Search products and suppliers...", "search_products": "Search products...", "search_by_order_number": "Search by order number"}, "title": {"new_items": "New Items", "top_sellers": "Top Sellers", "home": "Home", "browse": "Browse", "orders": "Orders", "cart": "<PERSON><PERSON>", "browse_by_category": "Browse by category", "order_again": "Order Again", "recommended_for_you": "Recommended For You"}, "catalog": {"select_template": "Select Catalog Template", "show_price": "Show Price", "share": "Share", "generating": "Generating...", "loading_templates": "Loading templates...", "no_templates_found": "No catalog templates found.", "create_templates_message": "Create templates from the dashboard to share catalogs.", "select_template_first": "Please select a template first", "failed_to_generate": "Failed to generate catalog. Please try again.", "sharing_error": "Could not share the PDF file", "pdf_generated_successfully": "PDF generated and shared successfully"}}