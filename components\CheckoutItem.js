import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo, useContext } from "react";
import { UserContext } from "../context/userContext";
import { getUomName, getUomQuantity } from "../lib/uomHelpers";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      backgroundColor: theme.appColors.disabled,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      marginRight: 10,
      width: 30,
    },
    buttonText: {
      color: theme.appColors.primary,
      fontSize: 15,
      textAlign: "center",
    },
    itemContainer: {
      flex: 1,
      justifyContent: "space-between",
      margin: 10,
    },
    itemContent: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "center",
    },
    itemCountContainer: {
      justifyContent: "center",
    },
    itemImage: {
      height: 50,
      width: 50,
    },
    itemNameText: {
      color: theme.appColors.primary,
      flex: 1,
      fontSize: 12,
      fontWeight: "600",
      textAlign: "left",
      width: 170,
    },
    itemPriceUnitContainer: {
      flexDirection: "row",
    },
    uomText: {
      fontSize: 11,
      color: theme.appColors.text,
      textAlign: "left",
      fontStyle: "italic",
      marginTop: 2,
    },
    itemSubtotalContainer: {
      justifyContent: "center",
    },
    itemSubtotalDiscountedText: {
      color: theme.appColors.accent,
      marginRight: 5,
    },
    itemSubtotalOriginalText: {
      // color: theme.appColors.disabled,
      color: "grey",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
      marginRight: 5,
    },
    itemSubtotalText: {
      color: theme.appColors.primary,
      textAlign: "right",
      width: 55,
    },
    itemTextContainer: {
      flex: 2,
      justifyContent: "center",
      marginLeft: 20,
    },
    quantityText: {
      backgroundColor: theme.appColors.textNeutral,
      borderRadius: 20,
      color: theme.appColors.primary,
      fontSize: 15,
      height: 20,
      justifyContent: "center",
      textAlign: "center",
      width: 20,
    },
    standardColumn: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
    },
    unitDiscountedPriceText: {
      color: theme.appColors.accent,
      fontSize: 12,
      marginRight: 5,
      textAlign: "left",
    },
    unitOriginalPriceText: {
      fontSize: 12,
      // color: theme.appColors.disabled,
      color: "grey",
      textAlign: "left",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
      marginRight: 5,
    },
    unitSizeText: {
      color: theme.appColors.textLight,
      fontSize: 12,
      textAlign: "left",
    },
  });
  return styles;
};

export default function CheckoutItem({
  imageUri,
  itemName,
  unitSize,
  unitPrice,
  discountedUnitPrice,
  quantity,
  amount,
  discountedAmount,
  item, // Add item prop to get UOM info
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { user } = useContext(UserContext);

  // Get UOM name for display
  const selectedUomName = item?.item_uom_id
    ? getUomName(item, item.item_uom_id)
    : "Unit";

  // Calculate display quantity (divide by UOM quantity if UOM is selected)
  const uomQuantity = item?.item_uom_id
    ? getUomQuantity(item, item.item_uom_id)
    : 1;
  const displayQuantity = Number((quantity / uomQuantity).toFixed(2));

  console.log("selectedUomName", selectedUomName);
  return (
    <View style={styles.itemContainer}>
      <View style={styles.itemContent}>
        <View style={{ justifyContent: "center" }}>
          <Image
            style={styles.itemImage}
            source={{
              uri: imageUri,
            }}
          />
        </View>
        <View style={styles.itemTextContainer}>
          <Text style={styles.itemNameText} numberOfLines={2}>
            {itemName}
          </Text>
          {item?.item_uom_id && selectedUomName !== "Unit" && (
            <Text style={styles.uomText}>
              UOM: {selectedUomName} ({getUomQuantity(item, item.item_uom_id)})
            </Text>
          )}
          {/* Removing this for now, code is clunky right now and has information
          that is mostly already displayed. Might add this back after UOM feedback
          and we have a better way to display w/ the UOM information. */}
          {/* {user.show_prices && (
            discountedAmount === amount ? (
              <Text style={styles.unitSizeText}>
                ${unitPrice} / {unitSize} ct
              </Text>
            ) : (
              <View style={styles.itemPriceUnitContainer}>
                <Text style={styles.unitDiscountedPriceText}>
                  ${discountedUnitPrice}
                </Text>
                <Text style={styles.unitOriginalPriceText}>${unitPrice}</Text>
                <Text style={styles.unitSizeText}>
                  / {unitSize > 1 ? `${unitSize}ct` : "each"}
                </Text>
              </View>
            )
          )} */}
        </View>
        <View style={styles.itemCountContainer}>
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>{displayQuantity}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.itemSubtotalContainer}>
          {user.show_prices &&
            (discountedAmount === amount ? (
              <Text style={styles.itemSubtotalText}>${amount.toFixed(2)}</Text>
            ) : (
              <View>
                <Text style={styles.itemSubtotalDiscountedText}>
                  ${discountedAmount.toFixed(2)}
                </Text>
                <Text style={styles.itemSubtotalOriginalText}>
                  ${amount.toFixed(2)}
                </Text>
              </View>
            ))}
        </View>
      </View>
    </View>
  );
}
