import { PixelRatio } from "react-native";
import { createStackNavigator } from "@react-navigation/stack";

import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import ScanResultScreen from "../scanResultScreen/scanResultScreen";
import InvoiceItemScanResultScreen from "../invoiceItemScanResultScreen/invoiceItemScanResultScreen";
import InvoiceItemDetailScreen from "../invoiceItemDetailScreen/invoiceItemDetailScreen";
import ActionItemsScreen from "../actionItemsScreen/actionItemsScreen";
import ScannerScreen from "./scannerScreen";

const fontScale = PixelRatio.getFontScale();

const ScannerScreenStack = createStackNavigator();

export default function ScannerStack() {
  return (
    <ScannerScreenStack.Navigator
      screenOptions={() => ({
        headerTitleStyle: {
          fontSize: 25 / fontScale,
        },
      })}
      initialRouteName="Attain"
    >
      <ScannerScreenStack.Screen 
        name="Scanner" 
        options={{ title: "Scan Barcode" }}
        component={ScannerScreen} 
      />
      <ScannerScreenStack.Screen
        name="ItemDetail"
        component={ItemDetailScreen}
      />
      <ScannerScreenStack.Screen
        name="InvoiceItemDetailScreen"
        component={InvoiceItemDetailScreen}
        options={{ headerShown: false }}
      />
      <ScannerScreenStack.Screen
        name="ActionItemsScreen"
        component={ActionItemsScreen}
        options={{ headerShown: false }}
      />
      <ScannerScreenStack.Screen
        name="ScanResult"
        options={{ title: "Scan Results" }}
        component={ScanResultScreen}
      />
      <ScannerScreenStack.Screen
        name="InvoiceItemScanResultScreen"
        options={{ title: "Scan Results" }}
        component={InvoiceItemScanResultScreen}
      />
    </ScannerScreenStack.Navigator>
  );
}
