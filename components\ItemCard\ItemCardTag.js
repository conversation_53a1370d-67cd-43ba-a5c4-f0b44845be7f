import { StyleSheet, View } from "react-native";
import { useRoute, useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";
import useGlobalStyles from "../../lib/useGlobalStyles";
import Text from "../Text";
import { discountExists } from "../../lib/discountExists";
import { useTranslation } from "react-i18next";
import isBackInStock from "../../lib/isBackInStock";

const styles = StyleSheet.create({
  viewStyle: {
    borderRadius: 25,
    left: 15,
    overflow: "hidden",
    paddingBottom: 2,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 2,
    position: "absolute",
    top: 10,
  },
});

const ItemCardTag = ({ item }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const { t } = useTranslation();
  const route = useRoute();
  const currentScreenName = route.name;

  function getTag() {
    if (typeof item.oos === "boolean" && item.oos) {
      return {
        tag: t("item.out_of_stock"),
        color: theme.appColors.orangeText,
      };
    } else if (typeof item.oos === "string" && item.oos === "TRUE") {
      return {
        tag: t("item.out_of_stock"),
        color: theme.appColors.orangeText,
      };
    } else if (isBackInStock(item.back_in_stock_date) && !item.oos) {
      return {
        tag: t("item.back_in_stock"),
        color: theme.appColors.greenText,
      };
    } else if (discountExists(item)) {
      return {
        tag: t("item.sale"),
        color: theme.appColors.accent,
      };
    } else if (isNewItem(item.created_at)) {
      return {
        tag: t("item.new"),
        color: theme.appColors.backdropDark,
      };
    } else {
      return null;
    }
  }

  const isNewItem = (created_at) => {
    const today = new Date();
    const date = new Date(created_at);
    const diffTime = Math.abs(today - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 14;
  };
  const tag = getTag();
  if (!tag) return null;

  return (
    <>
      <View style={[styles.viewStyle, { backgroundColor: tag.color }]}>
        <Text style={[globalStyles.headlineMedium, globalStyles.textNeutral]}>
          {tag.tag}
        </Text>
      </View>
    </>
  );
};

ItemCardTag.propTypes = {
  item: PropTypes.object.isRequired,
};

export default ItemCardTag;
