query GetItems($getItemsInput: GetItemsInput) {
  items(getItemsInput: $getItemsInput) {
    id
    name
    unit_size
    size
    price
    discounted_price
    upc1
    upc2
    nacs_category
    nacs_subcategory
    supplier
    image
    qoh
    qty_on_hand
    last_ordered_date
    avg_cases_per_week
    oos
    created_at
    metadata
    back_in_stock_date
    moq
    uoms {
      id
      name
      supplier_id
      uom_id
      quantity
      item_id
      price
      upc
      archived
    }
    crv
    min_sale_price
    isFavorited
    supplier_info {
      id
      name
      need_signup
    }
    related_items {
        id
        name
        unit_size
        price
        discounted_price
        upc1
        upc2
        nacs_category
        nacs_subcategory
        supplier
        image
        last_ordered_date
        qoh
        qty_on_hand
        oos
        created_at
        isFavorited
        back_in_stock_date    
    }
  }
}
