import { useState, useContext, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  FlatList,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";

import Text from "../../components/Text";
import {
  useGetOrdersQuery,
  useGetInvoiceQuery,
  useGetInvoiceItemsQuery,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";

import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import OrderSupplierIcon from "../../components/OrderSupplierIcon";
import Header from "../../components/Header";
import Button from "../../components/Button";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    clicked: {
      borderColor: "#DEDEDE",
      borderWidth: 1,
      padding: 4,
    },
    deliveryGraphic: {
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 37,
    },
    deliveryGraphicLine: {
      borderColor: "red",
      borderRadius: 1,
      borderStyle: "dashed",
      borderWidth: 2,
      flex: 1,
    },
    deliveryGraphicLineClip: {
      flex: 1,
      height: 2,
      marginHorizontal: -2,
      overflow: "hidden",
    },
    deliveryGraphicPoint: {
      borderRadius: 5,
      height: 10,
      width: 10,
      zIndex: 2,
    },
    headerContainer: {
      alignItems: "center",
      paddingBottom: 20,
      paddingHorizontal: 20,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    itemImage: {
      height: 55,
      width: 55,
    },
    itemInfoContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    itemPrice: {
      marginLeft: 8,
    },
    itemTextContainer: {
      marginLeft: 8,
      width: 180,
    },
    list: {
      backgroundColor: "white",
      height: "100%",
    },
    listHeader: {
      margin: 20,
    },
    oosText: {
      color: "gray",
      textDecorationLine: "line-through",
      textDecorationStyle: "solid",
    },
    orderItemContainer: {
      marginBottom: 15,
      paddingHorizontal: 20,
    },
    orderNameContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: 6,
    },
    orderSummaryView: {
      alignSelf: "stretch",
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    pencilIconStyle: {
      paddingRight: 5,
    },
    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      width: 30,
    },
    tag: {
      marginBottom: 10,
      marginHorizontal: -3,
      marginTop: 20,
    },
    truck: {
      marginLeft: 28,
      marginRight: 25,
      marginVertical: 5,
    },
  });
  return styles;
};

export default function InvoiceScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();

  const { order, invoiceId: existingInvoiceId } = route.params;
  const [invoiceId, setInvoiceId] = useState(existingInvoiceId || null);

  // if (getOrdersLoading && !getOrdersData)
  //   return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  // if (getOrdersError)
  //   return (
  //     <LoadingErrorStatus message={getOrdersError.message} errorStatus={true} />
  //   );

  const {
    loading: getInvoiceLoading,
    data: getInvoiceData,
    error: getInvoiceError,
  } = useGetInvoiceQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      orderId: order.id,
      invoiceId: invoiceId,
    },
    pollInterval: 15000,
    skip: invoiceId,
  });

  const {
    loading: getInvoiceItemsLoading,
    data: getInvoiceItemsData,
    error: getInvoiceItemsError,
  } = useGetInvoiceItemsQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      invoiceId: invoiceId,
    },
    skip: invoiceId === null,
    pollInterval: 500,
  });

  useEffect(() => {
    if (!getInvoiceLoading && getInvoiceData) {
      if (!getInvoiceData.invoice.processing) {
        setInvoiceId(getInvoiceData.invoice.invoice.id);
      }
    }
  }, [getInvoiceData, getInvoiceLoading]);

  if (getInvoiceLoading && !getInvoiceData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (
    !getInvoiceLoading &&
    getInvoiceData &&
    getInvoiceData.invoice.processing === true
  )
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getInvoiceError)
    return (
      <LoadingErrorStatus
        message={getInvoiceError.message}
        errorStatus={true}
      />
    );

  if (invoiceId === null)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getInvoiceItemsLoading && !getInvoiceItemsData)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getInvoiceItemsError)
    return (
      <LoadingErrorStatus
        message={getInvoiceItemsError.message}
        errorStatus={true}
      />
    );

  const invoiceLineItems = getInvoiceItemsData.invoiceItems;

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={{
          marginHorizontal: 30,
          marginVertical: 10,
          paddingHorizontal: 15,
          paddingVertical: 8,
          justifyContent: "space-between",
          flexDirection: "row",
          borderWidth: 1,
          borderColor: theme.appColors.backdropDark,
          borderRadius: 10,
        }}
        onPress={() =>
          // navigation.navigate("ActionItemsScreen", { invoiceItemId: 1 })

          navigation.navigate("InvoiceItemDetailScreen", {
            invoiceItem: item,
            invoiceId: invoiceId,
          })
        }
      >
        <View style={{ justifyContent: "center", width: "80%" }}>
          <Text
            numberOfLines={1}
            style={[globalStyles.text, globalStyles.headlineSmall]}
          >
            {item.name}
          </Text>
        </View>
        <View
          style={{
            borderWidth: 1,
            borderColor: theme.appColors.backdropLight,
            borderRadius: 5,
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 8,
            paddingVertical: 6,
            width: "17%",
          }}
        >
          <Text style={[globalStyles.text, globalStyles.headlineSmall]}>
            {item.checked_in_quantity || 0}/{item.quantity}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <SafeAreaView style={globalStyles.container}>
      <Header goBack={navigation.goBack} title="Check In" />
      <View style={{ flex: 1 }}>
        <FlatList
          ListHeaderComponent={
            <>
              <View style={styles.headerContainer}>
                <View style={styles.orderSummaryView}>
                  <View>
                    <View style={styles.orderNameContainer}>
                      <Text
                        style={[
                          globalStyles.headlineMedium,
                          globalStyles.textNeutral,
                        ]}
                      >
                        {`${order?.subtotal >= 0 ? "Order" : "Credit"} #${
                          order?.order_number
                        }`}
                      </Text>
                    </View>
                    <View>
                      <OrderSupplierIcon order={order} dark />
                    </View>
                  </View>
                  <View
                    style={{
                      justifyContent: "center",
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        paddingLeft: 10,
                        paddingRight: 10,
                        borderRadius: 5,
                        shadowOpacity: 0.1,
                        shadowOffset: { height: 3 },
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: theme.appColors.backdropLight,
                        padding: 5,
                      }}
                      onPress={() => {
                        navigation.navigate("Scan", {
                          screen: "Scanner",
                          params: {
                            type: "invoiceItem",
                            invoiceId: invoiceId,
                          },
                        });
                      }}
                    >
                      <Image
                        style={{ height: 24, width: 24 }}
                        source={theme.assets.barcode}
                      />
                      <Text
                        style={[
                          globalStyles.text,
                          globalStyles.headlineSmall,
                          { marginLeft: 5 },
                        ]}
                      >
                        Scan
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <Text
                style={[
                  globalStyles.text,
                  globalStyles.headlineMedium,
                  { marginLeft: 40, marginBottom: 10 },
                ]}
              >
                Input Received Quantity
              </Text>
            </>
          }
          data={invoiceLineItems}
          numColumns={1}
          renderItem={renderItem}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
          ListFooterComponentStyle={{
            flex: 1,
            justifyContent: "flex-end",
            marginTop: 10,
          }}
          ListFooterComponent={
            <>
              <View
                style={{
                  justifyContent: "center",
                  flexDirection: "row",
                }}
              >
                <Button
                  size="lg"
                  onPress={() =>
                    navigation.navigate("OrderCheckedInScreen", {
                      invoiceId: invoiceId,
                    })
                  }
                >
                  <Text>Done</Text>
                </Button>
              </View>
            </>
          }
          keyExtractor={(item) => item.id}
          style={styles.list}
        />
      </View>
    </SafeAreaView>
  );
}
