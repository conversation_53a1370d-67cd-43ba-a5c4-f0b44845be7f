import { useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import PropTypes from "prop-types";
import { useTheme } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

import Text from "./Text";
import useGlobalStyles from "../lib/useGlobalStyles";
import { posthog } from "../src/posthog";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    closeButton: {
      alignSelf: "flex-start",
      marginTop: -10,
    },
    container: {
      alignItems: "center",
      backgroundColor: theme.appColors.accent,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      bottom: 0,
      flexDirection: "row",
      justifyContent: "space-between",
      left: 0,
      paddingLeft: 20,
      paddingRight: 20,
      paddingVertical: 15,
      position: "absolute",
      right: 0,
    },
    shopButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropNeutral,
      borderRadius: 8,
      paddingVertical: 3,
      width: 80,
    },
    shopText: {
      color: theme.appColors.accent,
    },
  });
  return styles;
};

const Promo = ({ message, subMessage, supplier }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const [closed, close] = useState(false);
  const navigation = useNavigation();
  const globalStyles = useGlobalStyles();

  if (closed) {
    return <></>;
  }

  return (
    <View style={styles.container}>
      <View>
        <Text style={[globalStyles.headlineMedium, globalStyles.textNeutral]}>
          {message}
        </Text>
        <Text style={[globalStyles.bodyMedium, globalStyles.textNeutral]}>
          {subMessage}
        </Text>
      </View>
      <TouchableOpacity
        onPress={() => {
          posthog.capture("promo_clicked", { supplier: supplier.name });
          navigation.navigate("Browse");
        }}
        style={styles.shopButton}
      >
        <Text style={[globalStyles.headlineSmall, styles.shopText]}>Shop</Text>
      </TouchableOpacity>
      {/* <TouchableOpacity onPress={() => close(true)} style={styles.closeButton}>
        <Ionicons name="close" color={theme.colors.text} size={25} />
      </TouchableOpacity> */}
    </View>
  );
};

Promo.propTypes = {
  message: PropTypes.string.isRequired,
  subMessage: PropTypes.string.isRequired,
  supplier: PropTypes.object.isRequired,
};

export default Promo;
