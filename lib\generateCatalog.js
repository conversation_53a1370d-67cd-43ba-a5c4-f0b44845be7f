import { Alert, Share, Platform } from "react-native";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
// Todo: Have this comming from some env during build since its not possible to get userid here.
const BACKEND_URL = "https://attain-server.herokuapp.com/api";
/**
 * Generates a catalog PDF using the backend API
 * @param {string} supplierId - The ID of the supplier
 * @param {Object} config - Catalog configuration object with pricing override
 * @param {string} templateId - Template ID to use for generation
 * @param {boolean} showPrice - Whether to show pricing (overrides template config)
 * @returns {Promise} Promise with the API response
 */
export const generateCatalogPdf = async ({
  supplierId,
  config,
  templateId,
  showPrice = true,
  userId,
}) => {
  try {
    // Override the pricing_enabled in config based on user selection
    const updatedConfig = {
      ...config,
      pricing_enabled: showPrice,
    };

    const response = await fetch(`${BACKEND_URL}/generateCatalog`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        supplierId,
        config: updatedConfig,
        catalogTemplateId: templateId,
        userId,
      }),
    });

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorResult = await response.json();
        errorMessage = errorResult.error || errorMessage;
      } catch {
        // If response is not JSON, use the status message
      }
      throw new Error(errorMessage);
    }

    // Get the PDF as a blob
    const pdfBlob = await response.blob();

    console.log("pdfBlob", pdfBlob);

    // Convert blob to base64 for mobile file system
    const reader = new FileReader();
    return new Promise((resolve, reject) => {
      reader.onload = async () => {
        try {
          const base64Data = reader.result.split(",")[1];
          const fileUri = `${
            FileSystem.documentDirectory
          }catalog-${supplierId}-${Date.now()}.pdf`;

          // Write the file to device storage
          await FileSystem.writeAsStringAsync(fileUri, base64Data, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Share the PDF
          await sharePdf(fileUri);

          resolve({
            success: true,
            fileUri,
            message: "PDF generated and shared successfully",
          });
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error("Failed to process PDF"));
      };

      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error("Error generating catalog PDF:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while generating the PDF";

    Alert.alert("Error Generating PDF", errorMessage);

    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Shares the PDF file using the device's sharing capabilities
 * @param {string} fileUri - The local file URI of the PDF
 */
const sharePdf = async (fileUri) => {
  try {
    // Use Expo Sharing for both platforms for better PDF handling
    const isAvailable = await Sharing.isAvailableAsync();
    if (isAvailable) {
      await Sharing.shareAsync(fileUri, {
        mimeType: "application/pdf",
        dialogTitle: "Share Catalog",
        UTI: "com.adobe.pdf", // For iOS PDF recognition
      });
    } else {
      // Fallback to React Native Share API
      if (Platform.OS === "android") {
        await Share.share({
          url: fileUri,
          title: "Share Catalog",
          type: "application/pdf",
        });
      } else {
        throw new Error("Sharing is not available on this device");
      }
    }
  } catch (error) {
    console.error("Error sharing PDF:", error);
    Alert.alert("Sharing Error", "Could not share the PDF file");
  }
};

/**
 * Alternative implementation using fetch without FileReader (for React Native)
 * This version might work better in some React Native environments
 */
export const generateCatalogPdfAlternative = async ({
  supplierId,
  config,
  templateId,
  showPrice = true,
}) => {
  try {
    // Override the pricing_enabled in config based on user selection
    const updatedConfig = {
      ...config,
      pricing_enabled: showPrice,
    };

    const response = await fetch(`${BACKEND_URL}/generateCatalog`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        supplierId,
        config: updatedConfig,
        catalogTemplateId: templateId,
      }),
    });

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorResult = await response.json();
        errorMessage = errorResult.error || errorMessage;
      } catch {
        // If response is not JSON, use the status message
      }
      throw new Error(errorMessage);
    }

    // Download the PDF directly
    const fileUri = `${
      FileSystem.documentDirectory
    }catalog-${supplierId}-${Date.now()}.pdf`;

    const downloadResult = await FileSystem.downloadAsync(
      `${BACKEND_URL}/generateCatalog`,
      fileUri,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          supplierId,
          config: updatedConfig,
          catalogTemplateId: templateId,
        }),
      }
    );

    if (downloadResult.status === 200) {
      // Share the PDF
      await sharePdf(downloadResult.uri);

      return {
        success: true,
        fileUri: downloadResult.uri,
        message: "PDF generated and shared successfully",
      };
    } else {
      throw new Error("Failed to download PDF");
    }
  } catch (error) {
    console.error("Error generating catalog PDF:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "An unexpected error occurred while generating the PDF";

    Alert.alert("Error Generating PDF", errorMessage);

    return {
      success: false,
      error: errorMessage,
    };
  }
};

export default generateCatalogPdf;
