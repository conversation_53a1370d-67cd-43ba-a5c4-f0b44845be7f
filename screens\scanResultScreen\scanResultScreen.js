import { useContext } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  PixelRatio,
  StatusBar,
  ScrollView,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import Text from "../../components/Text";
import { createStackNavigator } from "@react-navigation/stack";

import {
  useGetItemsQuery,
  useGetItemsBySupplierQuery,
  useUpdateItemInCartMutation,
} from "../../generated/graphql";
import SelectItemsScreen from "../selectItemsScreen/selectItemsScreen";
import ItemDetailScreen from "../itemDetailScreen/itemDetailScreen";
import BrowseSupplierScreen from "../browseSupplierScreen/browseSupplierScreen";
import ScannerScreen from "../scannerScreen/scannerScreen";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";

import { UserContext } from "../../context/userContext";
import useGlobalStyles from "../../lib/useGlobalStyles";
import Categories from "../../components/Categories";
import ItemsSection from "../../components/ItemsSection";
import Promo from "../../components/Promo";
import { posthog } from "../../src/posthog";
const screenWidth = Dimensions.get("window").width;
const fontScale = PixelRatio.getFontScale();

const styles = StyleSheet.create({
  bodyText: {
    fontSize: 15,
  },
  categoryImage: {
    aspectRatio: 1,
    marginBottom: 5,
    width: "50%",
  },
  categoryView: {
    borderColor: "gray",
    borderRadius: 15,
    borderStyle: "solid",
    borderWidth: 1,
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
    width: screenWidth * 0.42,
  },
  container: {
    backgroundColor: "white",
    height: "100%",
    paddingTop: 10,
  },
});

const categoryColors = [];
export default function ScanResultScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  const { id, supplier, upcCode } = route.params;

  posthog.capture("scanned_item", {
    supplier: supplier,
    upcCode: upcCode,
    id,
  });

  const bySupplier = supplier && supplier.id;

  const itemsResult = useGetItemsQuery({
    fetchPolicy: "cache-and-network",
    skip: bySupplier,
    variables: {
      getItemsInput: {
        userId: user.id,
        ids: id ? [id] : null,
        upcs: upcCode ? [upcCode] : null,
        pagination: {
          limit: 1,
          offset: 0,
        },
      },
    },
  });

  const itemsBySupplierResult = useGetItemsBySupplierQuery({
    fetchPolicy: "cache-and-network",
    skip: !bySupplier,
    variables: {
      getItemsBySupplierInput: {
        supplierId: bySupplier ? supplier.id : null,
        userId: user.id,
        upc: upcCode ? upcCode : null,
        pagination: {
          limit: 1,
          offset: 0,
        },
      },
    },
  });

  const { loading, error, data } = bySupplier
    ? itemsBySupplierResult
    : itemsResult;

  if (loading && !data)
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  if (error)
    return <LoadingErrorStatus message={error.message} errorStatus={true} />;

  const items = bySupplier ? data.itemsBySupplier : data.items;

  const scanError = (
    <LoadingErrorStatus
      message="Sorry, we can't scan this item. Try searching for it instead."
      errorStatus={true}
    />
  );

  if (items.length === 0) {
    return scanError;
  }

  const getSections = (items) => {
    var result = {};
    for (var i = 0; i < items.length; i++) {
      const cur = items[i];
      if (result[cur.supplier]) {
        result[cur.supplier].push(cur);
      } else {
        result[cur.supplier] = [cur];
      }
    }

    return Object.entries(result).map((entry) => {
      return {
        supplier: entry[0],
        mySupplier: user.suppliers
          .map((supplier) => supplier.name)
          .includes(entry[0]),
        needSignup: supplier ? false : entry[1][0].supplier_info.need_signup,
        items: entry[1],
      };
    });
  };

  const sections = getSections(items);
  const mySupplierSections = sections.filter(
    (section) => section.mySupplier === true
  );
  const otherSupplierSections = sections.filter(
    (section) => section.mySupplier !== true
  );

  if (user.supplier_beta && mySupplierSections.length === 0) {
    return scanError;
  }

  return (
    <View
      style={[
        globalStyles.container,
        { backgroundColor: theme.appColors.surface },
      ]}
    >
      <StatusBar
        backgroundColor={theme.appColors.primary}
        barStyle="light-content"
      />

      <ScrollView>
        {mySupplierSections.map((section) => {
          return (
            <ItemsSection
              key={section.supplier}
              to={"BrowseSupplier"}
              //   params={{ supplier: item.supplier }}
              variant={"light"}
              title={section.supplier}
              items={section.items}
              userApproved={user.approved}
              customPrices={user.custom_prices}
            />
          );
        })}
        {otherSupplierSections.map((section) => {
          return (
            <View style={{ marginTop: 20 }} key={section.supplier}>
              <ItemsSection
                key={section.supplier}
                to={"BrowseSupplier"}
                //   params={{ supplier: item.supplier }}
                variant={"dark"}
                title={section.supplier}
                needSignup={true}
                items={section.items}
                userApproved={user.approved}
                customPrices={user.custom_prices}
              />
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}

const ScanResultStack = createStackNavigator();
// export default function ScanResultScreen() {
//   return (
//     <ScanResultStack.Navigator
//       screenOptions={() => ({
//         headerTitleStyle: {
//           fontSize: 25 / fontScale,
//         },
//       })}
//     >
//       <ScanResultStack.Screen
//         name="BrowseScreen"
//         options={{ headerShown: false, title: "Browse" }}
//         component={ScanScreenComponent}
//       />
//       <ScanResultStack.Screen
//         name="SelectItems"
//         component={SelectItemsScreen}
//         options={({ route }) => ({ title: route.params.title })}
//       />
//       <ScanResultStack.Screen
//         name="ItemDetail"
//         component={ItemDetailScreen}
//         options={{ title: "Item Details" }}
//       />
//       <ScanResultStack.Screen
//         name="BrowseSupplier"
//         component={BrowseSupplierScreen}
//         options={{ headerShown: false }}
//       />
//       <ScanResultStack.Screen
//         name="SupplierScanner"
//         component={ScannerScreen}
//         options={({ route }) => ({
//           title: route.params.supplier.name + " Scanner",
//         })}
//       />
//     </ScanResultStack.Navigator>
//   );
// }
