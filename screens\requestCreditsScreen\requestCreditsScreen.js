import { useState, useContext } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import useGlobalStyles from "../../lib/useGlobalStyles";
import CheckBox from "@react-native-community/checkbox";
import { UserContext } from "../../context/userContext";
import OrderSupplierIcon from "../../components/OrderSupplierIcon";
import Header from "../../components/Header";
import Button from "../../components/Button";
import EditCreditRequestModal from "../../components/EditCreditRequestModal";
import OrderItemWithoutPrice from "../../components/OrderItemWithoutPrice";
import Text from "../../components/Text";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    headerContainer: {
      alignItems: "center",
      paddingBottom: 20,
      paddingHorizontal: 20,
    },
    list: {
      backgroundColor: "white",
      height: "100%",
    },
    orderNameContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: 6,
    },
    orderSummaryView: {
      alignSelf: "stretch",
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      padding: 20,
    },
    quantity: {
      alignItems: "center",
      backgroundColor: theme.appColors.surface,
      borderRadius: 10,
      height: 30,
      justifyContent: "center",
      marginLeft: 20,
      width: 30,
    },
  });
  return styles;
};

export default function RequestCreditsScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();
  const [modalOpen, setModalOpen] = useState(false);
  const toggleModal = () => {
    setModalOpen(!modalOpen);
  };
  const { order } = route.params;
  const [selectedItems, setSelectedItems] = useState(new Set());
  const handleCheckboxChange = (item) => {
    // Create a new Set from the current selectedItems Set
    const newSelectedItems = new Set(selectedItems);
    if (newSelectedItems.has(item)) {
      // If the item is already in the Set, remove it
      newSelectedItems.delete(item);
    } else {
      // If the item is not in the Set, add it
      newSelectedItems.add(item);
    }
    // Update the state with the new Set
    setSelectedItems(newSelectedItems);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          flexDirection: "row",
          marginHorizontal: 20,
          marginBottom: 20,
        }}
      >
        <View style={{ flexDirection: "column", justifyContent: "center" }}>
          <CheckBox
            boxType="square"
            onFillColor={theme.appColors.orangeText}
            onCheckColor={theme.appColors.surface}
            tintColor={theme.appColors.orangeText}
            onTintColor={theme.appColors.orangeText}
            animationDuration={0}
            style={{ width: 20, height: 20, marginRight: 10 }}
            onValueChange={(value) => {
              handleCheckboxChange(item);
            }}
            value={selectedItems.has(item)}
          />
        </View>

        <OrderItemWithoutPrice item={item} quantity={true} />
      </View>
    );
  };

  const submitRequest = (items, order) => {
    setModalOpen(false);
    navigation.navigate("CreditRequestsConfirmationScreen", {
      items: items,
      order: order,
    });
  };

  return (
    <SafeAreaView style={globalStyles.container}>
      <Header goBack={navigation.goBack} title="Check In" />
      {selectedItems.size > 0 && modalOpen && (
        <EditCreditRequestModal
          modalOpen={modalOpen}
          toggleModal={toggleModal}
          items={Array.from(selectedItems)}
          submitRequests={(items) => submitRequest(items, order)}
        />
      )}
      <View style={{ flex: 1 }}>
        <FlatList
          ListHeaderComponent={
            <>
              <View style={styles.headerContainer}>
                <View style={styles.orderSummaryView}>
                  <View>
                    <TouchableOpacity style={styles.orderNameContainer}>
                      <TextInput
                        style={[globalStyles.headlineLarge, globalStyles.text]}
                        defaultValue={`Order #${order.order_number}`}
                        maxLength={32}
                        numberOfLines={1}
                      />
                    </TouchableOpacity>
                    <View>
                      <OrderSupplierIcon order={order} dark />
                    </View>
                  </View>
                </View>
              </View>
            </>
          }
          data={order.orderItems}
          numColumns={1}
          renderItem={renderItem}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 80 }}
          keyExtractor={(item) => item.id}
          style={styles.list}
        />

        <Button
          size="lg"
          style={{
            position: "absolute",
            bottom: 20,
            alignSelf: "center",
          }}
          onPress={toggleModal}
        >
          <Text>Continue</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}
