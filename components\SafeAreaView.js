import { StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import PropTypes from "prop-types";

const SafeAreaView = ({ children, style, edges, ...rest }) => {
  const insets = useSafeAreaInsets();
  const defaultEdges = edges === undefined;
  return (
    <View
      style={StyleSheet.compose(
        {
          paddingTop:
            defaultEdges || edges?.includes("top") ? insets.top : undefined,
          paddingBottom: edges?.includes("bottom") ? insets.bottom : undefined,
          paddingLeft:
            defaultEdges || edges?.includes("left") ? insets.left : undefined,
          paddingRight:
            defaultEdges || edges?.includes("right") ? insets.right : undefined,
        },
        style
      )}
      {...rest}
    >
      {children}
    </View>
  );
};

SafeAreaView.propTypes = {
  children: PropTypes.node,
  numberOfLines: PropTypes.number,
  style: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  edges: PropTypes.arrayOf(PropTypes.oneOf(["top", "bottom", "left", "right"])),
};

export default SafeAreaView;
