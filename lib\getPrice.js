import { discountExists } from "./discountExists";

export const getPrice = (item, custom_prices = []) => {
  let item_price =
    item.price_purchased_at ??
    (discountExists(item) ? item.discounted_price : item.price);
  if (custom_prices.length) {
    const customPrice = custom_prices.find(
      (i) => i.item_id === item.id || i.item_id === item.item_id
    );
    if (customPrice) {
      if (item.discounted_price) {
        item_price = customPrice.price;
      }
      item_price = customPrice.price;
    }
  }
  if (!item_price) {
    item_price = 0.0;
  }
  return parseFloat(item_price).toFixed(2);
};

export const getCustomPrice = (item) => {
  if (item.custom_price !== undefined && item.custom_price !== null) {
    return parseFloat(item.custom_price).toFixed(2);
  }

  let item_price =
    item.price_purchased_at ??
    (discountExists(item) ? item.discounted_price : item.price);

  return parseFloat(item_price).toFixed(2);
};
