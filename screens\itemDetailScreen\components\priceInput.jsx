import { View, TextInput } from "react-native";
import PropTypes from "prop-types";
import Text from "../../../components/Text";

const PriceInput = ({ value, onValueChange, theme, globalStyles }) => {
  const handleTextChange = (text) => {
    const cleanText = text.replace("$", "");
    onValueChange(cleanText);
  };

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        borderWidth: 1,
        borderColor: theme.appColors.primary,
        borderRadius: 8,
        height: 35,
        paddingHorizontal: 8,
        backgroundColor: "white",
        minWidth: 80,
      }}
    >
      <Text style={[globalStyles.headlineMedium, globalStyles.text]}>$</Text>

      {/* Price Input */}
      <TextInput
        style={[
          globalStyles.headlineMedium,
          globalStyles.text,
          {
            flex: 1,
            textAlign: "left",
            paddingVertical: 0,
            marginLeft: 2,
            minWidth: 40,
          },
        ]}
        value={value.toString()}
        onChangeText={handleTextChange}
        keyboardType="decimal-pad"
        placeholder="0.00"
        placeholderTextColor={theme.appColors.textLight}
      />
    </View>
  );
};

PriceInput.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onValueChange: PropTypes.func.isRequired,
  theme: PropTypes.object.isRequired,
  globalStyles: PropTypes.object.isRequired,
};

export default PriceInput;
