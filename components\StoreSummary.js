import { StyleSheet, Text, View, Image, TouchableOpacity } from "react-native";
import useGlobalStyles from "../lib/useGlobalStyles";
import { useTheme } from "@react-navigation/native";
import React, { useMemo } from "react";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    button: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 8,
      height: 25,
      justifyContent: "center",
      marginRight: 20,
      paddingHorizontal: 10,
    },
    buttonContainer: {
      justifyContent: "center",
    },
    buttonText: {
      color: "white",
      fontSize: 14,
      fontWeight: theme.fonts.medium.fontWeight,
      textAlign: "center",
    },
    container: {
      backgroundColor: theme.appColors.backdropLight,
      display: "flex",
      flexDirection: "row",
      height: 50,
      justifyContent: "space-between",
      paddingHorizontal: 0,
    },
    mainText: {
      color: theme.appColors.text,
      fontSize: 15,
      fontWeight: theme.fonts.medium.fontWeight,
      marginLeft: 10,
    },
    standardColumn: {
      justifyContent: "center",
    },
    storeContainer: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "center",
      marginLeft: 30,
      marginVertical: 10,
    },
    storeImage: {
      height: 30,
      width: 30,
    },
  });
  return styles;
};

export default function StoreSummary({
  imageUri,
  store,
  onPress,
  buttonText,
  buttonPressed,
}) {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <View style={styles.container}>
      <View style={styles.storeContainer}>
        <Image
          style={styles.storeImage}
          source={{
            uri: imageUri,
          }}
        />
        <Text style={styles.mainText}>{store}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.button,
            buttonPressed
              ? { backgroundColor: theme.appColors.orangeText }
              : null,
          ]}
          onPress={onPress}
        >
          <Text style={styles.buttonText}>
            {buttonPressed ? "Confirm" : buttonText}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
