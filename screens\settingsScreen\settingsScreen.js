import { useTheme } from "@react-navigation/native";
import * as Linking from "expo-linking";
import { getAuth, signOut } from "firebase/auth";
import PropTypes from "prop-types";
import { useContext, useEffect, useState } from "react";
import {
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Modal from "react-native-modal";
import {
  PERMISSIONS,
  RESULTS,
  requestMultiple,
} from "react-native-permissions";
import RNZebraBluetoothPrinter from "react-native-zebra-bluetooth-printer";
import Button from "../../components/Button";
import ButtonToggle from "../../components/ButtonToggle";
import SelectPrinterModal from "../../components/SelectPrinterModal";
import { UserContext } from "../../context/userContext";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { posthog } from "../../src/posthog";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { BleManager } from "react-native-ble-plx";
import ExpoZebraPrinter from "../../modules/expo-zebra-printer";
import { useTranslation } from "react-i18next";
import CatalogTemplateModal from "../../components/CatalogTemplateModal";
import { useGetCatalogTemplatesQuery } from "../../generated/graphql";
import generateCatalogPdf from "../../lib/generateCatalog";
import { useGetSupplierConfigByIdQuery } from "../../generated/graphql";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    accountContainer: {
      marginBottom: 20,
      marginTop: 20,
    },
    bluetoothContainer: {
      alignItems: "center",
    },
    buttonContainer: {
      alignItems: "center",
      marginTop: 20,
    },
    container: {
      backgroundColor: theme.appColors.backdropNeutral,
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    letterIcon: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropDark,
      borderRadius: 37.5,
      height: 75,
      justifyContent: "center",
      width: 75,
    },
    modal: {
      justifyContent: "flex-end",
      margin: 0,
    },
    profileContainer: {
      alignItems: "center",
      flexDirection: "column",
      justifyContent: "center",
      marginBottom: 30,
      marginTop: 10,
    },
    scrollView: {
      display: "flex",
      flexDirection: "column",
      height: "100%",
      marginHorizontal: 20,
    },
  });
  return styles;
};

const LetterIcon = ({ letter, style }) => {
  return (
    <View style={style}>
      <Text style={{ color: "white", fontSize: 40 }}>{letter}</Text>
    </View>
  );
};

LetterIcon.propTypes = {
  letter: PropTypes.string.isRequired,
  style: PropTypes.object.isRequired,
};

const haveBluetoothPermissions = async () => {
  if (Platform.OS === "android") {
    if (Platform.Version > 30) {
      return await requestMultiple([
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
        PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
      ]).then((statuses) => {
        return (
          statuses[PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION] ===
            RESULTS.GRANTED &&
          statuses[PERMISSIONS.ANDROID.BLUETOOTH_SCAN] === RESULTS.GRANTED &&
          statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT] === RESULTS.GRANTED
        );
      });
    } else {
      return await requestMultiple([
        PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        "android.permission.BLUETOOTH",
      ]).then((statuses) => {
        return (
          statuses[PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION] ===
            RESULTS.GRANTED &&
          statuses["android.permission.BLUETOOTH"] === RESULTS.GRANTED
        );
      });
    }
  } else if (Platform.OS === "ios") {
    return await requestMultiple([PERMISSIONS.IOS.BLUETOOTH]).then(
      (statuses) => {
        return statuses[PERMISSIONS.IOS.BLUETOOTH] === RESULTS.GRANTED;
      }
    );
  }
};

const manager = new BleManager();

export default function SettingsScreen({ navigation }) {
  const auth = getAuth();
  const logoutButtonPressed = async () => {
    posthog.capture("logout_clicked");
    await signOut(auth);
  };

  const globalStyles = useGlobalStyles();

  const theme = useTheme();
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();
  const { user } = useContext(UserContext);
  const supplier = user.suppliers.length > 0 ? user.suppliers[0] : {};
  const [bluetoothPermissionsLoading, setBluetoothPermissionsLoading] =
    useState(true);
  const [bluetoothPermissions, setBluetoothPermissions] = useState(false);
  const [bluetoothStatusLoading, setBluetoothStatusLoading] = useState(true);
  const [bluetoothStatus, setBluetoothStatus] = useState(false);
  const [bluetoothButtonClicked, setBluetoothButtonClicked] = useState(false);
  const [bluetoothDevices, setBluetoothDevices] = useState([]);
  const [bluetoothDevicesLoading, setBluetoothDevicesLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPrinter, setSelectedPrinter] = useState(null);
  const [supplierNameTapCount, setSupplierNameTapCount] = useState(0);
  const [catalogModalVisible, setCatalogModalVisible] = useState(false);

  const config = user.suppliers[0].config;

  // Fetch catalog templates
  const {
    data: catalogData,
    loading: catalogLoading,
    refetch: refetchCatalogTemplates,
  } = useGetCatalogTemplatesQuery({
    variables: {
      getCatalogTemplatesInput: {
        supplier_id: supplier.id,
      },
    },
    skip: !supplier.id || !config?.enable_share_catalog,
    fetchPolicy: "cache-and-network", // Always check for updates
  });

  const catalogTemplates = catalogData?.getCatalogTemplates || [];

  // Refetch templates when modal opens
  const handleOpenCatalogModal = () => {
    setCatalogModalVisible(true);
    if (supplier.id) {
      refetchCatalogTemplates();
    }
  };

  const handleShareCatalog = async (template, showPrice) => {
    try {
      await generateCatalogPdf({
        supplierId: supplier.id,
        config: template.config,
        templateId: template.id,
        showPrice,
        userId: user.id,
      });
    } catch (error) {
      console.error("Error sharing catalog:", error);
      throw error;
    }
  };

  const { data } = useGetSupplierConfigByIdQuery({
    variables: { supplierId: supplier.id },
    skip: !supplier.id, // Don't run the query if there's no supplier ID
  });
  const showGoalsFeature =
    data?.supplierConfig?.show_goals_tracking_feature || false;

  const checkBluetoothPermissions = () => {
    // if (posthog.isFeatureEnabled("bluetooth_print")) {
    setBluetoothPermissions(false);
    setBluetoothPermissionsLoading(true);
    haveBluetoothPermissions().then((result) => {
      setBluetoothPermissions(result);
      setBluetoothPermissionsLoading(false);
    });
    // }
  };

  const checkBluetoothStatus = async () => {
    if (!bluetoothPermissionsLoading && bluetoothPermissions) {
      setBluetoothStatus(false);
      setBluetoothStatusLoading(true);
      posthog.capture("bluetooth_permissions_granted");
      const bluetoothEnabled =
        await RNZebraBluetoothPrinter.isEnabledBluetooth();
      setBluetoothStatus(bluetoothEnabled);
      setBluetoothStatusLoading(false);
    }
  };

  useEffect(() => {
    if (user.driver) {
      checkBluetoothPermissions();
    }
  }, []);

  useEffect(() => {
    checkBluetoothStatus();
  }, [bluetoothPermissions, bluetoothPermissionsLoading]);

  useEffect(() => {
    const fetchSelectedPrinterFromStorage = async () => {
      const selectedPrinter = JSON.parse(
        await AsyncStorage.getItem("selected_printer")
      );
      setSelectedPrinter(selectedPrinter);
    };
    fetchSelectedPrinterFromStorage();
  }, []);

  const fetchBluetoothDevices = async () => {
    posthog.capture("bluetooth_print_clicked", {
      bluetoothPermissionsAllowed: bluetoothPermissions,
      bluetoothEnabled: bluetoothStatus,
    });

    if (bluetoothPermissions) {
      if (bluetoothStatus) {
        setBluetoothDevicesLoading(true);
        try {
          if (Platform.OS === "android") {
            const devicesJson = await RNZebraBluetoothPrinter.scanDevices();
            let devices = [];
            if (typeof devicesJson === "string") {
              devices = JSON.parse(devicesJson);
            } else {
              devices = devicesJson;
            }
            if (typeof devices.found === "string") {
              devices = JSON.parse(devices.found);
            } else {
              devices = devices.found;
            }
            try {
              const connectedDevicesToOS = await manager.connectedDevices([
                "38EB4A80-C570-11E3-9507-0002A5D5C51B", // Zebra Service UUID
              ]);
              let connectedDevices = [];
              if (typeof devices.paired === "string") {
                connectedDevices = JSON.parse(devices.paired);
              } else {
                connectedDevices = devices.paired || [];
              }
              connectedDevices = connectedDevices.map((d) => ({
                name: d.name,
                address: d.id,
                connected: true,
              }));
              connectedDevices = connectedDevices.concat(
                connectedDevicesToOS.map((d) => ({
                  name: d.name,
                  address: d.id,
                  connected: true,
                }))
              );
              devices = devices.concat(connectedDevices);
            } catch (e) {
              console.error("Could not parse paired devices:", e);
            }
            console.log("Bluetooth devices:", devices);
            setBluetoothDevices(devices);
            setBluetoothDevicesLoading(false);
            setModalVisible(true);
          } else if (Platform.OS === "ios") {
            // const devices = await ExpoZebraPrinter.getDiscoveredDevices();
            const devices =
              await ExpoZebraPrinter.getDiscoveredExternalAccessories();
            console.log("External accessory devices:", devices);
            setBluetoothDevices(
              devices.map((d) => ({
                address: d.id,
                name: d.name,
                connected: d.connected ?? false,
              }))
            );
            setBluetoothDevicesLoading(false);
            setModalVisible(true);
          }
        } catch (err) {
          console.log(err);
          console.log("error");
        }
      } else if (Platform.OS === "android") {
        RNZebraBluetoothPrinter.enableBluetooth();
        await checkBluetoothStatus();
      } else if (Platform.OS === "ios") {
        await checkBluetoothStatus();
      }
    } else {
      if (!bluetoothButtonClicked) await Linking.openSettings();
      checkBluetoothPermissions();
    }
    setBluetoothButtonClicked(!bluetoothButtonClicked);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.profileContainer}>
          <LetterIcon letter={user.user_name[0]} style={styles.letterIcon} />
          <Text
            style={[
              globalStyles.headlineLarge,
              globalStyles.text,
              { marginTop: 10 },
            ]}
          >
            {user.name}
          </Text>
        </View>
        <View style={styles.accountContainer}>
          <View
            style={{ justifyContent: "space-between", flexDirection: "row" }}
          >
            <Text
              style={[
                globalStyles.headlineLarge,
                globalStyles.text,
                {
                  marginBottom: 20,
                  textDecorationLine: "underline",
                },
              ]}
            >
              {t("settings.contact_us")}
            </Text>
            <Image
              source={{ uri: supplier.logo }}
              style={{ height: 25, width: 150 }}
            />
          </View>

          <View
            style={{
              justifyContent: "space-between",
              flexDirection: "row",
              marginBottom: 15,
            }}
          >
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {t("user.name")}
            </Text>
            <TouchableOpacity
              onPress={() => {
                const newCount = supplierNameTapCount + 1;
                setSupplierNameTapCount(newCount);

                if (newCount === 3) {
                  // Reset the counter
                  setSupplierNameTapCount(0);
                  // Navigate to debug screen
                  navigation.navigate("Debug");
                  // Log debug screen accessed event
                  posthog.capture("debug_screen_accessed");
                }
              }}
            >
              <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
                {supplier.name ? supplier.name : "N/A"}
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              justifyContent: "space-between",
              flexDirection: "row",
              marginBottom: 15,
            }}
          >
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {t("user.phone_number")}
            </Text>
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {supplier.phone_number ? supplier.phone_number : "N/A"}
            </Text>
          </View>
          <View
            style={{
              justifyContent: "space-between",
              flexDirection: "row",
            }}
          >
            <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
              {t("user.address")}
            </Text>
            <Text
              style={[
                globalStyles.headlineMedium,
                globalStyles.text,
                { maxWidth: 170 },
              ]}
              numberOfLines={3}
            >
              {supplier.address ? supplier.address : "N/A"}
            </Text>
          </View>
        </View>

        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "row",
            marginBottom: 15,
          }}
        >
          <Text
            style={[
              globalStyles.headlineLarge,
              globalStyles.text,
              { paddingVertical: 5, marginRight: 5 },
            ]}
          >
            {t("settings.language") + ":"}
          </Text>
          <ButtonToggle
            options={["English", "Español"]}
            selectedOption={i18n.language === "en" ? "English" : "Español"}
            onSelect={(option) => {
              const lang = option === "English" ? "en" : "es";
              i18n.changeLanguage(lang);
            }}
          />
        </View>

        {config?.enable_share_catalog ? (
          <View style={styles.buttonContainer}>
            <View
              style={{
                justifyContent: "center",
                flexDirection: "row",
                marginBottom: 20,
              }}
            >
              <Button
                variant="light"
                size="lg"
                onPress={handleOpenCatalogModal}
              >
                <Text>{t("settings.share_catalog")}</Text>
              </Button>
            </View>
          </View>
        ) : null}
        {user.driver ? (
          <View style={styles.bluetoothContainer}>
            <View style={{ justifyContent: "center", flexDirection: "row" }}>
              <Button
                variant="light"
                size="lg"
                loading={bluetoothDevicesLoading}
                onPress={async () => {
                  fetchBluetoothDevices();
                }}
                disabled={bluetoothPermissionsLoading}
              >
                <Text>
                  {bluetoothPermissionsLoading
                    ? t("bluetooth.bluetooth_loading")
                    : bluetoothPermissions
                    ? bluetoothStatusLoading
                      ? t("bluetooth.checking_bluetooth_status")
                      : bluetoothStatus
                      ? t("bluetooth.scan_and_select_printer")
                      : Platform.OS === "android"
                      ? t("bluetooth.enable_bluetooth")
                      : t("bluetooth.enable_bluetooth_in_settings")
                    : t("bluetooth.grant_bluetooth_permissions")}
                </Text>
              </Button>
            </View>
            {selectedPrinter ? (
              <Text style={globalStyles.text}>
                (Selected:{" "}
                {(selectedPrinter.name
                  ? selectedPrinter.name
                  : selectedPrinter.address
                ).replaceAll(":", ":\u{200B}")}
                )
              </Text>
            ) : null}
          </View>
        ) : null}

        {showGoalsFeature ? (
          <View style={styles.buttonContainer}>
            <View style={{ justifyContent: "center", flexDirection: "row" }}>
              <Button
                variant="light"
                size="lg"
                onPress={() => {
                  posthog.capture("switch_user_clicked");
                  navigation.navigate("Goals");
                }}
              >
                <Text>{t("settings.see_goals")}</Text>
              </Button>
            </View>
          </View>
        ) : (
          <></>
        )}

        {user.driver ? (
          <View style={styles.buttonContainer}>
            <Button
              variant="primary"
              size="lg"
              onPress={() => {
                posthog.capture("sign_up_store_clicked");
                navigation.navigate("SignUpStore");
              }}
            >
              <Text>{t("settings.sign_up_store")}</Text>
            </Button>
          </View>
        ) : null}

        {user.driver ? (
          <View style={styles.buttonContainer}>
            <View style={{ justifyContent: "center", flexDirection: "row" }}>
              <Button
                variant="light"
                size="lg"
                onPress={() => {
                  posthog.capture("switch_user_clicked");
                  navigation.navigate("Users");
                }}
              >
                <Text>{t("settings.switch_user")}</Text>
              </Button>
            </View>
          </View>
        ) : null}
        <View
          style={{
            flexGrow: 1,
            backgroundColor: "blue",
          }}
        ></View>
      </ScrollView>
      <Modal
        animationIn="slideInUp"
        isVisible={modalVisible}
        hasBackdrop
        onBackdropPress={() => setModalVisible(false)}
        onRequestClose={() => setModalVisible(!modalVisible)}
        style={styles.modal}
      >
        <SelectPrinterModal
          devices={bluetoothDevices || []}
          onClose={async (device) => {
            if (device) {
              console.log(device);
              console.log("Connecting to accessory");
              await ExpoZebraPrinter.connectAccessory(device.address, true);
              console.log("Connected!");
              await AsyncStorage.setItem(
                "selected_printer",
                JSON.stringify(device)
              );
              setSelectedPrinter(device);
            }
            setModalVisible(false);
          }}
        />
      </Modal>

      <Modal
        animationIn="slideInUp"
        isVisible={catalogModalVisible}
        hasBackdrop
        onBackdropPress={() => setCatalogModalVisible(false)}
        onRequestClose={() => setCatalogModalVisible(false)}
        style={styles.modal}
      >
        <CatalogTemplateModal
          visible={catalogModalVisible}
          onClose={() => setCatalogModalVisible(false)}
          templates={catalogTemplates}
          loading={catalogLoading}
          onShareCatalog={handleShareCatalog}
        />
      </Modal>
      <View style={styles.buttonContainer}>
        <View
          style={{
            justifyContent: "center",
            flexDirection: "row",
            paddingBottom: 20,
          }}
        >
          <Button
            variant="light"
            size="lg"
            onPress={logoutButtonPressed}
            style={{ backgroundColor: theme.appColors.redText }}
          >
            <Text style={{ color: theme.appColors.textNeutral }}>
              {t("settings.log_out")}
            </Text>
          </Button>
        </View>
      </View>
    </View>
  );
}
