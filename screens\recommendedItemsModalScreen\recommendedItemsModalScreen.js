import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  Text,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import PropTypes from "prop-types";

import Modal from "react-native-modal";
import { Ionicons } from "@expo/vector-icons";
import useGlobalStyles from "../../lib/useGlobalStyles";
import RecommendedItemCard from "../../components/ItemCard/RecommendedItemCard";
import { useContext } from "react";
import { UserContext } from "../../context/userContext";

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    flex: 1,
    justifyContent: "center",
  },
  mainContainer: {
    display: "flex",
    flexDirection: "column",
    flexGrow: 1,
    marginBottom: 10,
    paddingHorizontal: 10,
  },
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContainer: {
    maxHeight: "85%",
  },
  safeContainer: {
    backgroundColor: "white",
    borderRadius: 20,
    height: "100%",
  },
  subtitle: {
    marginBottom: 12,
    textAlign: "center",
  },
  title: {
    fontSize: 25,
    marginBottom: 10,
    textAlign: "center",
  },
});

export default function RecommendedItemsModalScreen({ navigation, route }) {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const { recommendations } = route.params;
  const { user } = useContext(UserContext);
  return (
    <View style={styles.container}>
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeContainer}>
          <View
            style={{
              width: "100%",
              marginBottom: 10,
              flexDirection: "row",
              justifyContent: "flex-end",
              paddingRight: 10,
              paddingTop: 10,
            }}
          >
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Ionicons
                name="close"
                size={35}
                color={theme.appColors.primary}
              />
            </TouchableOpacity>
          </View>
          <View style={styles.mainContainer}>
            <Text
              style={[
                globalStyles.headlineLarge,
                globalStyles.text,
                styles.title,
              ]}
            >
              🔥 Weekly Attain Recap 🔥
            </Text>
            <Text
              style={[
                globalStyles.headlineSmall,
                globalStyles.text,
                styles.subtitle,
              ]}
            >
              These products sold best in stores near you
            </Text>
            <FlatList
              data={recommendations}
              numColumns={2}
              renderItem={({ item, index }) => (
                <RecommendedItemCard
                  key={index}
                  recommendation={item}
                  index={index}
                  isRecommended={true}
                  userApproved={user.approved}
                />
              )}
              scrollEnabled={true}
              showsVerticalScrollIndicator={false}
              style={{ flex: 1 }}
            />
          </View>
        </SafeAreaView>
      </View>
    </View>
  );
}
