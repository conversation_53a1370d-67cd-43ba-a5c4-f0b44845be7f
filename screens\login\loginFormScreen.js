import { useState, useContext } from "react";
import {
  StyleSheet,
  View,
  StatusBar,
  TouchableOpacity,
  Image,
  TextInput,
  KeyboardAvoidingView,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";
import { useHeaderHeight } from "@react-navigation/elements";

import Text from "../../components/Text";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { UserContext } from "../../context/userContext";
import { getAuth, signInWithEmailAndPassword } from "firebase/auth";
import Button from "../../components/Button";
import { posthog } from "../../src/posthog";
const createStyles = (theme) => {
  const styles = StyleSheet.create({
    boldHeaderText: {
      fontSize: 50,
      fontWeight: "600",
      marginBottom: 50,
    },
    boldMainText: {
      fontSize: 20,
      fontWeight: "600",
    },
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    credentialsContainer: {
      alignItems: "center",
      flexDirection: "column",
      height: "75%",
    },
    credentialsInput: {
      borderRadius: 10,
      borderStyle: "solid",
      borderWidth: 1,
      height: 45,
      padding: 10,
      width: "80%",
    },
    logoImage: {
      height: 70,
      width: 80,
    },
    safeContainer: {
      flexDirection: "column",
      flex: 1,
      justifyContent: "center",
      marginHorizontal: 0,
    },
    signInButton: {
      alignItems: "center",
      backgroundColor: theme.appColors.backdropLight,
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      width: "80%",
    },
    signUpButton: {
      alignItems: "center",
      backgroundColor: "white",
      borderRadius: 10,
      height: 40,
      justifyContent: "center",
      width: "80%",
    },

    signUpContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
    },
    signUpLink: {
      textDecorationLine: "underline",
    },
  });
  return styles;
};

export default function LoginFormScreen({ navigation }) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const headerHeight = useHeaderHeight();
  const globalStyles = useGlobalStyles();
  const { setIsLoggedIn } = useContext(UserContext);
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
  });

  const onChangeText = (type, text) => {
    const temp = { ...credentials };
    temp[type] = text;
    setCredentials(temp);
  };
  const auth = getAuth();
  const [loginError, setLoginError] = useState(null);
  const signInButtonPressed = async () => {
    const email = credentials.username.includes("@")
      ? credentials.username
      : credentials.username + "@joinattain.com";
    const password = credentials.password;
    signInWithEmailAndPassword(auth, email, password)
      .then(() => {
        posthog.capture("login");
        setIsLoggedIn(true);
      })
      .catch((error) => {
        const errorCode = error.code;
        var errorMessage = error.message;
        if (errorCode == "auth/too-many-requests") {
          errorMessage = "Too Many Requests, Wait A Minute To Try Again";
        }
        if (errorCode == "auth/wrong-password") {
          errorMessage = "Wrong Password";
        }

        if (errorCode == "auth/user-not-found") {
          errorMessage = "Wrong Username";
        }

        setLoginError(errorMessage);
        posthog.capture("login_error", { error: errorMessage });
        setTimeout(() => setLoginError(null), 3000);
      });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeContainer}>
        {loginError && (
          <View
            style={{
              display: "flex",
              justifyContent: "center",
              paddingHorizontal: 35,
              alignItems: "center",
              flexDirection: "row",
              position: "absolute",
              top: 10,
              width: "100%",
              height: 60,
              backgroundColor: theme.appColors.backdropDark,
              borderRadius: 0,
            }}
          >
            <View
              style={{
                display: "flex",
                justifyContent: "center",
                flexDirection: "column",
              }}
            >
              <Text style={styles.boldSecondaryText}>{loginError}</Text>
            </View>
          </View>
        )}
        <KeyboardAvoidingView
          behavior="padding"
          keyboardVerticalOffset={headerHeight + 50}
        >
          <View style={styles.credentialsContainer}>
            <Image
              style={styles.logoImage}
              source={require("../../assets/logo.png")}
            />
            <Text style={styles.boldHeaderText}>Attain</Text>

            <TextInput
              style={[styles.credentialsInput, { marginBottom: 10 }]}
              placeholderTextColor="gray"
              placeholder="username"
              onChangeText={(text) => onChangeText("username", text)}
              autoCapitalize="none"
              value={credentials.username}
              autoCorrect={false}
              textContentType="username"
            ></TextInput>
            <TextInput
              style={[styles.credentialsInput, { marginBottom: 10 }]}
              placeholderTextColor="gray"
              placeholder="password"
              onChangeText={(text) => onChangeText("password", text)}
              autoCapitalize="none"
              value={credentials.password}
              secureTextEntry={true}
              autoCorrect={false}
              textContentType="password"
            ></TextInput>

            <Button
              style={{ marginTop: 20 }}
              variant="dark"
              size="lg"
              onPress={signInButtonPressed}
            >
              <Text>Sign In</Text>
            </Button>
          </View>
        </KeyboardAvoidingView>
      </View>
    </SafeAreaView>
  );
}
