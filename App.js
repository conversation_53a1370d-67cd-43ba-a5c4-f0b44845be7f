import { Apollo<PERSON>lient, InMemoryCache } from "@apollo/client";
import { ApolloProvider } from "@apollo/react-hooks";
import { Lato_400Regular, useFonts } from "@expo-google-fonts/lato";
import { Sen_400Regular, Sen_700Bold } from "@expo-google-fonts/sen";
import ReactNativeAsyncStorage from "@react-native-async-storage/async-storage";
import {
  NavigationContainer,
  useNavigationContainerRef,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { HttpLink } from "apollo-link-http";
import * as Updates from "expo-updates";
import { initializeApp } from "firebase/app";
import {
  getReactNativePersistence,
  initializeAuth,
  onAuthStateChanged,
} from "firebase/auth";
import { doc, getDoc, initializeFirestore } from "firebase/firestore";
import { PostHogProvider } from "posthog-react-native";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, LogBox, Text } from "react-native";
import { enableScreens } from "react-native-screens";
// import { DataCaptureContext } from "scandit-react-native-datacapture-core";
import LoadingErrorStatus from "./components/LoadingErrorStatus";
import { UserContext } from "./context/userContext";
import { useGetCartsQuery, useGetUsersQuery } from "./generated/graphql";
import { useGetSupplierConfig } from "./src/hooks/useGetSupplierConfig";
import HomeTabs from "./screens/homeTabs/homeTabs.js";
import LoginFormScreen from "./screens/login/loginFormScreen.js";
import StartScreen from "./screens/login/startScreen.js";
import OrderSubmittedScreen from "./screens/orderSubmittedScreen/orderSubmittedScreen.js";
import OrderSubmittedScreen2 from "./screens/orderSubmittedScreen/orderSubmittedScreen2";
import ScannerScreen from "./screens/scannerScreen/scannerStack";
import SignupScreen from "./screens/signupScreen/signupScreen.js";
import { posthog } from "./src/posthog.js";
import {
  cgSnacksTheme,
  theme as defaultTheme,
  empireSnacksTheme,
} from "./theme.js";
// Import i18n configuration
import "./src/i18n";
import { useTranslation } from "react-i18next";
import { useUpdateEmployeeMutation } from "./generated/graphql";
import { FavoritesProvider } from "./context/favoritesContext";
import GoalsScreen from "./screens/goalsScreen/goalsScreen";
import SignUpStoreScreen from "./screens/signUpStoreScreen/signUpStoreScreen";

LogBox.ignoreLogs(["new NativeEventEmitter"]); // Ignore log notification by message
LogBox.ignoreAllLogs();
enableScreens();

const firebaseConfig = {
  apiKey: "AIzaSyDazkUMxMUnWRpp19dAH31_TXJ3xrQL3RE",
  authDomain: "attain-23279.firebaseapp.com",
  projectId: "attain-23279",
  storageBucket: "attain-23279.appspot.com",
  messagingSenderId: "556882473265",
  appId: "1:556882473265:web:a92cd9a5294dc755f38010",
  measurementId: "G-H6QGK63XJL",
};

const app = initializeApp(firebaseConfig);

const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage),
});

const db = initializeFirestore(app, {
  experimentalForceLongPolling: true,
  useFetchStreams: false,
});

// const { manifest } = Constants;

// const uri = `http://${manifest.debuggerHost.split(':').shift()}:4000`;

async function onFetchUpdateAsync() {
  try {
    const update = await Updates.checkForUpdateAsync();

    if (update.isAvailable) {
      await Updates.fetchUpdateAsync();
      await Updates.reloadAsync();
      // Alert.alert(
      //   "New App Update",
      //   "A new update is available! Would you like to download it now?",
      //   [
      //     {
      //       text: "Yes",
      //       onPress: async () => {
      //         await Updates.fetchUpdateAsync();
      //         await Updates.reloadAsync();
      //       },
      //     },
      //     {
      //       text: "Later",
      //       onPress: () => {
      //         // Do nothing
      //       },
      //     },
      //   ]
      // );
    }
  } catch (error) {
    console.warn(`Error fetching latest Expo update: ${error}`);
  }
}

const demoUserIds = ["1586", "1651"];

function BootstrapApollo({ children }) {
  const [client, setClient] = useState(null);
  const [userId, setUserId] = useState(null);
  useEffect(() => {
    onAuthStateChanged(auth, async (user) => {
      if (user) {
        const querySnapshot = await getDoc(doc(db, "users", user.uid));
        setUserId(querySnapshot.data().userId);
      } else {
        setUserId(null);
      }
    });
  }, []);

  useEffect(() => {
    if (userId !== undefined) {
      const url = demoUserIds.includes(userId)
        ? "https://attain-staging-8f07d078a2fc.herokuapp.com"
        : "https://attain-server.herokuapp.com";

      console.log("Connected to:", url);

      const newClient = new ApolloClient({
        link: new HttpLink({ uri: url }),
        cache: new InMemoryCache({ addTypename: false }),
      });

      setClient(newClient);
    }
  }, [userId]);

  if (!client) {
    return null;
  }

  // Now that we have the client, wrap children
  return <ApolloProvider client={client}>{children}</ApolloProvider>;
}

export default function App() {
  let [fontsLoaded] = useFonts({
    Lato_400Regular,
    Sen_400Regular,
    Sen_700Bold,
  });
  if (!fontsLoaded) {
    return null;
  }

  onFetchUpdateAsync();

  return (
    <BootstrapApollo>
      <AppComponent></AppComponent>
    </BootstrapApollo>
  );
}

function AppComponent() {
  const [isLoggedIn, setIsLoggedIn] = useState(null);
  const [theme, setTheme] = useState(defaultTheme);
  const { t } = useTranslation();
  const [showRecommendationsModal, setShowRecommendationsModal] =
    useState(false);
  // const licenseKey =
  //   "AcjSKxpUOcT/Qs9bMD3LefEY0SQUD2S2S2MoIkt0i4DHaW4U20zYEAh9xo1YS0OXFm7526N0kOo0W/j3Cn9syeNU+MFcLotPQ1QZwM9j0GagUoHtCxCCbmkjCThaHioWZA+EyFxTu/O8lKKLfRl3YOj5wLt5h4jLGfSIOIP6DyG+w/G0In3x8Bc8h3LNfU/5UmLntC87zb1o8XRzlNpiF1uiBp/pGbC+Jltb0GyC9N2iurQQjtEW1/GWl6Al56n5Gy8I1PKrZAJigt75KWkCw091deTSP/ZZN7GY6UplR707cH0C9UxU7gUgQ+t62LUy07XP+x3jZ0ViGg3297X0YAkboX2NYDGuOz9/NfV6wTx0KB5BE4gqEYExcFUeEM2zHUbbL55GODKCpWPbh2ACYnxwG0kPkMm/pUqoz/AE7E4k6L+swr6vLKJyY4qM7p9QuQemY5z2lPWsKuERj270otJ1zFXMCqUmxlNQtRITHyaIWIeijFFhLL4ZlYUn0QB3iPAuMnn4j9ROKqQsD8XaTuWpueA9Ig/caTe+sBVxZtgPnZgo14jvDhhIUTkMJmDQrEywrTprAUj77dKYALoRoVmyF89irdxwtuuEq0jJOvA7qvhCz53rlZwTGuHHMKR4uKdDIy68lOSQ3YEjnCmRvQdbl9b//rzWnTybypwZ4coQ18X6p8qmBCmqDF8AefEQQouruPilC4Dfi/gFgcnBVncwi5UNBTX+iHNM28MPTg5srjj+XAqi7mkX9qhkT6/j3E3x/bN4AX8UO5ehqGjAfv6SrOC1ZixzSk0lSMUOhUVJImhP/A==";
  // const [dataCaptureContext, setDataCaptureContext] = useState(
  //   DataCaptureContext.forLicenseKey(licenseKey)
  // );
  const [user, setUser] = useState({});
  const [userId, setUserId] = useState(null);
  const [orderRoute, setOrderRoute] = useState("");
  const navigationRef = useNavigationContainerRef();
  const [isNewLogin, setIsNewLogin] = useState(true);
  const [updateEmployee] = useUpdateEmployeeMutation();

  const frontendUrl = "https://app.joinattain.com";
  const backendUrl = demoUserIds.includes(userId)
    ? "https://attain-staging-8f07d078a2fc.herokuapp.com"
    : "https://attain-server.herokuapp.com";

  const {
    loading: getUsersLoading,
    data: getUsersData,
    error: getUsersError,
  } = useGetUsersQuery({
    fetchPolicy: "network-only",
    skip: userId == null,
    variables: {
      getUsersInput: {
        ids: [userId],
        includeCustomPrices: true,
      },
    },
  });

  const { loading: getCartsLoading, data: getCartsData } = useGetCartsQuery({
    fetchPolicy: "cache-and-network",
    skip: userId == null,
    variables: {
      getCartsInput: {
        userId: userId,
      },
    },
    pollInterval: 500,
  });

  const { fetchAndMergeSupplierConfig } = useGetSupplierConfig();

  const setCustomPrices = (customPrices) => {
    setUser({ ...user, custom_prices: customPrices });
  };

  const setShowPrices = (showPrices) => {
    setUser((currentUser) => ({ ...currentUser, show_prices: showPrices }));
  };

  const setIsUserAccountSelectedByDriver = (isUserAccountSelectedByDriver) => {
    setUser((currentUser) => ({
      ...currentUser,
      is_user_account_selected_by_driver: isUserAccountSelectedByDriver,
    }));
  };

  useEffect(() => {
    onAuthStateChanged(auth, async (user) => {
      if (user) {
        const querySnapshot = await getDoc(doc(db, "users", user.uid));
        let userId, employeeId;
        const userData = querySnapshot.data();
        if (userData && "userId" in userData) {
          userId = userData["userId"];
        }
        if (userData && "employeeId" in userData) {
          employeeId = userData["employeeId"];
        }
        if (employeeId) {
          posthog.identify(employeeId, {
            username: user.email.split("@")[0], // optional: set additional user properties
          });
        } else {
          posthog.identify(userId, {
            username: user.email.split("@")[0], // optional: set additional user properties
          });
        }

        if (employeeId) {
          try {
            await updateEmployee({
              variables: {
                input: {
                  supplierId: userData["supplierId"],
                  id: employeeId,
                  lastLogin: new Date(),
                },
              },
            });
          } catch (error) {
            console.log(error);
          }
        }

        // TODO: Default to one of the driver accounts
        if (employeeId) {
          if (userData["supplierId"] == "31") {
            setUserId("1640");
          } else if (userData["supplierId"] == "68") {
            setUserId("650");
          }
        } else {
          setUserId(userId);
        }

        setIsLoggedIn(true);
        setIsNewLogin(true);
      } else {
        setIsLoggedIn(false);
        setUserId(null);
      }
    });
  }, [auth.currentUser]);

  useEffect(() => {
    const handleUserLoad = async () => {
      if (
        !getUsersLoading &&
        getUsersData &&
        !getCartsLoading &&
        getCartsData
      ) {
        const userExists = user.id ? true : false;

        let tempUser = {
          ...getUsersData.users[0],
          cart: getCartsData.carts[0],
          is_user_account: !getUsersData.users[0].driver,
          doesnt_use_uom:
            getUsersData.users[0].suppliers.filter(
              (supplier) => supplier.id === "31"
            ).length > 0
              ? true
              : false,
          show_prices: true,
          is_user_account_selected_by_driver:
            user.is_user_account_selected_by_driver,
        };

        if (userExists) {
          tempUser["driver"] = user["driver"];
        }

        setUser(tempUser);

        // Fetch and merge supplier config
        const updatedUser = await fetchAndMergeSupplierConfig(tempUser);
        if (updatedUser !== tempUser) {
          setUser(updatedUser);
        }

        if (isNewLogin) {
          setIsNewLogin(false);
          // If the user is driver or the previous account was already selected by the driver
          if (
            !tempUser.is_user_account ||
            tempUser.is_user_account_selected_by_driver
          ) {
            setIsUserAccountSelectedByDriver(true);
          } else {
            setIsUserAccountSelectedByDriver(false);
          }
        }

        if (
          getUsersData.users[0].suppliers.filter(
            (supplier) => supplier.id === "47"
          ).length > 0
        ) {
          setTheme({ ...defaultTheme, ...empireSnacksTheme });
        } else if (
          getUsersData.users[0].suppliers.filter(
            (supplier) => supplier.id === "31"
          ).length > 0
        ) {
          setTheme({ ...defaultTheme, ...cgSnacksTheme });
          setShowPrices(true);
        }
      }
    };

    handleUserLoad();
  }, [
    getUsersLoading,
    getUsersData,
    getCartsData,
    getCartsLoading,
    fetchAndMergeSupplierConfig,
  ]);

  if (isLoggedIn == null) {
    return <Text>Loading</Text>;
  }

  if ((isLoggedIn && (!user || !user.id)) || (getUsersLoading && !getUsersData))
    return (
      <LoadingErrorStatus
        message={t("common.loading")}
        errorStatus={false}
        theme={theme}
      />
    );
  if (getUsersError)
    return (
      <LoadingErrorStatus
        message="Error loading application"
        errorStatus={true}
        theme={theme}
      />
    );

  const AppStack = createStackNavigator();
  return (
    <UserContext.Provider
      value={{
        isLoggedIn,
        setIsLoggedIn,
        user,
        frontendUrl,
        backendUrl,
        orderRoute,
        showRecommendationsModal,
        setShowRecommendationsModal,
        setTheme,
        setUserId,
        setCustomPrices,
        setShowPrices,
        setOrderRoute,
      }}
    >
      <FavoritesProvider>
        <NavigationContainer ref={navigationRef} theme={theme}>
          <PostHogProvider
            // apiKey="phc_ayWKtrDDM8pXNlJrKCAaKFS0GG8aGSMSrrJcWykplEO"
            // options={{
            //   host: "https://app.posthog.com",
            // }}
            client={posthog}
            autocapture={{
              captureTouches: true, // If you don't want to capture touch events set this to false
              captureLifecycleEvents: true, // If you don't want to capture the Lifecycle Events (e.g. Application Opened) set this to false
              captureScreens: true, // If you don't want to capture screen events set this to false
              ignoreLabels: [], // Any labels here will be ignored from the stack in touch events
              customLabelProp: "ph-label",
              noCaptureProp: "ph-no-capture",
              navigation: {
                // By default only the Screen name is tracked but it is possible to track the
                // params or modify the name by intercepting theautocapture like so
                // routeToName: (name, params) => {
                //     if (params.id) return `${name}/${params.id}`
                //     return name
                // },
                // routeToParams: (name, params) => {
                //   if (name === "SensitiveScreen") return undefined;
                //   return params;
                // },
                // (Optional) Set the name based on the route. Defaults to the route name.
                routeToName: (name, properties) => name,
                // (Optional) Tracks all passProps as properties. Defaults to undefined
                routeToProperties: (name, properties) => properties,
              },
            }}
          >
            <AppStack.Navigator>
              {isLoggedIn && user ? (
                <AppStack.Screen
                  name="Attain"
                  component={HomeTabs}
                  options={{ headerShown: false }}
                />
              ) : (
                <>
                  <AppStack.Screen
                    name="Start"
                    component={StartScreen}
                    options={{ headerShown: false }}
                  ></AppStack.Screen>
                  <AppStack.Screen
                    name="Signup"
                    component={SignupScreen}
                    options={{ headerShown: true }}
                  />
                  <AppStack.Screen name="Login" component={LoginFormScreen} />
                </>
              )}
              <AppStack.Screen
                name="OrderSubmittedScreen"
                component={OrderSubmittedScreen}
                options={{ headerShown: false, presentation: "modal" }}
              />
              <AppStack.Screen
                name="OrderSubmittedScreen2"
                component={OrderSubmittedScreen2}
                options={{ headerShown: false, presentation: "modal" }}
              />
              <AppStack.Screen
                name="SupplierScanner"
                component={ScannerScreen}
                options={({ route }) => ({
                  headerShown: false,
                })}
              />
              <AppStack.Screen
                name="Scan"
                component={ScannerScreen}
                options={({ route }) => ({
                  headerShown: false,
                })}
              />
              <AppStack.Screen
                name="Goals"
                component={GoalsScreen}
                options={{ title: "My Goals" }}
              />
              <AppStack.Screen
                name="SignUpStore"
                component={SignUpStoreScreen}
                options={{ title: "Sign Up New Store" }}
              />
            </AppStack.Navigator>
          </PostHogProvider>
        </NavigationContainer>
      </FavoritesProvider>
    </UserContext.Provider>
  );
}
