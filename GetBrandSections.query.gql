query GetBrandSections($getSectionsInput: GetSectionsInput) {
  brandSections(getSectionsInput: $getSectionsInput) {
    itemsPreview {
      id
      image
      supplier_code
      name
      price
      discounted_price
      nacs_category
      unit_size
      qoh
      upc1
      supplier
      last_ordered_date
      avg_cases_per_week
      oos
      created_at
      metadata
      moq
      uoms {
        id
        name
        supplier_id
        uom_id
        quantity
        item_id
        price
        upc
        archived
      }
    }

    id
    logo
    name
    spotlight_image
  }
}
