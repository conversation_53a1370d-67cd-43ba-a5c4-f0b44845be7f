import { useCallback } from "react";
import { useGetSupplierConfigLazyQuery } from "../../generated/graphql";

interface User {
  suppliers: Array<{
    id: string;
    config?: any;
    [key: string]: any;
  }>;
  [key: string]: any;
}

interface UseGetSupplierConfigReturn {
  fetchAndMergeSupplierConfig: (user: User) => Promise<any>;
}

export const useGetSupplierConfig = (): UseGetSupplierConfigReturn => {
  const [getSupplierConfig] = useGetSupplierConfigLazyQuery();

  const fetchAndMergeSupplierConfig = useCallback(
    async (user: User) => {
      try {
        // Check if user has suppliers
        if (!user.suppliers || user.suppliers.length === 0) {
          console.warn("No suppliers found for user");
          return user;
        }

        const supplierId = user.suppliers[0].id;

        // Fetch supplier config
        const result = await getSupplierConfig({
          variables: { supplierId },
        });

        if (result.data && result.data.supplierConfig) {
          const existingConfig = user.suppliers[0].config || {};
          const mergedConfig = {
            ...existingConfig,
            ...result.data.supplierConfig,
          };

          return {
            ...user,
            suppliers: user.suppliers.map((supplier, index) =>
              index === 0 ? { ...supplier, config: mergedConfig } : supplier
            ),
          };
        }

        return user;
      } catch (error) {
        console.error("Error fetching supplier config:", error);
        return user;
      }
    },
    [getSupplierConfig]
  );

  return { fetchAndMergeSupplierConfig };
};
