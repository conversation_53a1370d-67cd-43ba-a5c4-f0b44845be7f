import { useContext } from "react";
import {
  StyleSheet,
  View,
  StatusBar,
  TouchableOpacity,
  Image,
  FlatList,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import { useTheme } from "@react-navigation/native";

import Text from "../../components/Text";
import { useGetOrderBySupplierQuery } from "../../generated/graphql";

import { UserContext } from "../../context/userContext";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import { getTotal } from "../../lib/getTotal";
import { getPrice } from "../../lib/getPrice";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    bodyText: {
      fontSize: 15,
    },
    boldSecondaryText: {
      fontSize: 15,
      fontWeight: "600",
      marginBottom: 5,
    },
    container: {
      backgroundColor: "white",
      flex: 1,
      paddingTop: StatusBar.currentHeight,
    },
    itemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",

      justifyContent: "space-between",
    },
    itemImage: {
      height: 55,
      width: 55,
    },
    itemInfoContainer: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
    },
    itemPriceContainer: {
      flex: 1,
      flexDirection: "column",
      justifyContent: "center",
    },
    itemTextContainer: {
      marginLeft: 8,
    },
    mutedBodyTextSmall: {
      color: theme.appColors.textDark,
      fontSize: 11,
    },
    orderItemContainer: {
      borderBottomColor: "gray",
      borderBottomWidth: 0.5,
      borderStyle: "solid",
      paddingVertical: 20,
    },
    orderSummaryView: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: 10,
    },
    safeContainer: {
      backgroundColor: "white",
      marginHorizontal: 20,
      marginTop: 20,
    },
  });
  return styles;
};

export default function SupplierItemsScreen({ navigation, route }) {
  const { user } = useContext(UserContext);
  const theme = useTheme();
  const styles = createStyles(theme);

  const { orderStatus } = route.params;
  const {
    loading: getOrderBySupplierLoading,
    data: getOrderBySupplierData,
    error: getOrderBySupplierError,
  } = useGetOrderBySupplierQuery({
    fetchPolicy: "cache-and-network",
    variables: {
      orderId: orderStatus.order_id,
      userId: user.id,
      supplierId: orderStatus.supplier_id,
    },
  });

  if (getOrderBySupplierLoading && !getOrderBySupplierData) {
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;
  }

  if (getOrderBySupplierError) {
    return (
      <LoadingErrorStatus
        message={getOrderBySupplierError.message}
        errorStatus={true}
      />
    );
  }

  const orderItems = getOrderBySupplierData.orderBySupplier.orderItems;
  const orderItemPressed = (orderItem) => {
    navigation.navigate("ItemDetail", {
      id: orderItem.item_id,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeContainer}>
        <FlatList
          ListHeaderComponent={
            <View
              style={{
                borderStyle: "solid",
                borderBottomColor: "black",
                borderBottomWidth: 1,
              }}
            >
              <View style={styles.orderSummaryView}>
                <View>
                  <Text
                    style={[
                      styles.bodyText,
                      { textAlign: "left", marginBottom: 5 },
                    ]}
                  >
                    {new Date(orderStatus.submission_date).toDateString()}
                  </Text>
                  <Text
                    style={[
                      styles.mutedBodyTextSmall,
                      { textAlign: "left", marginBottom: 5 },
                    ]}
                  >
                    Order#: {orderStatus.order_id}
                  </Text>
                </View>
                <View>
                  <Text
                    style={[
                      styles.bodyText,
                      { textAlign: "right", marginBottom: 5 },
                    ]}
                  >
                    ${getTotal(orderItems)}
                  </Text>
                  <Text
                    style={[
                      styles.mutedBodyTextSmall,
                      { textAlign: "right", marginBottom: 5 },
                    ]}
                  >
                    {orderItems.length} Items
                  </Text>
                </View>
              </View>
            </View>
          }
          data={orderItems}
          numColumns={1}
          renderItem={({ item }) => {
            return (
              <TouchableOpacity
                onPress={() => {
                  orderItemPressed(item);
                }}
                key={item.id}
                style={styles.orderItemContainer}
              >
                <Text style={styles.boldSecondaryText}>
                  {item.quantity}x {item.name}
                </Text>
                <View style={styles.itemContainer}>
                  <View style={styles.itemInfoContainer}>
                    <Image
                      style={styles.itemImage}
                      source={{ uri: item.image }}
                    />

                    <View style={styles.itemTextContainer}>
                      <Text style={styles.mutedBodyTextSmall}>
                        UPC: {item.upc1}
                      </Text>

                      <Text style={styles.mutedBodyTextSmall}>
                        Unit Size: {item.unit_size}
                      </Text>

                      <Text style={styles.mutedBodyTextSmall}>
                        Category: {item.nacs_category}
                      </Text>

                      <Text style={styles.mutedBodyTextSmall}>
                        Case Cost: {getPrice(item)}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.itemPriceContainer}>
                    <Text style={{ textAlign: "right" }}>
                      ${parseFloat((item.price * item.quantity).toFixed(2))}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          }}
          keyExtractor={(item) => item.id}
        ></FlatList>
      </View>
    </SafeAreaView>
  );
}
