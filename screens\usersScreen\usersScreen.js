import { useRoute, useTheme } from "@react-navigation/native";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import {
  FlatList,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import SafeAreaView from "../../components/SafeAreaView";
import Text from "../../components/Text";
import {
  useGetRoutesLazyQuery,
  useGetUsersLazyQuery,
  useGetOrdersBySupplierLazyQuery,
  useGetExpectedOrdersLazyQuery,
  useGetOrdersInfoLazyQuery,
} from "../../generated/graphql";
import useGlobalStyles from "../../lib/useGlobalStyles";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Header from "../../components/Header";
import LoadingErrorStatus from "../../components/LoadingErrorStatus";
import { UserContext } from "../../context/userContext";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";

const createStyles = (theme) => {
  const styles = StyleSheet.create({
    list: {
      backgroundColor: theme.appColors.backdropNeutral,
      height: "100%",
    },
    searchUsersInput: {
      backgroundColor: theme.appColors.backdropNeutral,
      borderRadius: 10,
      borderStyle: "solid",
      borderWidth: 1,
      flex: 1,
      height: 35,
      padding: 10,
      width: "100%",
    },
  });
  return styles;
};

// Helper functions to reduce duplication
const hasCustomRouteMatch = (order, routes) => {
  return (
    order.config?.custom_route &&
    routes.includes(order.config.custom_route.toString())
  );
};

const hasCustomDriverMatch = (order, routes, routesData) => {
  return (
    order.config?.custom_driver &&
    routes.some((routeId) => {
      const route = routesData?.find((r) => r.id === routeId);
      return route && route.driver === order.config.custom_driver;
    })
  );
};

const hasRelevantCustomRouting = (order, routes, routesData) => {
  return (
    hasCustomRouteMatch(order, routes) ||
    hasCustomDriverMatch(order, routes, routesData)
  );
};

// override is used if all routes are selected
const filterUsersByRoutes = (
  users,
  routes,
  override = false,
  orders = [],
  routesData = null
) => {
  // Get users that have the route directly assigned to them
  const usersWithDirectRoutes = users
    .filter((user) => user.config?.active !== false)
    .filter((user) => {
      const userRoutes = (user.route_id || "").split(",");
      return (
        override ||
        routes.some((selectedRoute) => userRoutes.indexOf(selectedRoute) !== -1)
      );
    });

  // Get users that have orders with custom routes/drivers matching the selected routes
  const usersWithCustomRouteOrders = orders
    .filter((order) => hasRelevantCustomRouting(order, routes, routesData))
    .map((order) => order.customerDetails.id);

  // Combine both sets of users
  const combinedUserIds = new Set([
    ...usersWithDirectRoutes.map((user) => user.id),
    ...usersWithCustomRouteOrders,
  ]);

  return users
    .filter((user) => combinedUserIds.has(user.id))
    .sort((a, b) => a.name.localeCompare(b.name));
};

const uniqueBy = (array, func) => {
  if (!array || !func) {
    return [];
  }
  var seen = new Set();
  return array.filter((item) => {
    const key = func(item);
    return seen.has(key) ? false : seen.add(key);
  });
};

export default function UsersScreen({ navigation }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const styles = createStyles(theme);
  const globalStyles = useGlobalStyles();
  const { user, setUserId, setOrderRoute } = useContext(UserContext);

  const route = useRoute();
  const { autoSelectStoreId, autoSelectRouteId } = route.params || {};

  const currentDay = dayjs().format("YYYY-MM-DD");
  const previousDay = dayjs(currentDay).subtract(1, "day").format("YYYY-MM-DD");
  const nextDay = dayjs(currentDay).add(1, "day").format("YYYY-MM-DD");

  const [
    loadUsers,
    { loading: getUsersLoading, data: getUsersData, error: getUsersError },
  ] = useGetUsersLazyQuery({
    variables: {
      getUsersInput: {
        supplierId: user.suppliers[0].id,
      },
    },
  });
  const [
    loadRoutes,
    { loading: getRoutesLoading, data: getRoutesData, error: getRoutesError },
  ] = useGetRoutesLazyQuery({
    variables: {
      getRoutesBySupplierInput: {
        supplierId: user.suppliers[0].id,
      },
    },
  });
  const [
    loadOrders,
    { loading: getOrdersLoading, data: getOrdersData, error: getOrdersError },
  ] = useGetOrdersBySupplierLazyQuery({
    fetchPolicy: "network-only",
    variables: {
      getOrdersBySupplierInput: {
        supplierId: user.suppliers[0].id,
        filters: {
          ...(user.suppliers[0].id === "31"
            ? {
                deliveryDateRange: [previousDay, nextDay],
                status: "In Transit",
              }
            : { deliveryDate: currentDay, status: "In Transit" }),
        },
      },
    },
  });

  // Submitted orders don't have a delivery date, so we need to get them separately
  const [loadSubmittedOrders, { data: getSubmittedOrdersData }] =
    useGetOrdersBySupplierLazyQuery({
      fetchPolicy: "network-only",
      variables: {
        getOrdersBySupplierInput: {
          supplierId: user.suppliers[0].id,
          filters: { deliveryDate: currentDay, status: "submitted" },
        },
      },
    });

  const [loadExpectedOrders, { data: getExpectedOrdersData }] =
    useGetExpectedOrdersLazyQuery({
      fetchPolicy: "network-only",
      variables: {
        supplierId: user.suppliers[0].id,
      },
    });

  const [loadUnpaidOrders, { data: getUnpaidOrdersData }] =
    useGetOrdersInfoLazyQuery({
      fetchPolicy: "network-only",
      variables: {
        ordersV2Input: {
          supplierId: user.suppliers[0].id,
          filters: {
            paidStatus: "unpaid",
          },
          pagination: {
            offset: 0,
            limit: 300,
          },
        },
      },
    });

  const [loadPartiallyPaidOrders, { data: getPartiallyPaidOrdersData }] =
    useGetOrdersInfoLazyQuery({
      fetchPolicy: "network-only",
      variables: {
        ordersV2Input: {
          supplierId: user.suppliers[0].id,
          filters: {
            paidStatus: "partial",
          },
          pagination: {
            offset: 0,
            limit: 300,
          },
        },
      },
    });

  // Function to get all orders - moved inside component to access state variables
  const getAllOrders = () => {
    return uniqueBy(
      getOrdersData?.ordersBySupplier?.orders.concat(
        getSubmittedOrdersData?.ordersBySupplier?.orders ?? [],
        user.suppliers[0].id === "68"
          ? getExpectedOrdersData?.expectedOrders.filter(
              (order) => order.customerDetails.config?.service_type === "DSD"
            ) ?? []
          : getPartiallyPaidOrdersData?.ordersV2?.orders.concat(
              getUnpaidOrdersData?.ordersV2?.orders ?? []
            ) ?? []
      ),
      (order) => order.id
    );
  };

  const [users, setUsers] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [usersByRoute, setUsersByRoute] = useState([]);
  const [selectedRoutes, setSelectedRoutes] = useState([]);
  const [usersFilter, setUsersFilter] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (autoSelectStoreId && autoSelectRouteId) {
      setSelectedRoutes([autoSelectRouteId]);
      setUserId(autoSelectStoreId);
      navigation.navigate("Attain");
    }
  }, [autoSelectStoreId, autoSelectRouteId, setUserId, navigation]);

  useEffect(() => {
    setLoading(true);
    loadUsers();
    loadOrders();
    loadRoutes();
    loadSubmittedOrders();
    loadExpectedOrders();
    loadUnpaidOrders();
    loadPartiallyPaidOrders();
    setLoading(false);
  }, []);

  useEffect(() => {
    if (
      getRoutesData &&
      getRoutesData.routesBySupplier &&
      getOrdersData &&
      getOrdersData.ordersBySupplier &&
      getUsersData &&
      getUsersData.users
    ) {
      const driversWithRoutes = {};
      getRoutesData.routesBySupplier.forEach((route) => {
        if (route.driver) {
          if (!driversWithRoutes[route.driver]) {
            driversWithRoutes[route.driver] = [];
          }
          driversWithRoutes[route.driver].push(route.id);
        }
      });

      setDrivers(
        [
          {
            name: "All",
            routes: getRoutesData.routesBySupplier.map((r) => r.id),
          },
        ].concat(
          Object.keys(driversWithRoutes).map((driver) => ({
            name: driver,
            routes: driversWithRoutes[driver],
            openOrders: countOpenOrdersByRoutes(driversWithRoutes[driver]),
            unpaidOrders: countUnpaidOrdersByRoutes(driversWithRoutes[driver]),
          }))
        )
      );
      setRoutes(
        getRoutesData.routesBySupplier
          .map((route) => ({
            ...route,
            openOrders: countOpenOrdersByRoutes([route.id]),
            unpaidOrders: countUnpaidOrdersByRoutes([route.id]),
          }))
          .concat([
            {
              id: "all",
              name: "All",
              day_of_week: "All",
              color: theme.appColors.backdropDark,
            },
          ])
      );
    }
  }, [
    getUsersData,
    getRoutesData,
    getOrdersData,
    getSubmittedOrdersData,
    getExpectedOrdersData,
    getUnpaidOrdersData,
    getPartiallyPaidOrdersData,
  ]);

  useEffect(() => {
    if (getUsersData && getUsersData.users && !selectedRoutes.length) {
      setUsers(
        getUsersData.users.filter((user) => user.config?.active !== false)
      );
    }
  }, [getUsersData]);

  useEffect(() => {
    setLoading(true);
    if (selectedRoutes.length) {
      const allRoutesSelected = selectedRoutes.length === routes.length - 1;
      const allOrders = getAllOrders();

      // Separate orders by custom routing
      const ordersWithCustomRouting = allOrders.filter(
        (order) =>
          (order.config?.custom_route || order.config?.custom_driver) &&
          (order.status === "In Transit" ||
            order.status === "Expected" ||
            (user.suppliers[0].id === "31" && order.status === "submitted")) &&
          (user.suppliers[0].id === "31"
            ? order.delivery_date === currentDay ||
              order.delivery_date === previousDay ||
              order.delivery_date === nextDay
            : order.delivery_date === currentDay)
      );

      const ordersWithoutCustomRouting = allOrders.filter(
        (order) =>
          !order.config?.custom_route &&
          !order.config?.custom_driver &&
          (order.status === "In Transit" ||
            order.status === "Expected" ||
            (user.suppliers[0].id === "31" && order.status === "submitted")) &&
          (user.suppliers[0].id === "31"
            ? order.delivery_date === currentDay ||
              order.delivery_date === previousDay ||
              order.delivery_date === nextDay
            : order.delivery_date === currentDay)
      );

      const routesData = getRoutesData?.routesBySupplier;
      const allUsersByRoute = filterUsersByRoutes(
        getUsersData.users,
        selectedRoutes,
        allRoutesSelected,
        allOrders,
        routesData
      )
        .map((user) => {
          // For each user, determine if they have an open order relevant to the SELECTED routes/driver

          // Check for orders WITHOUT custom routing - only if user directly belongs to selected routes
          const hasStandardOrders = ordersWithoutCustomRouting.some((order) => {
            const userBelongsToSelectedRoutes = (user.route_id || "")
              .split(",")
              .some((r) => selectedRoutes.includes(r));

            return (
              order.customerDetails.id === user.id &&
              userBelongsToSelectedRoutes
            );
          });

          // Check for orders WITH custom routing targeting these specific routes/driver
          const hasCustomRoutedOrders = ordersWithCustomRouting.some(
            (order) =>
              order.customerDetails.id === user.id &&
              hasRelevantCustomRouting(order, selectedRoutes, routesData)
          );

          // Check for unpaid orders
          const hasUnpaidOrder =
            user.suppliers[0].id === "31"
              ? allOrders.some(
                  (order) =>
                    order.customerDetails.id === user.id &&
                    (order.invoice.total - order.invoice.paid !== 0 ||
                      order.invoice.payment_status === "bounced") &&
                    order.status === "Delivered"
                )
              : false;

          return {
            ...user,
            hasOpenOrder: hasStandardOrders || hasCustomRoutedOrders,
            hasUnpaidOrder,
          };
        })
        .sort((a, b) => {
          // First sort by hasOpenOrder, then by hasUnpaidOrder when hasOpenOrder is the same
          if (b.hasOpenOrder !== a.hasOpenOrder) {
            return b.hasOpenOrder - a.hasOpenOrder;
          }
          return b.hasUnpaidOrder - a.hasUnpaidOrder;
        });
      setUsers(allUsersByRoute);
      setUsersByRoute(allUsersByRoute);
    } else {
      setUsersByRoute([]);
      setUsers([]);
    }
    setLoading(false);
  }, [selectedRoutes]);

  const countOpenOrdersByRoutes = (routes) => {
    const allOrders = getAllOrders();

    // Separate orders into those with custom routes/drivers and those without
    const ordersWithCustomRouting = allOrders.filter(
      (order) =>
        (order.config?.custom_route || order.config?.custom_driver) &&
        (order.status === "In Transit" ||
          order.status === "Expected" ||
          (user.suppliers[0].id === "31" && order.status === "submitted")) &&
        (user.suppliers[0].id === "31"
          ? order.delivery_date === currentDay ||
            order.delivery_date === previousDay ||
            order.delivery_date === nextDay
          : order.delivery_date === currentDay)
    );

    const ordersWithoutCustomRouting = allOrders.filter(
      (order) =>
        !order.config?.custom_route &&
        !order.config?.custom_driver &&
        (order.status === "In Transit" ||
          order.status === "Expected" ||
          (user.suppliers[0].id === "31" && order.status === "submitted")) &&
        (user.suppliers[0].id === "31"
          ? order.delivery_date === currentDay ||
            order.delivery_date === previousDay ||
            order.delivery_date === nextDay
          : order.delivery_date === currentDay)
    );

    // Filter users by direct route assignment
    const filteredUsers = filterUsersByRoutes(
      getUsersData.users,
      routes || [],
      false,
      allOrders,
      getRoutesData?.routesBySupplier
    );

    // For the current routes, count:
    // 1. Orders WITHOUT custom routing where the user is assigned to these routes
    const standardOrderCount = uniqueBy(
      ordersWithoutCustomRouting.filter((order) =>
        filteredUsers.some((user) => user.id === order.customerDetails.id)
      ),
      (order) => order.customerDetails.id
    ).length;

    // 2. Orders WITH custom routing that specifically target these routes/drivers
    const customOrderCount = uniqueBy(
      ordersWithCustomRouting.filter((order) =>
        hasRelevantCustomRouting(order, routes, getRoutesData?.routesBySupplier)
      ),
      (order) => order.customerDetails.id
    ).length;

    return standardOrderCount + customOrderCount;
  };

  const countUnpaidOrdersByRoutes = (routes) => {
    if (user.suppliers[0].id !== "31") {
      return 0;
    }

    const allOrders = getAllOrders();

    // Filter users by direct route assignment
    const filteredUsers = filterUsersByRoutes(
      getUsersData.users,
      routes || [],
      false,
      allOrders,
      getRoutesData?.routesBySupplier
    );

    // Count unique users with unpaid orders for these routes - include bounced
    const unpaidOrderCount = uniqueBy(
      allOrders.filter(
        (order) =>
          (order.invoice.total - order.invoice.paid !== 0 ||
            order.invoice.payment_status === "bounced") && // Has unpaid balance
          filteredUsers.some((user) => user.id === order.customerDetails.id) && // User is in the selected routes
          order.status === "Delivered"
      ),
      (order) => order.customerDetails.id
    ).length;

    return unpaidOrderCount;
  };

  const renderUser = useCallback(({ item }) => {
    return (
      <TouchableOpacity
        style={{
          marginHorizontal: 30,
          marginVertical: 10,
          paddingHorizontal: 15,
          paddingVertical: 8,

          borderWidth: 1,
          borderColor: theme.appColors.backdropDark,
          borderRadius: 10,
        }}
        onPress={() => {
          setUserId(item.id);
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <View style={{ flexDirection: "column", width: "80%" }}>
            <Text
              numberOfLines={1}
              style={[globalStyles.text, globalStyles.headlineMedium]}
            >
              {item.name}
            </Text>
            <Text
              numberOfLines={1}
              style={[globalStyles.text, globalStyles.bodyMedium]}
            >
              {item.address}
            </Text>
          </View>
          <View style={{ flexDirection: "row", gap: 2 }}>
            {item.hasUnpaidOrder && (
              <Text
                numberOfLines={1}
                style={[
                  globalStyles.text,
                  globalStyles.bodyMedium,
                  { marginRight: 8 },
                ]}
              >
                <MaterialCommunityIcons
                  name="currency-usd"
                  size={24}
                  color="red"
                />
              </Text>
            )}
            {item.hasOpenOrder ? (
              <Text
                numberOfLines={1}
                style={[globalStyles.text, globalStyles.bodyMedium]}
              >
                <MaterialCommunityIcons
                  name="truck-delivery"
                  size={24}
                  color={theme.appColors.textLight}
                />
              </Text>
            ) : null}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, []);

  const usersList = useMemo(
    () => (
      <FlatList
        data={users}
        numColumns={1}
        renderItem={renderUser}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
        ListHeaderComponent={
          <View
            style={{
              flexDirection: "column",
              alignItems: "center",
              marginVertical: 20,
              paddingHorizontal: 15,
            }}
          >
            <TextInput
              style={styles.searchUsersInput}
              placeholderTextColor="gray"
              multiline
              placeholder={t("search.search_users")}
              onChangeText={(text) => {
                setUsersFilter(text);
                setUsers(
                  (routes.length > 1
                    ? usersByRoute
                    : (getUsersData?.users || []).filter(
                        (user) => user.config?.active !== false
                      )
                  ).filter((user) => {
                    return (
                      user.name.toLowerCase().includes(text.toLowerCase()) ||
                      (user.store_group ?? "")
                        .toLowerCase()
                        .includes(text.toLowerCase())
                    );
                  })
                );
              }}
              value={usersFilter}
              autoCorrect={true}
            />
          </View>
        }
        stickyHeaderIndices={[0]}
        keyExtractor={(user) => user.id}
        style={styles.list}
      />
    ),
    [users]
  );

  const renderRoute = useCallback(
    ({ item }) => {
      return (
        <TouchableOpacity
          style={{
            marginHorizontal: 30,
            marginVertical: 10,
            paddingHorizontal: 15,
            paddingVertical: 8,

            borderWidth: 1,
            borderColor: theme.appColors.backdropDark,
            borderRadius: 10,
          }}
          onPress={() => {
            setLoading(true);
            item.id !== "all"
              ? setSelectedRoutes([item.id])
              : setSelectedRoutes(
                  getRoutesData.routesBySupplier.map((r) => r.id)
                );
            if (user.suppliers[0].id === "31") {
              setOrderRoute(item.id !== "all" ? item.id : "");
            }
          }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <View style={{ flexDirection: "column" }}>
              <Text
                numberOfLines={1}
                style={[globalStyles.text, globalStyles.headlineMedium]}
              >
                {item.name}
              </Text>
              <Text
                numberOfLines={1}
                style={[globalStyles.text, globalStyles.bodyMedium]}
              >
                {item.day_of_week}
              </Text>
            </View>
            <View style={{ flexDirection: "row", gap: 8 }}>
              {item.unpaidOrders > 0 && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    // marginRight: 16,
                    // gap: 8,
                  }}
                >
                  <Text
                    numberOfLines={1}
                    style={[
                      globalStyles.text,
                      globalStyles.headlineMedium,
                      // { marginRight: 8 },
                    ]}
                  >
                    {item.unpaidOrders}
                  </Text>
                  <MaterialCommunityIcons
                    name="currency-usd"
                    size={24}
                    color="red"
                  />
                </View>
              )}
              {item.openOrders > 0 && (
                <View
                  style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
                >
                  <Text
                    numberOfLines={1}
                    style={[globalStyles.text, globalStyles.headlineMedium]}
                  >
                    {item.openOrders}
                  </Text>
                  <MaterialCommunityIcons
                    name="truck-delivery"
                    size={24}
                    color={theme.appColors.textLight}
                  />
                </View>
              )}
            </View>
          </View>
        </TouchableOpacity>
      );
    },
    [getRoutesData, user, setOrderRoute]
  );

  const routesList = useMemo(
    () => (
      <FlatList
        data={routes}
        numColumns={1}
        renderItem={renderRoute}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
        keyExtractor={(route) => route.id}
        style={styles.list}
      />
    ),
    [routes]
  );

  const showDrivers = user.suppliers[0].id === "68";

  const renderDriver = useCallback(({ item }) => {
    return (
      <TouchableOpacity
        style={{
          marginHorizontal: 30,
          marginVertical: 10,
          paddingHorizontal: 15,
          paddingVertical: 8,

          borderWidth: 1,
          borderColor: theme.appColors.backdropDark,
          borderRadius: 10,
        }}
        onPress={() => {
          setLoading(true);
          setSelectedRoutes(item.routes);
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <View style={{ flexDirection: "column" }}>
            <Text
              numberOfLines={1}
              style={[globalStyles.text, globalStyles.headlineMedium]}
            >
              {item.name}
            </Text>
          </View>
          <View style={{ flexDirection: "row" }}>
            {item.unpaidOrders > 0 && (
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginRight: 16,
                }}
              >
                <Text
                  numberOfLines={1}
                  style={[
                    globalStyles.text,
                    globalStyles.headlineMedium,
                    { marginRight: 8 },
                  ]}
                >
                  {item.unpaidOrders}
                </Text>
                <MaterialCommunityIcons
                  name="currency-usd"
                  size={24}
                  color="red"
                />
              </View>
            )}
            {item.openOrders > 0 && (
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
              >
                <Text
                  numberOfLines={1}
                  style={[globalStyles.text, globalStyles.headlineMedium]}
                >
                  {item.openOrders}
                </Text>
                <MaterialCommunityIcons
                  name="truck-delivery"
                  size={24}
                  color={theme.appColors.textLight}
                />
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, []);

  const driversList = useMemo(
    () => (
      <FlatList
        data={drivers}
        numColumns={1}
        renderItem={renderDriver}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
        keyExtractor={(driver) => driver.name}
        style={styles.list}
      />
    ),
    [drivers]
  );

  if (
    (getUsersLoading || getRoutesLoading || getOrdersLoading) &&
    (!getUsersData || !getRoutesData || !getOrdersData)
  )
    return <LoadingErrorStatus message="Loading..." errorStatus={false} />;

  if (getUsersError || getRoutesError || getOrdersError)
    return (
      <LoadingErrorStatus
        message={(getUsersError || getRoutesError || getOrdersError).message}
        errorStatus={true}
      />
    );

  return (
    <SafeAreaView
      style={[
        globalStyles.container,
        { backgroundColor: theme.appColors.backdropNeutral },
      ]}
    >
      <Header
        goBack={
          !selectedRoutes.length
            ? navigation.goBack
            : () => {
                setLoading(true);
                setSelectedRoutes([]);
              }
        }
        title={
          routes.length > 1
            ? !selectedRoutes.length
              ? !showDrivers
                ? t("common.routes")
                : t("common.drivers")
              : `Users for ${
                  selectedRoutes.length === routes.length - 1
                    ? !showDrivers
                      ? t("common.all_routes")
                      : t("common.all_drivers")
                    : !showDrivers
                    ? selectedRoutes
                        .map((sr) => routes.find((r) => r.id === sr).name)
                        .join(", ")
                    : routes.find((r) => r.id === selectedRoutes[0]).driver
                }`
            : t("common.users")
        }
      />
      {getUsersLoading || getRoutesLoading || loading ? (
        <LoadingErrorStatus message="Loading..." errorStatus={false} />
      ) : (
        <View style={{ flex: 1 }}>
          {routes.length > 1
            ? !selectedRoutes.length
              ? !showDrivers
                ? routesList
                : driversList
              : usersList
            : usersList}
        </View>
      )}
    </SafeAreaView>
  );
}
