import { useTheme } from "@react-navigation/native";
import BaseNumericInput from "react-native-numeric-input";
import PropTypes from "prop-types";

import useGlobalStyles from "../lib/useGlobalStyles";
import { StyleSheet, View } from "react-native";

const NumericInput = ({
  onEditQuantity,
  quantity,
  totalHeight = 35,
  totalWidth = 120,
  borderRadius = 9,
}) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();

  const styles = StyleSheet.create({
    // This is or new wrapper view
    wrapper: {
      borderRadius: borderRadius,
      height: totalHeight,
      overflow: "hidden",
      width: totalWidth,
    },
  });

  return (
    <View style={styles.wrapper}>
      <BaseNumericInput
        value={quantity}
        onChange={onEditQuantity}
        initValue={quantity}
        minValue={0}
        borderColor={theme.appColors.primary}
        containerStyle={{ borderRadius: 0 }}
        iconStyle={[globalStyles.labelLarge, globalStyles.textNeutral]}
        inputStyle={[globalStyles.labelLarge, globalStyles.text]}
        leftButtonBackgroundColor={theme.appColors.primary}
        rightButtonBackgroundColor={theme.appColors.primary}
        totalHeight={totalHeight}
        totalWidth={totalWidth}
      />
    </View>
  );
};

NumericInput.propTypes = {
  onEditQuantity: PropTypes.func.isRequired,
  quantity: PropTypes.number.isRequired,
  totalHeight: PropTypes.number,
  totalWidth: PropTypes.number,
  borderRadius: PropTypes.number,
};

export default NumericInput;
