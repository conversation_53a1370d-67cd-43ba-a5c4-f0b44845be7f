import React, { useState } from "react";
import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import { useTheme } from "@react-navigation/native";
import useGlobalStyles from "../lib/useGlobalStyles";

const DayOfWeekSelector = ({ onSelect, selectedDay }) => {
  const theme = useTheme();
  const globalStyles = useGlobalStyles();
  const week = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];
  return (
    <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
      {week.map((day) => {
        return (
          <TouchableOpacity
            key={day}
            style={{
              backgroundColor:
                selectedDay === day ? theme.appColors.accent : "white",
              padding: 5,
              borderRadius: 7,
            }}
            onPress={() => onSelect(day)}
          >
            <Text
              style={[
                globalStyles.headlineMedium,
                selectedDay === day
                  ? globalStyles.textNeutral
                  : globalStyles.textLight,
              ]}
            >
              {day.substring(0, 3)}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default DayOfWeekSelector;
