import { StyleSheet, View, ScrollView, Image } from "react-native";
import Text from "../../../components/Text";
import Button from "../../../components/Button";
import useGlobalStyles from "../../../lib/useGlobalStyles";
import PropTypes from "prop-types";
import { getTotal } from "../../../lib/getTotal";
import { useNavigation } from "@react-navigation/native";
import { posthog } from "../../../src/posthog";
import { getUomQuantity } from "../../../lib/uomHelpers";
import { useContext } from "react";
import { UserContext } from "../../../context/userContext";
import { useTranslation } from "react-i18next";

const styles = StyleSheet.create({
  buttonContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  subCartHeader: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
});

const SubCart = ({ style, subCart }) => {
  const globalStyles = useGlobalStyles();
  const navigation = useNavigation();
  const { t } = useTranslation();
  const { user } = useContext(UserContext);

  // Calculate UOM-adjusted item count
  const getUomAdjustedItemCount = () => {
    if (subCart.supplier === "C&G Snacks") {
      return subCart.cartItems.reduce((acc, cartItem) => {
        const uomQuantity = cartItem?.item_uom_id
          ? getUomQuantity(cartItem, cartItem.item_uom_id)
          : 1;
        const displayQuantity = Number(
          (cartItem.quantity / uomQuantity).toFixed(2)
        );
        return acc + displayQuantity;
      }, 0);
    } else {
      return subCart.cartItems.length;
    }
  };

  return (
    <View style={[globalStyles.card, style]}>
      <View style={styles.subCartHeader}>
        <View>
          <Text style={[globalStyles.headlineMedium, globalStyles.text]}>
            {subCart.supplier}
          </Text>
          <Text style={[globalStyles.bodyMedium, globalStyles.textLight]}>
            {getUomAdjustedItemCount()} items
          </Text>
        </View>
        {user.show_prices ? (
          <View>
            <Text style={[globalStyles.headlineMedium, globalStyles.textLight]}>
              ${getTotal(subCart.cartItems)}
            </Text>
          </View>
        ) : null}
      </View>
      <ScrollView
        style={[globalStyles.scrollViewHorizontal, { marginVertical: 10 }]}
        horizontal={true}
      >
        {subCart.cartItems.map((cartItem, index) => {
          const supplier = user.suppliers.find(
            (supplier) => supplier.name === cartItem.supplier
          );
          return (
            <Image
              key={index}
              style={{ height: 30, width: 30 }}
              source={{
                uri:
                  cartItem.image ||
                  supplier?.config?.defaultItemImage ||
                  "https://fakeimg.pl/100x100?text=no+image",
              }}
            />
          );
        })}
      </ScrollView>
      <View style={styles.buttonContainer}>
        <Button
          variant="light"
          size="lg"
          onPress={() => {
            posthog.capture("continue_checkout", {
              supplier: subCart.supplier,
            });
            navigation.navigate("SubCartScreen", {
              supplier: subCart.supplier,
            });
          }}
        >
          <Text>{t("cart.continue_checkout")}</Text>
        </Button>
      </View>
    </View>
  );
};

SubCart.propTypes = {
  style: PropTypes.object,
  subCart: PropTypes.object,
};

export default SubCart;
